import React, { useEffect, useRef } from 'react';
import { Button, Space, message } from 'antd';
import { PrinterOutlined, CloseOutlined } from '@ant-design/icons';

/**
 * 文书打印组件
 * 提供打印预览和实际打印功能
 */
const DocumentPrint = ({
  documentType,
  data,
  config,
  mode = 'print'  // print | preview
}) => {
  const printRef = useRef(null);

  useEffect(() => {
    if (mode === 'print') {
      // 如果是打印模式，自动触发打印
      setTimeout(() => {
        handlePrint();
      }, 1000);
    }
  }, [mode]);

  // 处理打印
  const handlePrint = () => {
    if (!printRef.current) return;

    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        message.error('无法打开打印窗口，请检查浏览器设置');
        return;
      }

      // 构建打印内容
      const printContent = generatePrintContent();
      
      printWindow.document.write(printContent);
      printWindow.document.close();
      
      setTimeout(() => {
        printWindow.print();
        if (mode === 'print') {
          setTimeout(() => {
            printWindow.close();
          }, 1000);
        }
      }, 500);
    } catch (error) {
      message.error('打印失败：' + error.message);
    }
  };

  // 关闭预览
  const handleClose = () => {
    window.close();
  };

  // 生成打印内容
  const generatePrintContent = () => {
    const { printConfig = {} } = config;
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${config.name || '文书打印'}</title>
          <style>
            ${getPrintStyles(printConfig)}
          </style>
        </head>
        <body>
          <div class="print-container">
            ${printConfig.header ? '<div class="print-header">' + (printConfig.headerText || '') + '</div>' : ''}
            
            <div class="print-watermark">${printConfig.watermark || ''}</div>
            
            <div class="print-content">
              ${generateDocumentContent()}
            </div>
            
            ${printConfig.footer ? '<div class="print-footer">' + (printConfig.footerText || '') + '</div>' : ''}
          </div>
        </body>
      </html>
    `;
  };

  // 获取打印样式
  const getPrintStyles = (config) => {
    return `
      @page {
        size: A4 ${config.layout === 'landscape' ? 'landscape' : 'portrait'};
        margin: 2cm;
      }
      
      body {
        font-family: "SimSun", "宋体", serif;
        font-size: 16px;
        line-height: 1.8;
        color: #000;
        margin: 0;
        padding: 0;
      }
      
      .print-container {
        position: relative;
        min-height: 100vh;
      }
      
      .print-header {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
      }
      
      .print-watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 72px;
        color: rgba(0, 0, 0, 0.1);
        white-space: nowrap;
        z-index: -1;
      }
      
      .print-content {
        position: relative;
        z-index: 1;
      }
      
      .print-footer {
        position: fixed;
        bottom: 1cm;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 12px;
        color: #666;
      }
      
      .field-row {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-start;
      }
      
      .field-label {
        min-width: 120px;
        font-weight: bold;
        flex-shrink: 0;
      }
      
      .field-value {
        flex: 1;
        border-bottom: 1px solid #000;
        min-height: 24px;
        padding: 0 5px;
        word-break: break-all;
      }
      
      .signature-area {
        margin-top: 50px;
        text-align: right;
      }
      
      .signature-line {
        margin-bottom: 10px;
      }
      
      @media print {
        .no-print {
          display: none !important;
        }
      }
    `;
  };

  // 生成文档内容
  const generateDocumentContent = () => {
    const fields = config.fields || [];
    
    return `
      <div class="document-title">${config.name || '司法文书'}</div>
      
      <div class="document-body">
        ${fields.map(field => {
          const value = data[field.key] || '';
          return `
            <div class="field-row">
              <div class="field-label">${field.label}：</div>
              <div class="field-value">${value}</div>
            </div>
          `;
        }).join('')}
      </div>
      
      <div class="signature-area">
        <div class="signature-line">制表人：______________</div>
        <div class="signature-line">审核人：______________</div>
        <div class="signature-line">日期：${new Date().toLocaleDateString()}</div>
      </div>
    `;
  };

  return (
    <div className="document-print">
      <div className="print-actions no-print" style={{ padding: '20px', background: '#f5f5f5' }}>
        <Space>
          <Button
            type="primary"
            icon={<PrinterOutlined />}
            onClick={handlePrint}
            size="large"
          >
            打印
          </Button>
          
          {mode === 'preview' && (
            <Button
              icon={<CloseOutlined />}
              onClick={handleClose}
              size="large"
            >
              关闭
            </Button>
          )}
        </Space>
      </div>

      <div ref={printRef} className="print-preview">
        <div className="print-container">
          <div className="print-header">
            {config.name || '司法文书'}
          </div>
          
          <div className="print-watermark">
            {config.printConfig?.watermark || ''}
          </div>
          
          <div className="print-content">
            <div className="document-body">
              {(config.fields || []).map(field => {
                const value = data[field.key] || '';
                return (
                  <div key={field.key} className="field-row">
                    <div className="field-label">{field.label}：</div>
                    <div className="field-value">{value}</div>
                  </div>
                );
              })}
            </div>
            
            <div className="signature-area">
              <div className="signature-line">制表人：______________</div>
              <div className="signature-line">审核人：______________</div>
              <div className="signature-line">日期：{new Date().toLocaleDateString()}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentPrint;