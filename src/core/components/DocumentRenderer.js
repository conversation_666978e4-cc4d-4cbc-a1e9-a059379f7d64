import React from 'react';
import { useLocation } from 'umi';
import { useDocument } from '@/core/hooks/useDocument';
import DocumentEditor from './DocumentEditor';
import DocumentPrint from './DocumentPrint';
import DocumentViewer from './DocumentViewer';
import { Spin, Alert } from 'antd';

/**
 * 通用文书渲染引擎
 * 根据文书类型和模式自动选择合适的渲染组件
 */
const DocumentRenderer = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  
  const documentType = query.get('type') || 'DEFAULT';
  const documentId = query.get('id');
  const token = query.get('token') || localStorage.getItem('token');
  const mode = query.get('mode') || 'edit';
  const page = query.get('page') || '1';

  // 使用通用文书数据Hook
  const {
    documentData,
    documentConfig,
    loading,
    error,
    updateField,
    saveDocument,
    isDirty
  } = useDocument(documentType, documentId, token);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="正在加载文书数据..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
        />
      </div>
    );
  }

  // 根据模式渲染不同的组件
  const renderContent = () => {
    switch (mode) {
      case 'edit':
        return (
          <DocumentEditor
            documentType={documentType}
            data={documentData}
            config={documentConfig}
            onFieldChange={updateField}
            onSave={saveDocument}
            isDirty={isDirty}
            page={page}
          />
        );
      
      case 'print':
      case 'preview':
        return (
          <DocumentPrint
            documentType={documentType}
            data={documentData}
            config={documentConfig}
            mode={mode}
          />
        );
      
      case 'view':
        return (
          <DocumentViewer
            documentType={documentType}
            data={documentData}
            config={documentConfig}
          />
        );
      
      default:
        return (
          <Alert
            message="参数错误"
            description={`不支持的渲染模式: ${mode}`}
            type="warning"
          />
        );
    }
  };

  return (
    <div className="document-renderer">
      {renderContent()}
    </div>
  );
};

export default DocumentRenderer;