import React, { useState } from 'react';
import { Form, Input, Select, DatePicker, Button, Card, Space, message, Row, Col } from 'antd';
import { SaveOutlined, PrinterOutlined, EyeOutlined } from '@ant-design/icons';
import FieldRenderer from './FieldRenderer';

const { TextArea } = Input;
const { Option } = Select;

/**
 * 文书编辑组件
 * 根据配置动态生成表单字段
 */
const DocumentEditor = ({
  documentType,
  data,
  config,
  onFieldChange,
  onSave,
  isDirty,
  page = '1'
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 处理字段值变更
  const handleFieldChange = (fieldKey, value) => {
    onFieldChange(fieldKey, value);
  };

  // 保存文书
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      await onSave(values);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 打印预览
  const handlePrintPreview = () => {
    const url = new URL(window.location.href);
    url.searchParams.set('mode', 'print');
    window.open(url.toString(), '_blank');
  };

  // 渲染字段
  const renderField = (field) => {
    const value = data[field.key] || '';

    switch (field.type) {
      case 'input':
        return (
          <Form.Item
            key={field.key}
            name={field.key}
            label={field.label}
            rules={[
              { required: field.required, message: `${field.label}不能为空` }
            ]}
            initialValue={value}
          >
            <Input
              placeholder={`请输入${field.label}`}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item
            key={field.key}
            name={field.key}
            label={field.label}
            rules={[
              { required: field.required, message: `${field.label}不能为空` }
            ]}
            initialValue={value}
          >
            <TextArea
              rows={4}
              placeholder={`请输入${field.label}`}
              onChange={(e) => handleFieldChange(field.key, e.target.value)}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item
            key={field.key}
            name={field.key}
            label={field.label}
            rules={[
              { required: field.required, message: `${field.label}不能为空` }
            ]}
            initialValue={value}
          >
            <Select
              placeholder={`请选择${field.label}`}
              onChange={(value) => handleFieldChange(field.key, value)}
            >
              {field.options?.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item
            key={field.key}
            name={field.key}
            label={field.label}
            rules={[
              { required: field.required, message: `${field.label}不能为空` }
            ]}
            initialValue={value}
          >
            <DatePicker
              style={{ width: '100%' }}
              onChange={(date, dateString) => handleFieldChange(field.key, dateString)}
            />
          </Form.Item>
        );

      default:
        return null;
    }
  };

  // 分组字段
  const groupFields = (fields) => {
    const groups = [];
    let currentGroup = [];
    
    fields.forEach((field, index) => {
      currentGroup.push(field);
      
      // 每3个字段一组
      if ((index + 1) % 3 === 0 || index === fields.length - 1) {
        groups.push([...currentGroup]);
        currentGroup = [];
      }
    });
    
    return groups;
  };

  const fieldGroups = groupFields(config.fields || []);

  return (
    <div className="document-editor">
      <Card
        title={<div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{config.name} - 编辑模式</span>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              disabled={!isDirty}
            >
              保存
            </Button>
            <Button
              icon={<PrinterOutlined />}
              onClick={handlePrintPreview}
            >
              打印预览
            </Button>
          </Space>
        </div>}
        className="document-editor-card"
      >
        <Form form={form} layout="vertical">
          {fieldGroups.map((group, groupIndex) => (
            <Row gutter={16} key={groupIndex}>
              {group.map(field => (
                <Col span={8} key={field.key}>
                  {renderField(field)}
                </Col>
              ))}
            </Row>
          ))}
        </Form>
      </Card>
    </div>
  );
};

export default DocumentEditor;