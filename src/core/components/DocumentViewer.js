import React from 'react';
import { Card, Descriptions, Button, Space } from 'antd';
import { PrinterOutlined, EditOutlined } from '@ant-design/icons';

/**
 * 文书查看组件
 * 提供只读查看功能
 */
const DocumentViewer = ({
  documentType,
  data,
  config
}) => {

  const handlePrint = () => {
    const url = new URL(window.location.href);
    url.searchParams.set('mode', 'print');
    window.open(url.toString(), '_blank');
  };

  const handleEdit = () => {
    const url = new URL(window.location.href);
    url.searchParams.set('mode', 'edit');
    window.location.href = url.toString();
  };

  const getFieldValue = (field) => {
    const value = data[field.key];
    if (value === null || value === undefined || value === '') {
      return '—';
    }
    return value;
  };

  return (
    <div className="document-viewer">
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>{config.name} - 查看模式</span>
            <Space>
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={handleEdit}
              >
                编辑
              </Button>
              <Button
                icon={<PrinterOutlined />}
                onClick={handlePrint}
              >
                打印
              </Button>
            </Space>
          </div>
        }
        className="document-viewer-card"
      >
        <Descriptions
          title="文书信息"
          bordered
          column={2}
          size="middle"
        >
          {(config.fields || []).map(field => (
            <Descriptions.Item
              key={field.key}
              label={field.label}
              span={field.type === 'textarea' ? 2 : 1}
            >
              {getFieldValue(field)}
            </Descriptions.Item>
          ))}
        </Descriptions>

        {config.printConfig?.header && (
          <div style={{ marginTop: 24, padding: '16px', background: '#f5f5f5' }}>
            <h4>打印配置</h4>
            <p>布局：{config.printConfig.layout === 'landscape' ? '横向' : '纵向'}</p>
            <p>水印：{config.printConfig.watermark || '无'}</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default DocumentViewer;