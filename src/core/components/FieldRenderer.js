import React from 'react';
import { Input, Select, DatePicker, InputNumber, Switch, Checkbox, Radio } from 'antd';
import moment from 'moment';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 通用字段渲染器
 * 根据字段配置动态渲染不同类型的输入组件
 */
const FieldRenderer = ({
  field,
  value,
  onChange,
  mode = 'edit', // edit | view | print
  ...props
}) => {
  const { type, options = [], placeholder, disabled, readOnly } = field;

  // 获取显示值
  const getDisplayValue = () => {
    if (value === null || value === undefined || value === '') {
      return mode === 'print' ? '__________' : '—';
    }

    switch (type) {
      case 'select':
        const selectedOption = options.find(opt => opt.value === value);
        return selectedOption ? selectedOption.label : value;
      
      case 'date':
        return moment(value).format('YYYY-MM-DD');
      
      case 'datetime':
        return moment(value).format('YYYY-MM-DD HH:mm:ss');
      
      case 'boolean':
        return value ? '是' : '否';
      
      default:
        return value;
    }
  };

  // 查看模式
  if (mode === 'view') {
    return (
      <span className="field-value-view">
        {getDisplayValue()}
      </span>
    );
  }

  // 打印模式
  if (mode === 'print') {
    return (
      <span className="field-value-print">
        {getDisplayValue()}
      </span>
    );
  }

  // 编辑模式
  const handleChange = (newValue) => {
    if (onChange) {
      onChange(newValue);
    }
  };

  switch (type) {
    case 'input':
      return (
        <Input
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        />
      );

    case 'textarea':
      return (
        <TextArea
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          rows={field.rows || 3}
          {...props}
        />
      );

    case 'select':
      return (
        <Select
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        >
          {options.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      );

    case 'multiselect':
      return (
        <Select
          mode="multiple"
          value={value || []}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        >
          {options.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      );

    case 'date':
      return (
        <DatePicker
          value={value ? moment(value) : null}
          onChange={(date) => handleChange(date ? date.format('YYYY-MM-DD') : null)}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        />
      );

    case 'datetime':
      return (
        <DatePicker
          showTime
          value={value ? moment(value) : null}
          onChange={(date) => handleChange(date ? date.format('YYYY-MM-DD HH:mm:ss') : null)}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        />
      );

    case 'daterange':
      return (
        <RangePicker
          value={value ? [moment(value[0]), moment(value[1])] : null}
          onChange={(dates) => handleChange(dates ? [dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')] : null)}
          disabled={disabled || readOnly}
          {...props}
        />
      );

    case 'number':
      return (
        <InputNumber
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          min={field.min}
          max={field.max}
          step={field.step}
          {...props}
        />
      );

    case 'boolean':
      return (
        <Switch
          checked={value}
          onChange={handleChange}
          disabled={disabled || readOnly}
          {...props}
        />
      );

    case 'checkbox':
      return (
        <Checkbox
          checked={value}
          onChange={(e) => handleChange(e.target.checked)}
          disabled={disabled || readOnly}
          {...props}
        >
          {field.label}
        </Checkbox>
      );

    case 'radio':
      return (
        <Radio.Group
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          disabled={disabled || readOnly}
          {...props}
        >
          {options.map(option => (
            <Radio key={option.value} value={option.value}>
              {option.label}
            </Radio>
          ))}
        </Radio.Group>
      );

    default:
      return (
        <Input
          value={value}
          onChange={(e) => handleChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled || readOnly}
          {...props}
        />
      );
  }
};

export default FieldRenderer;