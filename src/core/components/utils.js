/**
 * 通用工具函数
 */

/**
 * 格式化日期
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式字符串 (默认: 'YYYY-MM-DD')
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day);
};

/**
 * 验证字段值
 * @param {Object} field - 字段配置
 * @param {*} value - 字段值
 * @returns {Object} 验证结果 { valid: boolean, message: string }
 */
export const validateField = (field, value) => {
  if (field.required && (value === null || value === undefined || value === '')) {
    return {
      valid: false,
      message: `${field.label}不能为空`
    };
  }
  
  if (field.validation) {
    const { pattern, message } = field.validation;
    if (pattern && !pattern.test(value)) {
      return {
        valid: false,
        message: message || `${field.label}格式不正确`
      };
    }
  }
  
  if (field.minLength && String(value).length < field.minLength) {
    return {
      valid: false,
      message: `${field.label}至少需要${field.minLength}个字符`
    };
  }
  
  if (field.maxLength && String(value).length > field.maxLength) {
    return {
      valid: false,
      message: `${field.label}最多只能有${field.maxLength}个字符`
    };
  }
  
  return { valid: true };
};

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 深拷贝对象
 * @param {Object} obj - 要拷贝的对象
 * @returns {Object} 拷贝后的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  
  const cloned = {};
  Object.keys(obj).forEach(key => {
    cloned[key] = deepClone(obj[key]);
  });
  
  return cloned;
};

/**
 * 防抖函数
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间(ms)
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func - 要执行的函数
 * @param {number} limit - 时间限制(ms)
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 扩展名
 */
export const getFileExtension = (filename) => {
  const parts = filename.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : '';
};

/**
 * 文件大小格式化
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 */
export const downloadFile = (url, filename) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename || 'download';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 复制到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise} Promise对象
 */
export const copyToClipboard = (text) => {
  return navigator.clipboard.writeText(text);
};

/**
 * 判断是否为图片文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为图片
 */
export const isImageFile = (filename) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = getFileExtension(filename);
  return imageExtensions.includes(extension);
};

/**
 * 判断是否为PDF文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为PDF
 */
export const isPDFFile = (filename) => {
  return getFileExtension(filename) === 'pdf';
};

/**
 * 获取URL参数
 * @param {string} name - 参数名
 * @returns {string} 参数值
 */
export const getQueryParam = (name) => {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
};

/**
 * 设置URL参数
 * @param {string} name - 参数名
 * @param {string} value - 参数值
 */
export const setQueryParam = (name, value) => {
  const url = new URL(window.location.href);
  url.searchParams.set(name, value);
  window.history.replaceState({}, '', url);
};

/**
 * 移除URL参数
 * @param {string} name - 参数名
 */
export const removeQueryParam = (name) => {
  const url = new URL(window.location.href);
  url.searchParams.delete(name);
  window.history.replaceState({}, '', url);
};

/**
 * 对象转FormData
 * @param {Object} obj - 要转换的对象
 * @returns {FormData} FormData对象
 */
export const objectToFormData = (obj) => {
  const formData = new FormData();
  Object.keys(obj).forEach(key => {
    if (obj[key] !== null && obj[key] !== undefined) {
      formData.append(key, obj[key]);
    }
  });
  return formData;
};

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器信息
 */
export const getBrowserInfo = () => {
  const ua = navigator.userAgent;
  const browser = {
    name: 'Unknown',
    version: 'Unknown'
  };

  if (ua.indexOf('Chrome') > -1) {
    browser.name = 'Chrome';
    browser.version = ua.match(/Chrome\/([0-9.]+)/)[1];
  } else if (ua.indexOf('Firefox') > -1) {
    browser.name = 'Firefox';
    browser.version = ua.match(/Firefox\/([0-9.]+)/)[1];
  } else if (ua.indexOf('Safari') > -1) {
    browser.name = 'Safari';
    browser.version = ua.match(/Version\/([0-9.]+)/)[1];
  }

  return browser;
};