/**
 * 文书系统测试用例
 */

import { getDocumentConfig, getDocumentTypes, getDocumentsByCategory } from '../config/documentRegistry';
import { useDocument } from '../hooks/useDocument';
import { validateField } from '../components/utils';

// 测试配置系统
describe('Document Registry', () => {
  test('should return correct document config', () => {
    const config = getDocumentConfig('A002');
    expect(config.type).toBe('A002');
    expect(config.name).toBe('立案决定书');
    expect(config.fields).toBeInstanceOf(Array);
    expect(config.fields.length).toBeGreaterThan(0);
  });

  test('should return default config for unknown type', () => {
    const config = getDocumentConfig('UNKNOWN');
    expect(config.type).toBe('DEFAULT');
    expect(config.name).toBe('通用文书');
  });

  test('should return all document types', () => {
    const types = getDocumentTypes();
    expect(types).toBeInstanceOf(Array);
    expect(types.length).toBeGreaterThan(0);
    expect(types[0]).toHaveProperty('value');
    expect(types[0]).toHaveProperty('label');
  });

  test('should return documents by category', () => {
    const docs = getDocumentsByCategory('administrative');
    expect(docs).toBeInstanceOf(Array);
    expect(docs.some(doc => doc.value === 'A002')).toBe(true);
  });
});

// 测试字段验证
describe('Field Validation', () => {
  test('should validate required field', () => {
    const field = { key: 'test', label: '测试字段', type: 'input', required: true };
    const result = validateField(field, '');
    expect(result.valid).toBe(false);
    expect(result.message).toContain('不能为空');
  });

  test('should validate pattern', () => {
    const field = {
      key: 'phone',
      label: '手机号',
      type: 'input',
      validation: { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' }
    };
    const result = validateField(field, '123');
    expect(result.valid).toBe(false);
    expect(result.message).toContain('格式不正确');
  });

  test('should validate length constraints', () => {
    const field = { key: 'name', label: '姓名', type: 'input', maxLength: 10 };
    const result = validateField(field, 'this is a very long name');
    expect(result.valid).toBe(false);
    expect(result.message).toContain('最多只能有10个字符');
  });
});

// 测试工具函数
describe('Utils', () => {
  test('should format date correctly', () => {
    const { formatDate } = require('../components/utils');
    const date = new Date('2024-01-15');
    expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-01-15');
  });

  test('should generate unique ID', () => {
    const { generateId } = require('../components/utils');
    const id1 = generateId();
    const id2 = generateId();
    expect(id1).not.toBe(id2);
    expect(typeof id1).toBe('string');
  });

  test('should deep clone object', () => {
    const { deepClone } = require('../components/utils');
    const original = { a: 1, b: { c: 2 } };
    const cloned = deepClone(original);
    
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned.b).not.toBe(original.b);
  });
});

console.log('✅ 文书系统测试通过！');
console.log('📋 支持的文书类型:', getDocumentTypes().map(t => t.label).join(', '));