import { useState, useEffect, useCallback } from 'react';
import { useRequest } from 'ahooks';
import { getDocumentConfig } from '@/core/config/documentRegistry';

/**
 * 通用文书数据Hook
 * 统一处理文书数据的获取、保存和状态管理
 */
export const useDocument = (documentType, documentId, token) => {
  const documentConfig = getDocumentConfig(documentType);
  
  // 文书数据状态
  const [documentData, setDocumentData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isDirty, setIsDirty] = useState(false);

  // 获取文书数据
  const { data: fetchedData, loading: fetching, run: fetchDocument } = useRequest(
    async () => {
      if (!documentId || !token) return null;
      
      const response = await fetch(`/api/documents/${documentType}/${documentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`获取文书数据失败: ${response.status}`);
      }
      
      return response.json();
    },
    {
      manual: true,
      onSuccess: (data) => {
        setDocumentData(data || {});
        setIsDirty(false);
        setError(null);
      },
      onError: (err) => {
        setError(err.message);
        console.error('获取文书数据失败:', err);
      }
    }
  );

  // 保存文书数据
  const { loading: saving, run: saveDocument } = useRequest(
    async (data) => {
      if (!token) throw new Error('缺少认证token');
      
      const url = documentId 
        ? `/api/documents/${documentType}/${documentId}`
        : `/api/documents/${documentType}`;
      
      const method = documentId ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...data,
          documentType,
          template: documentConfig.template
        })
      });
      
      if (!response.ok) {
        throw new Error(`保存文书失败: ${response.status}`);
      }
      
      return response.json();
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result && result.id) {
          setDocumentData(prev => ({ ...prev, id: result.id }));
        }
        setIsDirty(false);
        setError(null);
      },
      onError: (err) => {
        setError(err.message);
        console.error('保存文书失败:', err);
      }
    }
  );

  // 更新字段值
  const updateField = useCallback((fieldKey, value) => {
    setDocumentData(prev => ({
      ...prev,
      [fieldKey]: value
    }));
    setIsDirty(true);
  }, []);

  // 批量更新字段
  const updateFields = useCallback((fields) => {
    setDocumentData(prev => ({
      ...prev,
      ...fields
    }));
    setIsDirty(true);
  }, []);

  // 重置数据
  const resetDocument = useCallback(() => {
    setDocumentData({});
    setIsDirty(false);
    setError(null);
  }, []);

  // 自动保存（防抖）
  const { run: autoSave } = useRequest(
    async () => {
      if (isDirty && documentData) {
        await saveDocument(documentData);
      }
    },
    {
      manual: true,
      debounceWait: 2000
    }
  );

  // 监听数据变化自动保存
  useEffect(() => {
    if (isDirty) {
      autoSave();
    }
  }, [documentData, isDirty, autoSave]);

  // 初始化加载
  useEffect(() => {
    if (documentId && token) {
      fetchDocument();
    }
  }, [documentType, documentId, token]);

  return {
    // 数据
    documentData,
    documentConfig,
    
    // 状态
    loading: fetching || saving,
    isLoading: fetching,
    isSaving: saving,
    error,
    isDirty,
    
    // 方法
    fetchDocument,
    saveDocument,
    updateField,
    updateFields,
    resetDocument,
    
    // 工具函数
    getFieldValue: (fieldKey) => documentData[fieldKey],
    isFieldRequired: (fieldKey) => {
      const field = documentConfig.fields.find(f => f.key === fieldKey);
      return field ? field.required : false;
    }
  };
};