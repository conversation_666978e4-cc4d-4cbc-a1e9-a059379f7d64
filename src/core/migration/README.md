# 文书系统迁移指南

## 概述
本指南说明如何将现有的400+个重复文书页面迁移到新的通用文书渲染引擎。

## 迁移步骤

### 1. 替换URL路由

#### 旧方式：
```
旧URL: /A002?wskey=xxx&token=xxx&type=edit
旧URL: /B008?wskey=xxx&token=xxx&type=print
```

#### 新方式：
```
新URL: /document?type=A002&id=xxx&token=xxx&mode=edit
新URL: /document?type=B008&id=xxx&token=xxx&mode=print
```

### 2. 配置迁移

#### 2.1 创建文书配置
在 `src/core/config/documentRegistry.js` 中添加新的文书类型：

```javascript
// 添加新的文书类型
NEW_DOCUMENT: {
  type: 'NEW_DOCUMENT',
  name: '新文书名称',
  category: '对应分类',
  template: '对应模板',
  fields: [
    { key: 'field1', label: '字段1', type: 'input', required: true },
    { key: 'field2', label: '字段2', type: 'select', required: false },
    // 更多字段...
  ],
  permissions: ['create', 'edit', 'print', 'view'],
  printConfig: {
    layout: 'portrait',
    header: true,
    footer: true,
    watermark: '司法文书'
  }
}
```

#### 2.2 字段映射
将旧系统的字段映射到新系统：

| 旧字段名 | 新字段名 | 类型映射 |
|----------|----------|----------|
| ajbh | caseNumber | input |
| ajmc | caseName | input |
| dsrxm | partyName | input |
| slrq | filingDate | date |

### 3. 数据迁移

#### 3.1 批量迁移脚本
```bash
# 运行数据迁移脚本
node scripts/migrate-documents.js
```

#### 3.2 手动迁移
对于特殊文书，可以手动创建配置：

1. 复制现有文书配置作为模板
2. 根据实际需求修改字段
3. 添加必要的验证规则

### 4. 代码替换示例

#### 4.1 替换页面路由
```javascript
// 旧代码：src/pages/A002/index.js
// 替换为：
// 删除整个A002目录，使用通用路由
```

#### 4.2 更新菜单配置
```javascript
// 旧菜单配置
{
  path: '/A002',
  component: './A002',
}

// 新菜单配置
{
  path: '/document',
  component: './core/components/DocumentRenderer',
}
```

### 5. 验证迁移

#### 5.1 测试用例
```javascript
// 创建测试用例
const testCases = [
  {
    type: 'A002',
    mode: 'edit',
    expected: '显示编辑表单'
  },
  {
    type: 'B008',
    mode: 'print',
    expected: '显示打印预览'
  }
];
```

#### 5.2 数据验证
```javascript
// 验证数据格式
const validateData = (data) => {
  return data.fields.every(field => 
    field.key && field.label && field.type
  );
};
```

## 迁移检查清单

### 配置检查
- [ ] 文书类型配置完整
- [ ] 字段定义正确
- [ ] 权限设置合理
- [ ] 打印配置正确

### 数据检查
- [ ] 旧数据格式兼容
- [ ] 字段映射无误
- [ ] 验证规则正确
- [ ] 默认值设置

### 功能检查
- [ ] 编辑功能正常
- [ ] 打印功能正常
- [ ] 查看功能正常
- [ ] 权限控制正确

### 性能检查
- [ ] 页面加载速度
- [ ] 数据获取效率
- [ ] 内存使用合理
- [ ] 无内存泄漏

## 回滚方案

### 1. 备份策略
```bash
# 备份旧文件
cp -r src/pages/A002 src/pages/A002.backup
cp -r src/pages/B008 src/pages/B008.backup
```

### 2. 快速回滚
```bash
# 恢复旧文件
mv src/pages/A002.backup src/pages/A002
mv src/pages/B008.backup src/pages/B008
```

### 3. 配置回滚
```javascript
// 恢复旧的路由配置
// 注释掉新的路由，启用旧的路由
```

## 常见问题

### Q1: 如何处理特殊字段验证？
A: 在字段配置中添加validation属性：

```javascript
{
  key: 'idNumber',
  label: '身份证号',
  type: 'input',
  validation: {
    pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
    message: '请输入正确的身份证号'
  }
}
```

### Q2: 如何自定义打印模板？
A: 在printConfig中添加template属性：

```javascript
printConfig: {
  template: 'custom-template',
  // 其他配置...
}
```

### Q3: 如何处理动态字段？
A: 使用动态字段配置：

```javascript
{
  key: 'dynamicFields',
  type: 'dynamic',
  fields: [
    // 动态字段配置
  ]
}
```

## 支持联系

如有迁移问题，请联系：
- 技术支持：<EMAIL>
- 文档地址：/docs/migration
- 示例代码：/examples/migration