/**
 * 文书类型注册中心
 * 统一配置所有文书类型，消除重复代码
 */

export const documentRegistry = {
  // A系列 - 行政文书
  A002: {
    type: 'A002',
    name: '立案决定书',
    category: 'administrative',
    template: 'standard-decision',
    fields: [
      { key: 'caseNumber', label: '案号', type: 'input', required: true },
      { key: 'partyName', label: '当事人姓名', type: 'input', required: true },
      { key: 'partyId', label: '当事人证件号', type: 'input', required: true },
      { key: 'caseType', label: '案件类型', type: 'select', required: true },
      { key: 'decisionDate', label: '决定日期', type: 'date', required: true },
      { key: 'content', label: '决定内容', type: 'textarea', required: true }
    ],
    permissions: ['create', 'edit', 'print', 'sign'],
    printConfig: {
      layout: 'portrait',
      header: true,
      footer: true,
      watermark: '司法文书'
    }
  },

  // B系列 - 检察文书
  B008: {
    type: 'B008',
    name: '起诉意见书',
    category: 'prosecution',
    template: 'prosecution-opinion',
    fields: [
      { key: 'caseNumber', label: '案件编号', type: 'input', required: true },
      { key: 'defendantName', label: '被告人姓名', type: 'input', required: true },
      { key: 'defendantId', label: '被告人身份证号', type: 'input', required: true },
      { key: 'charges', label: '指控罪名', type: 'select', required: true },
      { key: 'facts', label: '犯罪事实', type: 'textarea', required: true },
      { key: 'evidence', label: '证据材料', type: 'textarea', required: true },
      { key: 'prosecutor', label: '检察官', type: 'input', required: true }
    ],
    permissions: ['create', 'edit', 'review', 'print'],
    printConfig: {
      layout: 'portrait',
      header: true,
      footer: true,
      watermark: '起诉意见'
    }
  },

  // C系列 - 审判文书
  C014: {
    type: 'C014',
    name: '刑事判决书',
    category: 'judgment',
    template: 'criminal-judgment',
    fields: [
      { key: 'caseNumber', label: '案号', type: 'input', required: true },
      { key: 'courtName', label: '法院名称', type: 'input', required: true },
      { key: 'judgmentDate', label: '判决日期', type: 'date', required: true },
      { key: 'defendantName', label: '被告人', type: 'input', required: true },
      { key: 'charges', label: '指控罪名', type: 'select', required: true },
      { key: 'judgment', label: '判决结果', type: 'textarea', required: true },
      { key: 'judge', label: '审判长', type: 'input', required: true }
    ],
    permissions: ['create', 'edit', 'review', 'print', 'publish'],
    printConfig: {
      layout: 'portrait',
      header: true,
      footer: true,
      watermark: '法院判决书'
    }
  },

  // W系列 - 文书
  W001: {
    type: 'W001',
    name: '询问笔录',
    category: 'record',
    template: 'interrogation-record',
    fields: [
      { key: 'caseNumber', label: '案号', type: 'input', required: true },
      { key: 'interrogator', label: '询问人', type: 'input', required: true },
      { key: 'interrogatedName', label: '被询问人姓名', type: 'input', required: true },
      { key: 'interrogatedId', label: '被询问人身份证号', type: 'input', required: true },
      { key: 'startTime', label: '开始时间', type: 'datetime', required: true },
      { key: 'endTime', label: '结束时间', type: 'datetime', required: true },
      { key: 'location', label: '询问地点', type: 'input', required: true },
      { key: 'content', label: '询问内容', type: 'textarea', required: true }
    ],
    permissions: ['create', 'edit', 'print', 'view'],
    printConfig: {
      layout: 'portrait',
      header: true,
      footer: true,
      watermark: '询问笔录'
    }
  },

  // X系列 - 执行文书
  X003: {
    type: 'X003',
    name: '执行通知书',
    category: 'execution',
    template: 'execution-notice',
    fields: [
      { key: 'caseNumber', label: '执行案号', type: 'input', required: true },
      { key: 'courtName', label: '执行法院', type: 'input', required: true },
      { key: 'executor', label: '执行人员', type: 'input', required: true },
      { key: 'executedPerson', label: '被执行人', type: 'input', required: true },
      { key: 'executionContent', label: '执行内容', type: 'textarea', required: true },
      { key: 'executionDeadline', label: '执行期限', type: 'date', required: true },
      { key: 'courtContact', label: '法院联系方式', type: 'input', required: true }
    ],
    permissions: ['create', 'edit', 'print', 'view', 'publish'],
    printConfig: {
      layout: 'portrait',
      header: true,
      footer: true,
      watermark: '法院执行'
    }
  },

  // 默认配置模板
  DEFAULT: {
    type: 'DEFAULT',
    name: '通用文书',
    category: 'general',
    template: 'standard-template',
    fields: [
      { key: 'title', label: '标题', type: 'input', required: true },
      { key: 'content', label: '内容', type: 'textarea', required: true },
      { key: 'date', label: '日期', type: 'date', required: true }
    ],
    permissions: ['create', 'edit', 'print'],
    printConfig: {
      layout: 'portrait',
      header: false,
      footer: false
    }
  }
};

/**
 * 根据文书类型获取配置
 * @param {string} type 文书类型代码
 * @returns {Object} 文书配置
 */
export const getDocumentConfig = (type) => {
  return documentRegistry[type] || documentRegistry.DEFAULT;
};

/**
 * 获取所有文书类型列表
 * @returns {Array} 文书类型数组
 */
export const getDocumentTypes = () => {
  return Object.keys(documentRegistry)
    .filter(key => key !== 'DEFAULT')
    .map(key => ({
      value: key,
      label: documentRegistry[key].name,
      category: documentRegistry[key].category
    }));
};

/**
 * 根据分类获取文书类型
 * @param {string} category 分类名称
 * @returns {Array} 该分类下的文书类型
 */
export const getDocumentsByCategory = (category) => {
  return Object.keys(documentRegistry)
    .filter(key => key !== 'DEFAULT' && documentRegistry[key].category === category)
    .map(key => ({
      value: key,
      label: documentRegistry[key].name
    }));
};