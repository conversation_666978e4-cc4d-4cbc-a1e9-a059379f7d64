/**
 * Created by qsh on 2018/4/26.
 */
import {
  getStaticDictData,
  getDynamicDictData,
  getStaticDictItems,
  getDynamicDictItems,
  getStaticTreeData,
  getDynamicTreeData,
  getDynamicTreeTranslateData,
  getStaticTreeTranslateData,
  searchStaticDictData,
  searchDynamicDictData
} from '../common/services/dict';

export default {
  namespace: 'dict',

  state: {
    dataCache: {}
  },

  effects: {
    *fetchStaticDict({ payload }, { select, put, call }) {
      const { page, kind } = payload;
      const cacheKey = `fetch-${kind}-${page}`;
      const dataCache = yield select(({ dict }) => dict.dataCache);
      let data = dataCache[cacheKey];
      if (!data) {
        data = yield call(getStaticDictData, payload);
        yield put({
          type: 'saveDataCache',
          payload: {
            key: cacheKey,
            data
          }
        });
      }
      return data;
    },
    *fetchStaticTree({ payload }, { select, put, call }) {
      const { kind } = payload;
      const cacheKey = `fetch-tree-${kind}`;
      const dataCache = yield select(({ dict }) => dict.dataCache);
      let data = dataCache[cacheKey];
      if (!data) {
        data = yield call(getStaticTreeData, payload);
        if (data && data.length > 0) {
          const data2 = data.map((o) => {
            return {
              ...o,
              title: o.detail,
              value: o.code
            };
          });
          data = data2;
        }
        yield put({
          type: 'saveDataCache',
          payload: {
            key: cacheKey,
            data
          }
        });
      }
      return data;
    },
    *fetchDynamicDict({ payload }, { call }) {
      const response = yield call(getDynamicDictData, payload);
      return response;
    },
    *fetchDynamicTree({ payload }, { call }) {
      const response = yield call(getDynamicTreeData, payload);
      let data = [];
      if (response) {
        const { dictItems } = response;
        data = dictItems.map((o) => {
          return {
            ...o,
            title: o.detail,
            value: o.code
          };
        });
      }
      return data;
    },
    *translateDynamicTree({ payload }, { call }) {
      const response = yield call(getDynamicTreeTranslateData, payload);
      let data = [];
      if (response) {
        data = response.map((o) => {
          return {
            ...o,
            title: o.detail,
            value: o.code
          };
        });
      }
      return data;
    },
    *translateStatic({ payload }, { select, put, call }) {
      const { code, kind } = payload;
      const cacheKey = `translate-${kind}-${code}`;
      const dataCache = yield select(({ dict }) => dict.dataCache);
      let data = dataCache[cacheKey];
      if (!data) {
        data = yield call(getStaticDictItems, payload);
        yield put({
          type: 'saveDataCache',
          payload: {
            key: cacheKey,
            data
          }
        });
      }
      return data;
    },
    *translateStaticTree({ payload }, { select, put, call }) {
      const { code, kind } = payload;
      const cacheKey = `translate-tree-${kind}-${code}`;
      const dataCache = yield select(({ dict }) => dict.dataCache);
      let data = dataCache[cacheKey];
      if (!data) {
        data = yield call(getStaticTreeTranslateData, payload);
        if (data) {
          const data2 = data.map((o) => {
            return {
              ...o,
              title: o.detail,
              value: o.code
            };
          });
          data = data2;
        }
        yield put({
          type: 'saveDataCache',
          payload: {
            key: cacheKey,
            data
          }
        });
      }
      return data;
    },
    *translateDynamic({ payload }, { call }) {
      const response = yield call(getDynamicDictItems, payload);
      return response;
    },
    *searchStaticDict({ payload }, { select, put, call }) {
      const { page, kind, keyword } = payload;
      const cacheKey = `search-${kind}-${page}-${keyword}`;
      const dataCache = yield select(({ dict }) => dict.dataCache);
      let data = dataCache[cacheKey];
      if (!data) {
        data = yield call(searchStaticDictData, payload);
        yield put({
          type: 'saveDataCache',
          payload: {
            key: cacheKey,
            data
          }
        });
      }
      return data;
    },
    *searchDynamicDict({ payload }, { call }) {
      const data = yield call(searchDynamicDictData, payload);
      return data;
    }
  },

  reducers: {
    saveDataCache(state, { payload }) {
      const { key, data } = payload;
      const { dataCache } = state;
      dataCache[key] = data;
      return {
        ...state,
        dataCache
      };
    }
  }
};
