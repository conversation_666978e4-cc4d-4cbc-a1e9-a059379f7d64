/**
 * Created by dd on 2022/10/12.
 */
import { S3Upload, evidencesUpload, handleFile, getParameters, getSignedPdf, handleSdrj } from '@/common/services/qmb';

export default {
  namespace: 'qmb',

  state: {},
  effects: {
    *S3Upload({ payload }, { call }) {
      return yield call(S3Upload, payload);
    },
    *evidencesUpload({ payload }, { call }) {
      return yield call(evidencesUpload, payload);
    },
    *handleFile({ payload }, { call }) {
      return yield call(handleFile, payload);
    },
    *getParameters({ payload }, { call }) {
      return yield call(getParameters, payload);
    },
    *getSignedPdf({ payload }, { call }) {
      return yield call(getSignedPdf, payload);
    },
    *handleSdrj({ payload }, { call }) {
      return yield call(handleSdrj, payload);
    }
  },
  reducers: {}
};
