/**
 * Created by qsh on 2018/6/5.
 */
import { io } from 'SinoGear';
import { config } from '../config';

const handleListResult = (result) => {
  if (result === null || result === undefined || result === '') {
    return [];
  }
  return result.status === 204 ? [] : result;
};


const { dictContextPath } = config;
const {
  staticDict,
  searchStaticDict,
  searchDynamicDict,
  dynamicDict,
  staticDictTranslate,
  dynamicDictTranslate,
  staticTreeDict,
  dynamicTreeDict,
  dynamicTreeDictTranslate,
  staticTreeDictTranslate
} = config.api;
const pageSize = 10;

export async function getStaticDictData(data) {
  const page = data.page ? data.page : '1';
  const url = `${dictContextPath}${staticDict}kind=${data.kind}&page=${page}&pageSize=${pageSize}&searchField=detail`;
  return handleListResult(io.get(url));
}

export async function getDynamicDictData(data) {
  const page = data.page ? data.page : '1';
  const url = `${dictContextPath}${dynamicDict}${data.paramStr}&page=${page}&pageSize=${pageSize}&searchField=detail`;
  return handleListResult(io.get(url));
}

export async function getStaticDictItems(data) {
  const url = `${dictContextPath}${staticDictTranslate}kind=${data.kind}&searchField=detail&code=${data.code}`;
  return handleListResult(io.get(url));
}

export async function getDynamicDictItems(data) {
  const url = `${dictContextPath}${dynamicDictTranslate}${data.paramStr}&searchField=detail&code=${data.code}`;
  return handleListResult(io.get(url));
}

export async function getStaticTreeData(data) {
  const url = `${dictContextPath}${staticTreeDict}kind=${data.kind}&searchField=detail`;
  return handleListResult(io.get(url));
}

export async function getDynamicTreeData(data) {
  const { paramStr } = data;
  const url = `${dictContextPath}${dynamicTreeDict}${paramStr}`;
  return handleListResult(io.get(url));
}

export async function getDynamicTreeTranslateData(data) {
  const { code, paramStr } = data;
  const url = `${dictContextPath}${dynamicTreeDictTranslate}${paramStr}&code=${code}`;
  return handleListResult(io.get(url));
}

export async function getStaticTreeTranslateData(data) {
  const { code, kind } = data;
  const url = `${dictContextPath}${staticTreeDictTranslate}kind=${kind}&code=${code}`;
  return handleListResult(io.get(url));
}

export async function searchStaticDictData(data) {
  const { page = 1, keyword } = data;
  const url =
    `${dictContextPath}${searchStaticDict}?kind=${data.kind}&page=${page}&pageSize=${pageSize}` +
    `&searchField=code;detail&query=${keyword}`;
  return handleListResult(io.get(url));
}

export async function searchDynamicDictData(data) {
  const { page = 1, keyword, params } = data;
  let paramStr = '';
  for (const p in params) {
    if (Object.prototype.hasOwnProperty.call(params, p)) {
      paramStr += `${p}=${params[p]}&`;
    }
  }
  const url =
    `${dictContextPath}${searchDynamicDict}?${paramStr}page=${page}&pageSize=${pageSize}` +
    `&searchField=code;detail&query=${keyword}`;
  return handleListResult(io.get(url));
}
