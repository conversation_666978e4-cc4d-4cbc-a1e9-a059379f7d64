// import request from '@/utils/request';
import { io } from 'SinoGear';
import { config } from '../config';
import { getUrlParam } from '@/utils/func';

export function S3Upload(payload) {
  return io.post(`${config.qmbContextPath}/api/evidence/S3Upload/String`, payload);
}
export function signUpload(payload) {
  const options = {
    headers: {
      Authorization: payload.token,
    },
  };
  return io.post(`${config.jakbContextPath}/api/zjcl/b_jakb_qmzps/upload/sswsqm`, payload, options);
}
export function evidencesUpload(payload) {
  const { token, ...rest } = payload;
  console.log(payload, 'payload');
  const options = {
    headers: {
      Authorization: token,
    },
  };
  const systemCode = getUrlParam(window?.location.href, 'systemCode');
  if (['JAKB_GX'].includes(systemCode)) {
    console.log(rest, 'rest');
    return io.post(`${config?.qmnyConfig?.qmbContextPath}/api/evidence/upload/v1`, rest, options);
  } else {
    return io.post(`${config.qmbContextPath}/api/evidence/upload/v1`, rest, options);
  }
}
export function handleFile(payload) {
  const { token, ...rest } = payload;
  const options = {
    headers: {
      Authorization: token,
    },
  };
  return io.post(`${config.contextPath}/api/jzgl/b_asj_jzs/uploadzjclToJz`, [rest], options);
}

export function getParameters({ systemCode, token }) {
  const options = {
    headers: {
      Authorization: token,
    },
  };
  return io.get(`${config.ajblContextPath}/api/s_parameters/${systemCode}`, options);
}

// 根据pdf url 获取base64
export function getOriginPdf({ url, token }) {
  const options = {
    headers: {
      Authorization: token,
    },
  };
  return io.get(url, options);
}

export function getSSWSPdf({ url, token }) {
  const options = {
    headers: {
      Authorization: token,
      'Content-Type': 'multipart/form-data',
    },
    responseType: 'blob',
  };
  return io.get(url, options);
}

// 获取查看签名
export function getSignedPdf({ token, businessSystem, businessId, clmc }) {
  const options = {
    headers: {
      Authorization: token,
    },
  };
  const systemCode = getUrlParam(window?.location.href, 'systemCode');
  if (['JAKB_GX'].includes(systemCode)) {
    return io.get(
      // eslint-disable-next-line max-len
      `${config?.qmnyConfig?.qmbContextPath}/api/b_evidences/getSignedPdf?businessSystem=${businessSystem}&businessId=${businessId}&clmc=${clmc}`,
      options
    );
  } else {
    return io.get(
      // eslint-disable-next-line max-len
      `${config.qmbContextPath}/api/b_evidences/getSignedPdf?businessSystem=${businessSystem}&businessId=${businessId}&clmc=${clmc}`,
      options
    );
  }
}

// 手动入卷
export function handleSdrj(payload) {
  const { token, ...rest } = payload;
  const params = { ...rest };
  const options = {
    headers: {
      Authorization: token,
    },
  };
  return io.post(`${config.contextPath}/api/b_asj_ws_sswss/rj`, params, options);
}
