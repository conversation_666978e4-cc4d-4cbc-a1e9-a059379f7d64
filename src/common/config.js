const defaultConfig = {
  version: '1.6.4-Beta.1',
  publicTime: '2025-08-05 14:40',
  api: {
    // 静态字典
    staticDict: `/StaticDict/load?`,
    staticDictTranslate: `/StaticDict/loadItem?`,
    staticTreeDict: `/StaticDict/tree?`,
    staticTreeDictTranslate: `/StaticDict/loadItem?`,
    searchStaticDict: `/StaticDict/search`,
    // 动态字典
    dynamicDict: `/DynamicDict/load?`,
    dynamicDictTranslate: `/DynamicDict/loadItem?`,
    dynamicTreeDict: `/DynamicDict/load?`,
    dynamicTreeDictTranslate: `/DynamicDict/loadItem?`,
    searchDynamicDict: `/DynamicDict/search`
  },
  contextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.contextPath,
  wsreportPrintContextPath:
    process.env.NO_PROXY !== 'true' ? '' : process.env.printContextPath,
  wsreportContextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.reportContextPath,
  dzjzContextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.dzjzContextPath,
  ajblContextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.ajblContextPath,
  dictContextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.dictContextPath,
  upLoadWsContextPath: process.env.NO_PROXY !== 'true' ? '' : process.env.upLoadWsContextPath
};

const sggc = window.SinoGearGlobalConfig;

const config = Object.assign({}, defaultConfig, sggc);

export { config };
