import { message } from 'antd';
import styles from './WenShuReport.less';
export const validateForm = (errMessage) => {
  const keys = Object.keys(errMessage);
  console.log('validateForm', errMessage);
  if (keys) {
    let dom = document.getElementById(keys[0]);
    console.log('errMessage2', errMessage[keys[0]].errors[0]);
    const msg = errMessage[keys[0]].errors[0].message;
    message.error(msg);
    const klns = dom.className;
    dom.className = `${klns} ${styles.shabox}`;
    dom.scrollIntoView({ behavior: 'smooth', block: 'center' });
    setTimeout(() => {
      dom.className = klns;
    }, 2000);
  }
};
