.rollContain {
  width: 100%;
  overflow-y: auto;
  .pdfView {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    :global {
      .react-pdf__Page__textContent{
        display: none;
      }
    }
  }
}

.turnContain {
  width: 100%;
  .preview{
    padding-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    :global {
      .react-pdf__Page__textContent{
        display: none;
      }
    }
  }
  .pageNumber{
    font-size: 18px;
    text-align: center;
    padding-top: 10px;
  }
}


