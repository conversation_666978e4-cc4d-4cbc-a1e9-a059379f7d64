import React, { Component } from 'react';
import styles from './wsPage.less';
import RectangleBorder from './RectangleBorder';

class WsPage extends Component {
  render() {
    const { borderLines, footText, zoom, elementStyle = {}, pageStyle = {} } = this.props;

    return (
      <div datatype="wspage" className={styles.pageContainer} style={{ ...{ zoom: zoom / 100 }, ...elementStyle }}>
        <div className={styles.wsContext}>
          <RectangleBorder pageStyle={pageStyle} borderLines={borderLines} footText={footText} />
          <div className={styles.wsBodyContent}>{this.props.children}</div>
        </div>
      </div>
    );
  }
}

export default WsPage;
