import React, { Component, Fragment } from 'react';
import { Document, Page } from 'react-pdf';
import { Pagination } from 'antd';
import styles from './PDFView.less';

class PDFView extends Component {
  state = {
    pageNumber: 1, // 翻页
    numPages: 0
  };

  componentDidMount() {}
  onDocumentLoad = ({ numPages }) => {
    this.setState({ numPages });
  };

  onChangePage = (page) => {
    this.setState({ pageNumber: page });
  };

  // 滚动
  rollPdf = () => {
    const { numPages } = this.state;
    const { pdfUrl, scale = 1.5, rollHeight = '600px' } = this.props;
    return (
      <div className={styles.rollContain} style={{ height: rollHeight }}>
        <Document onLoadSuccess={this.onDocumentLoad} file={pdfUrl} renderMode="canvas" className={styles.pdfView}>
          {new Array(numPages).fill('').map((cur, index) => (
            <Page key={index} scale={scale} pageNumber={index + 1} />
          ))}
        </Document>
      </div>
    );
  };

  // 翻页
  turnPdf = () => {
    const { numPages, pageNumber } = this.state;
    const { pdfUrl, scale = 1.5 } = this.props;
    return (
      <div className={styles.turnContain}>
        <Pagination
          className={styles.pageNumber}
          total={numPages}
          showTotal={(total) => `共 ${total} 页`}
          current={pageNumber}
          pageSize={1}
          size="small"
          onChange={this.onChangePage}
        />
        <div className={styles.preview}>
          <Document onLoadSuccess={this.onDocumentLoad} file={pdfUrl} renderMode="canvas">
            <Page pageNumber={pageNumber} scale={scale} />
          </Document>
        </div>
      </div>
    );
  };

  render() {
    const { type = 'roll', pdfUrl } = this.props;
    return (
      <Fragment>
        {/* <TopBar title="PDF查看" logo={<SgIcon type="sg-add" />} /> */}
        {pdfUrl && (type === 'roll' ? this.rollPdf() : this.turnPdf())}
      </Fragment>
    );
  }
}

export default PDFView;
