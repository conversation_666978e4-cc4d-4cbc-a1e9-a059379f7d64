import React, { Component } from 'react';
import { But<PERSON>, <PERSON>lider, Tooltip } from 'antd';

class Toolbar extends Component {
  handleScaleReport = (type) => {
    this.props.handleScaleReport && this.props.handleScaleReport(type);
  };

  onSliderChange = (value) => {
    this.props.onSliderChange && this.props.onSliderChange(value);
  };

  handlePrintReport = () => {
    this.props.handlePrintReport && this.props.handlePrintReport();
  };

  handlePreviewReport = () => {
    this.props.handlePreviewReport && this.props.handlePreviewReport();
  };

  render() {
    const { style: elemStyle, } = this.props;
    const allStyle = {
      ...elemStyle,
      ...{
        height: '48px',
        boxShadow: '0 2px 10px 0 rgba(0,0,0,.15)',
        left: '0',
        top: '0',
        position: 'fixed',
        width: '100%',
        zIndex: '999',
        background: '#fff'
      }
    };

    return (
      <div style={allStyle}>
        <div style={{ width: '640px', margin: '0 auto', height: '100%', display: 'flex' }}>
          <Tooltip title="缩小">
            <Button
              style={{ alignSelf: 'center', height: '28px', width: '28px' }}
              type="primary"
              shape="circle"
              icon="minus"
              onClick={this.handleScaleReport.bind(this, 'minus')}
            />
          </Tooltip>
          <Slider
            onChange={this.onSliderChange}
            style={{ width: '120px', alignSelf: 'center' }}
            value={this.props.scale}
          />

          <Tooltip title="放大">
            <Button
              onClick={this.handleScaleReport.bind(this, 'plus')}
              type="primary"
              shape="circle"
              icon="plus"
              style={{ marginLeft: '12px', alignSelf: 'center', height: '28px', width: '28px' }}
            />
          </Tooltip>
          {/*<Button style={{ alignSelf: 'center', marginLeft: '220px' }} type="primary" htmlType="submit">
            提交
          </Button>*/}
          {/*<Button
            type="primary"
            ghost
            onClick={this.handlePreviewReport}
            style={{ marginLeft: '10px', alignSelf: 'center', float: 'right' }}
          >
            预览
          </Button>*/}
          <Button
            type="primary"
            style={{
              marginLeft: '400px',
              alignSelf: 'center',
              float: 'right',

            }}
            onClick={this.handlePrintReport}
          >
            上传
          </Button>
        </div>
      </div>
    );
  }
}

export default Toolbar;
