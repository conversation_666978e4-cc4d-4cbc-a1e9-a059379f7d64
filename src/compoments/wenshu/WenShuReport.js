import React, { Component } from 'react';
import { config } from '@/common/config';
import { getDevId } from '@/compoments/wenshu/utils/YfUtil';
import { DatePicker, Form, Spin, message, AutoComplete, InputNumber, Checkbox } from 'antd';
import reactStringReplace from 'react-string-replace';
import moment from 'moment';
import { QRCodeSVG } from 'qrcode.react';
import { router } from 'umi';
import request from '../../utils/request';
import { validateForm } from '@/compoments/wenshu/EnhancedVerification';
import WsPage from './WsPage';
import WsInput from './WsInput';
import FillingWsInput from './FillingWsInput';
import Paragraph from './Paragraph';
import StaticText from './StaticText';
import WsDate from './WsDate';
// import GADictSelect from '../../compoments/GADictSelect';
import { GADictSelect, GARangeDictSelect } from '../../compoments/DictSelect';

import Toolbar from './Toolbar';
import ToolbarQM from './ToolbarQM';
import { calcParagraphSize } from './utils/SizeUtil';
import { isDate, fieldConfigCheck } from './utils/CommonUtil';
import { pagination } from './utils/pagination';
import spyz from '../../assets/spyz.png';
import wsStyle from './WenShuReport.less';

const dateFormat = 'YYYY年MM月DD日';

@Form.create({
  onFieldsChange: () => {
    calcParagraphSize();
  },

  mapPropsToFields(props) {
    // conditionValue 是 connect中传过来的取值
    const { wsData, formConfig, dateList = [] } = props;
    const keys = Object.keys(wsData);

    if (keys.length) {
      const formData = {};

      for (const key of keys) {
        if (isDate(wsData[key]) || dateList.indexOf(key.toLowerCase()) > -1) {
          const checkResult = fieldConfigCheck(formConfig, key);

          // 可编辑 说明是 时间组件 否则 直接显示串
          if (checkResult && checkResult.isEditable) {
            const validata = wsData[key]
              .replace(/[年月]/g, '-')
              .replace(/日/, ' ')
              .replace(/[时分]/g, ':')
              .replace(/秒/, '');

            // 有值才设置
            if (wsData[key]) {
              formData[key] = Form.createFormField({
                value: moment(validata)
              });
            }
          } else if (key === 'birthdayinbegin') {
            // 对 出生日期特殊处理 只显示年月日 时分秒隐藏
            const birthdayinbegin = wsData[key] && moment(wsData[key]).format('YYYY年MM月DD日');
            formData[key] = Form.createFormField({
              value: birthdayinbegin
            });
          } else if (key === 'writetime') {
            // 对 填发时间特殊处理 只显示年月日 时分秒隐藏
            const writetime = wsData[key] && moment(wsData[key]).format('YYYY年MM月DD日');
            formData[key] = Form.createFormField({
              value: writetime
            });
          } else if (key === 'confirmtime') {
            // 对 填发时间特殊处理 只显示年月日 时分秒隐藏
            const confirmtime = wsData[key] && moment(wsData[key]).format('YYYY年MM月DD日');
            formData[key] = Form.createFormField({
              value: confirmtime
            });
          } else {
            formData[key] = Form.createFormField({
              value: wsData[key]
            });
          }
        } else {
          formData[key] = Form.createFormField({
            value: wsData[key]
          });
        }
      }
      return formData;
    } else {
      return {};
    }
  }
})
class Index extends React.Component {
  state = {
    scale: 100,
    isLoading: false,
    keyChangeSource: '',
    newConfig: null
  };

  componentDidMount() {
    calcParagraphSize();
    const newConfig = pagination(this.props);

    this.setState({
      newConfig
    });

    // this.handleinitFormValue();
  }

  componentDidUpdate() {
    calcParagraphSize();
  }

  genPageContent = (dataConfig) => {
    if (!dataConfig) {
      return null;
    }
    const { pages = [], borderLines } = dataConfig;
    const { showPage } = this.props;
    const totalPage = pages.map((item) => {
      const { paragraph, footText, elementStyle, pageStyle } = item;
      const paragraphList = this.genParagraph(paragraph);

      return (
        <WsPage
          zoom={this.state.scale}
          key={item.key}
          borderLines={borderLines}
          footText={footText}
          elementStyle={elementStyle}
          pageStyle={pageStyle}
        >
          {paragraphList}
        </WsPage>
      );
    });

    if (showPage && totalPage.length >= parseInt(showPage)) {
      return totalPage[showPage - 1];
    } else {
      return totalPage;
    }
  };

  genParagraph = (paragraphList) => {
    return paragraphList.map((item) => {
      const { style, contents } = item;

      const contentList = contents.map((contentItem) => {
        if (contentItem.type === 'staticText') {
          return this.genStaticText(contentItem);
        } else if (contentItem.type === 'textField') {
          return this.genTextField(contentItem);
        } else if (contentItem.type === 'bigText') {
          return this.genBigText(contentItem);
        } else if (contentItem.type === 'img') {
          return this.genYZimg(contentItem);
        } else if (contentItem.type === 'list') {
          return this.genList(contentItem);
        } else if (contentItem.type === 'qrcode') {
          return this.genQrcode(contentItem);
        } else if (contentItem.type === 'zdyImg') {
          return this.genImg(contentItem);
        } else if (contentItem.type === 'HTML') {
          return this.genHTML(contentItem);
        } else {
          return null;
        }
      });

      return (
        <Paragraph key={item.key} style={style}>
          {contentList}
        </Paragraph>
      );
    });
  };

  genList = (listContent) => {
    // 取出列表
    const { formData } = this.props;
    const listData = formData[listContent.textName.toLowerCase()];
    return (
      <table className={wsStyle.table}>
        <tbody>
          <tr>
            <td> 编号 </td>
            <td> 名称 </td>
            <td> 数量 </td>
            <td> 特征 </td>
          </tr>
        </tbody>
      </table>
    );
  };

  genQrcode = (qrcodeObject) => {
    const { elementStyle = {} } = qrcodeObject;
    const { formData } = this.props;
    const data = encodeURI(formData.qrcodeData);

    return (
      formData.qrcodeData && (
        <div style={elementStyle}>
          <div>(请扫描此二维码缴纳保证金)</div>
          <QRCodeSVG value={data} size={128} level="H" fgColor="#000000" bgColor="#ffffff" />
        </div>
      )
    );
  };

  genYZimg = (imgObject) => {
    const { elementStyle = {} } = imgObject;
    const yzstyle = {
      ...elementStyle,
      height: '120pt',
      width: '120pt',
      zIndex: '-1'
    };
    const { formData } = this.props;
    // return <img style={yzstyle} src={formData.spdwyz ? `data:image/jpeg;base64,${formData.spdwyz}` : spyz} alt="" />;
    return <img style={yzstyle} src={`data:image/jpeg;base64,${formData.spdwyz || formData.cqdwyz}`} alt="" />;
  };

  genImg = (imgObject) => {
    const { elementStyle = {}, textName = '' } = imgObject;
    const yzstyle = {
      ...elementStyle
      // height: '120pt',
      // width: '120pt',
      // zIndex: '-1'
    };
    const { formData } = this.props;
    return <img style={yzstyle} src={`data:image/jpeg;base64,${formData[textName] || ''}`} alt="" />;
  };

  onSearch = (dictSource, searchText) => {
    if (searchText) {
      const keyChangeSource = dictSource.filter((item) => item.indexOf(searchText) !== -1);
      this.setState({
        keyChangeSource
      });
    } else {
      this.setState({
        keyChangeSource: dictSource
      });
    }
  };

  // // 给表单赋初始值
  // handleinitFormValue = () => {
  //   const { sswsname: wsName, form, formData } = this.props;
  //
  //   if (wsName === '取保候审保证书') {
  //     setTimeout(() => {
  //       form.setFieldsValue({
  //         xm2: formData.reservation15 // 担保人姓名
  //       });
  //     }, 3000);
  //   }
  // };

  // // 字典变更回调
  // handleDicChange = (val, type) => {
  //   const { dispatch, form } = this.props;
  //
  //   // 取保候审报告书，姓名变更，其他字典自动赋值
  //   if (type === 'XM2') {
  //     dispatch({
  //       type: 'C016/getBzrInfoById',
  //       payload: {
  //         id: val
  //       },
  //       callback: data => {
  //         form.setFieldsValue({
  //           xb2: data.xb,                 // 性别
  //           csrq2: moment(data.csrq2),    // 出生日期
  //           xxzz: data.xxdzms,            // 住址
  //           performby: data.zjzl,         // 担保人证件名称
  //           note: data.zjhm,              // 证件号码
  //           dw2: data.gzdw,               // 单位
  //           zy2: data.xrzhiy,             // 职业
  //           lxfs2: data.lxdh              // 联系方式
  //         });
  //       }
  //     });
  //   }
  // };

  genBigText = (bigTextObj) => {
    const { fields, tempStr, elementStyle } = bigTextObj;
    const { getFieldDecorator } = this.props.form;
    const { formConfig, wsData } = this.props;
    const {
      dataConfig: { wsType }
    } = this.props;
    const changeStr = tempStr
      .replace(/\r\n/g, '<br>')
      .replace(/\n/g, '<br>')
      .replace(/\s/g, '&ensp;');

    const bigTextList = reactStringReplace(changeStr, /<%([^%>]+)?%>/g, (match, i) => {
      let itemComponent;
      const matchField = fields && fields.find((item) => item.textName === match);
      const checkResult = fieldConfigCheck(formConfig, match);

      if (matchField) {
        if (matchField.textType === 'checkbox') {
          itemComponent = (
            <span key={`{${matchField.key}_${i}`}>
              {getFieldDecorator(matchField.textName.toLowerCase(), {
                valuePropName: 'checked',
                initialValue: matchField.checked || false
              })(<Checkbox />)}
            </span>
          );
        } else if (matchField.textType === 'list') {
          const wplist = wsData[match.toLowerCase()];
          let wpListNode = '';
          let wpLen = 0;
          if (wplist) {
            wpLen = wplist.length;
            // 判断特征字段超过38个字就让字体变小到8pt
            wpListNode = wplist.map((item, index) => {
              return (
                <tr style={{ breakInside: 'avoid' }} className={wsStyle.wptr} key={item.systemid}>
                  {matchField.listConfig.column.map((row) => {
                    return (
                      <td
                        key={`${row.key}_${index}`}
                        width={row.width}
                        style={{ fontSize: item[row.key] && item[row.key].length > 38 ? '8pt' : '12pt' }}
                      >
                        {row.key === 'index'
                          ? row.rowStartIndex
                            ? row.rowStartIndex + index + 1
                            : index + 1
                          : item[row.key]}
                      </td>
                    );
                  })}

                  {/* <td width="10%">{index + 1}</td> */}
                  {/* <td width="22%">{item.wpmc}</td> */}
                  {/* <td width="16%"> */}
                  {/*  {item.wpsl} */}
                  {/*  {item.jldw} */}
                  {/* </td> */}
                  {/* <td width="62%" style={{ fontSize: item.wptz && item.wptz.length > 38 ? '8pt' : '12pt' }}> */}
                  {/*  {item.wptz} */}
                  {/* </td> */}
                </tr>
              );
            });
          }
          const restLen = 5 - wpLen;
          const restTr = [];
          let restTd = '';
          if (matchField.listConfig.column.length > 4) {
            const tds = [];
            const restLong = matchField.listConfig.column.length - 4;
            for (let j = 0; j < restLong; j++) {
              tds.push(<td />);
            }
            restTd = tds;
          }

          if (restLen > 0) {
            if (!matchField.listConfig.emptyRow) {
              for (let j = 0; j < restLen; j++) {
                if (j === 0) {
                  const trNode = (
                    <tr className={wsStyle.wptr}>
                      <td>以</td>
                      <td>下</td>
                      <td>空</td>
                      <td>白</td>
                      {restTd}
                    </tr>
                  );
                  restTr.push(trNode);
                }
                const trNode = (
                  <tr className={wsStyle.wptr}>
                    <td />
                    <td />
                    <td />
                    <td />
                    {restTd}
                  </tr>
                );
                restTr.push(trNode);
              }
            }
          }
          itemComponent = (
            <table className={wsStyle.table}>
              <tbody>
                <tr>
                  {matchField.listConfig.column.map((item) => {
                    return (
                      <td key={item.key} width={item.width}>
                        {item.name}
                      </td>
                    );
                  })}
                  {/* <td width="10%">编号</td> */}
                  {/* <td width="22%">名称</td> */}
                  {/* <td width="16%">数量</td> */}
                  {/* <td width="62%">特征</td> */}
                </tr>
                {wpListNode}
                {restTr}
              </tbody>
            </table>
          );
        }
        // 如果该项为string类型 或者是只读项目，则直接显示string
        else if (matchField.textType === 'string' || !checkResult.isEditable) {
          const formRule = matchField.formRule || [];

          itemComponent = (
            <span key={`{${matchField.key}_${i}`}>
              {getFieldDecorator(matchField.textName.toLowerCase(), {
                rules: [
                  {
                    required: checkResult.isRequired,
                    message: `请输入${checkResult.chname}`
                  },
                  ...formRule
                ]
              })(
                <WsInput
                  placeholder={checkResult.chname}
                  pageType={wsType}
                  style={matchField.elementStyle}
                  editable={checkResult.isEditable}
                />
              )}
            </span>
          );
        } else if (matchField.textType === 'date') {
          itemComponent = (
            <span key={`{${matchField.key}_${i}`}>
              {getFieldDecorator(matchField.textName.toLowerCase(), {
                rules: [
                  {
                    required: checkResult.isRequired,
                    message: '请输入'
                  }
                ]
              })(
                <DatePicker
                  onChange={(value) => {
                    matchField.onChange && matchField.onChange(value, this.props.form);
                  }}
                  key={`{${matchField.key}_${i}`}
                  size="default"
                  style={{
                    ...{
                      width: '190px',
                      fontSize: '12pt'
                    },
                    ...matchField.elementStyle
                  }}
                  format={matchField.dateFormat || dateFormat}
                  showTime={
                    matchField.showTime && {
                      format: matchField.showTime
                    }
                  }
                  disabledDate={(current) => {
                    if (['WRITETIME', 'writetime'].includes(matchField.textName)) {
                      // 填发时间, 【Q220299000060】所有的诉讼文书的填发时间不能填今天之后的时间，只能选当前时间或者以前的时间
                      return matchField.disabledDate ? matchField.disabledDate(current) : current && current > moment();
                    }
                    return matchField.disabledDate ? matchField.disabledDate(current) : false;
                  }}
                  disabled={!checkResult.isEditable}
                  placeholder={`选择${checkResult.chname}`}
                />
              )}
            </span>
          );
        } else if (matchField.textType === 'dict') {
          const { dispatch } = this.props;
          const { type, kind, params, showCode = true, colModel = 'code|detail', onChange } = matchField.dictConfig;
          // 给动态字典的参数赋值
          if (params?.ajbh !== undefined) {
            Object.assign(params, { ajbh: wsData.ajbh });
          }

          if (type === 'static') {
            itemComponent = (
              <span key={`{${matchField.key}_${i}`}>
                {getFieldDecorator(matchField.textName.toLowerCase(), {
                  rules: [
                    {
                      required: checkResult.isRequired,
                      message: '请输入'
                    }
                  ]
                })(
                  <GADictSelect
                    placeholder={checkResult.chname}
                    type={type}
                    kind={kind}
                    dispatch={dispatch}
                    disabled={!checkResult.isEditable}
                    showCode={showCode}
                    colModel={colModel}
                    dropdownWidth="100%"
                    style={{
                      // ...{
                      //   minWidth: '200px',
                      //   width: 'auto'
                      // },
                      ...matchField.elementStyle
                    }}
                  />
                )}
              </span>
            );
          } else if (type === 'dynamic') {
            itemComponent = (
              <span key={`{${matchField.key}_${i}`}>
                {getFieldDecorator(matchField.textName.toLowerCase())(
                  <GADictSelect
                    placeholder={checkResult.chname}
                    type="dynamic"
                    params={params}
                    dispatch={dispatch}
                    disabled={!checkResult.isEditable}
                    showCode={showCode}
                    colModel={colModel}
                    dropdownWidth="100%"
                    style={{
                      ...{
                        minWidth: '200px',
                        width: 'auto'
                      },
                      ...matchField.elementStyle
                    }}
                    onChange={(val) => onChange && onChange(val, this.props.form)}
                  />
                )}
              </span>
            );
          }
        } else if (matchField.textType === 'auto') {
          itemComponent = (
            <span key={`{${matchField.key}_${i}`}>
              {getFieldDecorator(matchField.textName.toLowerCase())(
                <AutoComplete
                  dataSource={this.state.keyChangeSource || matchField.dictSource}
                  style={matchField.elementStyle}
                  onSearch={this.onSearch.bind(this, matchField.dictSource)}
                  placeholder={checkResult.chname}
                />
              )}
            </span>
          );
        } else if (matchField.textType === 'number') {
          itemComponent = (
            <span key={`{${matchField.key}_${i}`}>
              {getFieldDecorator(matchField.textName.toLowerCase())(
                <InputNumber
                  placeholder={checkResult.chname}
                  style={{
                    ...{
                      border: 'none',
                      borderBottom: '1px solid',
                      borderRadius: '0',
                      textAlign: 'center'
                    },
                    ...matchField.elementStyle
                  }}
                  editable={checkResult.isEditable}
                  min={matchField.min}
                />
              )}
            </span>
          );
        } else if (matchField.textType === 'rangedict') {
          const { dispatch } = this.props;
          const { type, kind, params, mixinModel } = matchField.dictConfig;
          if (type === 'static') {
            itemComponent = (
              <span key={`{${matchField.key}_${i}`}>
                {getFieldDecorator(matchField.textName.toLowerCase(), {
                  rules: [
                    {
                      required: checkResult.isRequired,
                      message: '请输入'
                    }
                  ]
                })(
                  <GARangeDictSelect
                    placeholder={checkResult.chname}
                    type="static"
                    kind={kind}
                    dispatch={dispatch}
                    disabled={!checkResult.isEditable}
                    dropdownWidth="500px"
                    mixinModel={mixinModel}
                    style={{
                      ...matchField.elementStyle
                    }}
                  />
                )}
              </span>
            );
          } else if (type === 'dynamic') {
            itemComponent = (
              <span key={`{${matchField.key}_${i}`}>
                {getFieldDecorator(matchField.textName.toLowerCase())(
                  <GARangeDictSelect
                    placeholder={checkResult.chname}
                    type="dynamic"
                    params={params}
                    dispatch={dispatch}
                    disabled={!checkResult.isEditable}
                    dropdownWidth="500px"
                    mixinModel={mixinModel}
                    style={{
                      ...matchField.elementStyle
                    }}
                  />
                )}
              </span>
            );
          }
        }
      }
      if (matchField?.textType === 'HTML') {
        itemComponent = matchField?.element || '没有传入element属性，即dom节点，请检查';
      }
      return itemComponent;
    });
    const bigTextListNode = bigTextList.map((item, i) => {
      if (typeof item === 'string') {
        return (
          <span
            key={i}
            dangerouslySetInnerHTML={{
              __html: item
            }}
          />
        );
      } else {
        return item;
      }
    });
    return (
      <div key={bigTextObj.key} style={elementStyle}>
        {bigTextListNode}
      </div>
    );
  };

  genStaticText = (staticTextObj) => {
    const { textValue, elementStyle } = staticTextObj;
    return <StaticText key={staticTextObj.key} text={textValue} style={elementStyle} />;
  };

  genHTML = (staticTextObj) => {
    const { element, elementStyle } = staticTextObj;
    return <div style={elementStyle}>{element}</div>;
  };

  genTextField = (textFieldObj) => {
    const { getFieldDecorator } = this.props.form;
    const { textName, elementStyle, textType } = textFieldObj;
    const { formConfig } = this.props;
    const {
      dataConfig: { wsType }
    } = this.props;
    const checkResult = fieldConfigCheck(formConfig, textFieldObj.textName);
    if (textType === 'string') {
      if (wsType === 'filed') {
        return getFieldDecorator(textName.toLowerCase(), {
          rules: [
            {
              required: checkResult.isRequired,
              message: '请输入'
            }
          ]
        })(
          <FillingWsInput
            prefixText={textFieldObj.prefixText}
            key={textFieldObj.key}
            pageType={wsType}
            style={elementStyle}
            editable={checkResult.isEditable}
          />
        );
      } else {
        return getFieldDecorator(textName.toLowerCase(), {
          rules: [
            {
              required: checkResult.isRequired,
              message: '请输入'
            }
          ]
        })(
          <WsInput
            prefixText={textFieldObj.prefixText || ''}
            key={textFieldObj.key}
            pageType={wsType}
            style={elementStyle}
            editable={checkResult.isEditable}
          />
        );
      }
    } else if (textType === 'date') {
      return getFieldDecorator(textName.toLowerCase(), {
        rules: [
          {
            required: checkResult.isRequired,
            message: '请输入'
          }
        ]
      })(
        <WsDate
          format={dateFormat}
          key={textFieldObj.key}
          pageType="filed"
          style={elementStyle}
          disabled={!checkResult.isEditable}
        />
      );
    } else if (textType === 'dict') {
      const { dispatch } = this.props;
      const { type, kind, params } = textFieldObj.dictConfig;
      if (type === 'static') {
        return (
          <span style={elementStyle} key={`{${textFieldObj.key}`}>
            {getFieldDecorator(textFieldObj.textName.toLowerCase(), {
              rules: [
                {
                  required: checkResult.isRequired,
                  message: '请输入'
                }
              ]
            })(<GADictSelect type={type} kind={kind} dispatch={dispatch} disabled={!checkResult.isEditable} />)}
          </span>
        );
      } else if (type === 'dynamic') {
        return (
          <span key={`{${textFieldObj.key}`}>
            {getFieldDecorator(textFieldObj.textName.toLowerCase())(
              <GADictSelect type="dynamic" params={params} dispatch={dispatch} disabled />
            )}
          </span>
        );
      }
    }
  };

  zhDigitToArabic = (digit) => {
    if (!digit) {
      return '';
    }
    const zh = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    const unit = ['千', '百', '十'];
    const quot = [
      '万',
      '亿',
      '兆',
      '京',
      '垓',
      '秭',
      '穰',
      '沟',
      '涧',
      '正',
      '载',
      '极',
      '恒河沙',
      '阿僧祗',
      '那由他',
      '不可思议',
      '无量',
      '大数'
    ];
    let result = 0,
      quotFlag;

    for (let i = digit.length - 1; i >= 0; i--) {
      if (zh.indexOf(digit[i]) > -1) {
        // 数字
        if (quotFlag) {
          result += quotFlag * getNumber(digit[i]);
        } else {
          result += getNumber(digit[i]);
        }
      } else if (unit.indexOf(digit[i]) > -1) {
        // 十分位
        if (quotFlag) {
          result += quotFlag * getUnit(digit[i]) * getNumber(digit[i - 1]);
        } else {
          result += getUnit(digit[i]) * getNumber(digit[i - 1]);
        }
        --i;
      } else if (quot.indexOf(digit[i]) > -1) {
        // 万分位
        if (unit.indexOf(digit[i - 1]) > -1) {
          if (getNumber(digit[i - 1])) {
            result += getQuot(digit[i]) * getNumber(digit[i - 1]);
          } else {
            result += getQuot(digit[i]) * getUnit(digit[i - 1]) * getNumber(digit[i - 2]);
            quotFlag = getQuot(digit[i]);
            --i;
          }
        } else {
          result += getQuot(digit[i]) * getNumber(digit[i - 1]);
          quotFlag = getQuot(digit[i]);
        }
        --i;
      }
    }

    return result;

    // 返回中文大写数字对应的阿拉伯数字
    function getNumber(num) {
      for (let i = 0; i < zh.length; i++) {
        if (zh[i] === num) {
          return i;
        }
      }
    }

    // 取单位
    function getUnit(num) {
      for (let i = unit.length; i > 0; i--) {
        if (num === unit[i - 1]) {
          return Math.pow(10, 4 - i);
        }
      }
    }

    // 取分段
    function getQuot(q) {
      for (let i = 0; i < quot.length; i++) {
        if (q === quot[i]) {
          return Math.pow(10, (i + 1) * 4);
        }
      }
    }
  };
  handleSubmit = (e) => {
    e.preventDefault();
    this.setState({
      isLoading: true
    });
    // console.log('进入打印方法');
    this.props.form.validateFields((err, values) => {
      // console.log("props",this.props);
      // console.log("form Data",values);
      // return ;
      const { formConfig } = this.props;
      const postFormData = {};
      console.log('err', err);
      if (!err) {
        // console.log('进入处理阶段');
        if (!Object.keys(values).length) {
          this.setState({
            isLoading: false
          });
          return;
        }

        for (const key of Object.keys(values)) {
          if (typeof values[key] === 'object' && values[key]) {
            // 先转换时间格式的字段
            values[key] = moment(values[key]).format('YYYY-MM-DD HH:mm:ss');
          }

          // 对这个字段进行特殊处理
          if (key === 'delaylengthofterm') {
            const zh_value = this.zhDigitToArabic(values[key]);
            values[key] = zh_value || values[key];
          }

          // 仅对可编辑的字段进行保存
          const checkResult = fieldConfigCheck(formConfig, key);
          if (checkResult.isEditable) {
            if (values[key] === undefined) {
              postFormData[key] = null;
            } else {
              postFormData[key] = values[key];
            }
          }

          // 对特殊字段进行处理 比如 checkbox
          const { dataConfig } = this.props;
          if (dataConfig.specialField) {
            const { specialField } = dataConfig;
            if (specialField.checkBox) {
              const { checkBox, toField } = specialField;
              const checkboxResult = [];
              for (let i = 0; i < checkBox.length; i++) {
                if (values[checkBox[i].toLowerCase()]) {
                  const index = checkBox[i].split('_')[1];
                  checkboxResult.push(index);
                }
              }
              postFormData[toField] = checkboxResult.join(',');
            }
          }
        }
        console.log('postFormData', postFormData);
        const {
          fileName,
          sswsname,
          location: { query }
        } = this.props;

        const filename = fileName;

        const { wskey, token, ajbh } = query;

        if (wskey) {
          const formData = {
            ...postFormData,
            systemid: wskey
          };
          console.log('开始请求数据', fileName);
          this.props
            .dispatch({
              type: `${filename}/updateData`,
              payload: {
                token,
                formData,
                systemid: wskey,
                filename: fileName,
                wsmc: sswsname
              }
            })
            .then((res) => {
              this.setState({
                isLoading: false
              });
              if (res && res.errorMsg) {
                message.error(res.errorMsg);
              } else {
                message.info('保存成功');

                parent.postMessage && parent.postMessage('runSswsReQuery', '*'); // eslint-disable-line
                window.opener && window.opener.postMessage && window.opener.postMessage('runSswsReQuery', '*'); // eslint-disable-line
                window.location.reload();
              }
            });
        } else {
          const { extraData, wsData, formData: initFormData, printUrl } = this.props;
          let formData = {
            ...postFormData,
            ajbh,
            reservation02: wsData.reservation02
          };
          if (extraData) {
            formData = {
              ...initFormData,
              ...extraData,
              ...formData
            };
          }
          this.props
            .dispatch({
              type: `${filename}/createData`,
              payload: {
                token,
                formData,
                filename: fileName,
                wsmc: sswsname
              }
            })
            .then((res) => {
              console.info('res===>', res);
              this.setState({
                isLoading: false
              });
              if (res.systemid) {
                window.location = `/${printUrl || fileName}?wskey=${res.systemid}&token=${token}&type=edit`;
              }
            });
        }
      } else {
        validateForm(err);
        this.setState({
          isLoading: false
        });
      }
    });
  };

  handleSubmitAndPrint = (DSQM) => {
    this.setState({
      isLoading: true
    });
    this.props.form.validateFields((err, values) => {
      if (err) {
        message.warn('必填项校验未通过，请检查！');
        console.log('err', err);
        validateForm(err);
        setTimeout(() => {
          this.setState({
            isLoading: false
          });
        }, 1000);
        return;
      }
      const { formConfig } = this.props;
      const postFormData = {};
      if (!err) {
        if (!Object.keys(values).length) {
          if (this.props.handleReportUpload) {
            this.props.handleReportUpload();
            window.location.reload();
          } else {
            this.handlePrintReport(DSQM);
            window.location.reload();
          }
        }
        for (const key of Object.keys(values)) {
          if (typeof values[key] === 'object') {
            // 先转换时间格式的字段
            values[key] = moment(values[key]).format('YYYY-MM-DD HH:mm:ss');
          }

          // 对这个字段进行特殊处理
          if (key === 'delaylengthofterm') {
            const zh_value = this.zhDigitToArabic(values[key]);
            values[key] = zh_value || values[key];
          }

          // 仅对可编辑的字段进行保存
          const checkResult = fieldConfigCheck(formConfig, key);
          if (checkResult.isEditable) {
            postFormData[key] = values[key];
          }

          // 对特殊字段进行处理 比如 checkbox
          const { dataConfig } = this.props;
          if (dataConfig.specialField) {
            const { specialField } = dataConfig;
            if (specialField.checkBox) {
              const { checkBox, toField } = specialField;
              const checkboxResult = [];
              for (let i = 0; i < checkBox.length; i++) {
                if (values[checkBox[i].toLowerCase()]) {
                  const index = checkBox[i].split('_')[1];
                  checkboxResult.push(index);
                }
              }
              postFormData[toField] = checkboxResult.join(',');
            }
          }
        }

        const {
          fileName,
          sswsname,
          location: { query }
        } = this.props;

        const filename = this.props.fileName || fileName;

        const { wskey, token, ajbh, type } = query;
        console.log('wskey', wskey);

        // 需要校验打印的文书类型
        const needCheckPrintWscodes = [
          // 拘传、拘留、取保、监视居住、逮捕、起诉
          'R059',
          'R099',
          'R101',
          'R001',
          'R001A',
          // R054开头的是行政处罚
          'R054',
          // 强制戒毒、社区戒毒、社区康复、继续盘问
          'R006',
          'R014',
          'R007',
          'R007A',
          'R012',
          'R016',
          'A009',
          'A009B'
        ];

        // 获取当前文书的wscode
        const currentWscode = this.props.wsData?.wscode || this.props.formData?.wscode;

        // 判断是否需要校验打印
        const needCheckPrint = needCheckPrintWscodes.some((code) => {
          if (code === 'R054') {
            // R054开头的都是行政处罚
            return currentWscode && currentWscode.startsWith('R054');
          }
          return currentWscode === code;
        });

        if (needCheckPrint) {
          // 先请求校验接口，检查是否可以打印
          this.props
            .dispatch({
              type: 'common/canprint',
              payload: {
                wirtid: wskey,
                token
              }
            })
            .then((res) => {
              // 检查返回结果
              if (res && res.data === false) {
                // 如果不可以打印，显示错误信息并停止后续业务
                message.error(res.msg || '当前状态不允许打印');
                this.setState({
                  isLoading: false
                });
                return;
              }

              // 如果可以打印，继续后续业务
              this.executeBusinessLogic(wskey, postFormData, filename, fileName, sswsname, token, type, DSQM);
            })
            .catch((error) => {
              // 请求失败时的处理
              console.error('校验接口请求失败:', error);
              message.error('校验失败，请稍后重试');
              this.setState({
                isLoading: false
              });
            });
        } else {
          // 不需要校验的文书类型，直接执行业务逻辑
          this.executeBusinessLogic(wskey, postFormData, filename, fileName, sswsname, token, type, DSQM);
        }
      } else {
        console.info(err);
        validateForm(err);
        this.setState({
          isLoading: false
        });
      }
      parent.postMessage && parent.postMessage('runSswsPrintReQuery', '*'); // eslint-disable-line
      window.opener && window.opener.postMessage && window.opener.postMessage('runSswsPrintReQuery', '*'); // eslint-disable-line
    });
  };

  handlePrintReport = (DSQM) => {
    console.info('handlePrintReport---');
    const {
      fileName,
      location: { query },
      sswsname,
      printUrl,
      isQM
    } = this.props;
    const { wskey, token } = query;
    const { wsreportPrintContextPath, wsreportContextPath, qmContextPath, isOpenQM } = config;
    console.info('开始打印------》');

    // isOpenQM 是否开放签名
    // isQM 该文书是否需要签名
    // DSQM 用户所在地市是否需要签名
    if (isOpenQM && isQM && DSQM) {
      window.open(
        /* eslint-disable-next-line */
        `${qmContextPath}?url=${wsreportContextPath}/${printUrl ||
          fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${sswsname}&fileName=${fileName}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
        // `http://192.168.1.104:3000/print?url=http://192.168.1.10:8002/A002&type=print&wskey=PCS37201302260000000000184156&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODE3MjUzMX0.axgbBRqBHFNWwSpLvzKvyWp1Y5sTQPR6LwTngnPMQXo`,
      );
    } else {
      window.open(
        /* eslint-disable-next-line */
        `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl ||
          fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${sswsname}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
        // `http://192.168.1.104:3000/print?url=http://192.168.1.10:8002/A002&type=print&wskey=PCS37201302260000000000184156&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODE3MjUzMX0.axgbBRqBHFNWwSpLvzKvyWp1Y5sTQPR6LwTngnPMQXo`,
      );
    }
  };

  handleSave = () => {
    this.setState({
      isLoading: true
    });

    this.props.form.validateFields((err, values) => {
      if (err) {
        message.warn('必填项校验未通过，请检查！');
        console.log('err', err);
        validateForm(err);
        setTimeout(() => {
          this.setState({
            isLoading: false
          });
        }, 1000);
        return;
      }
      const { formConfig } = this.props;
      const postFormData = {};
      if (!err) {
        for (const key of Object.keys(values)) {
          if (typeof values[key] === 'object') {
            // 先转换时间格式的字段
            values[key] = moment(values[key]).format('YYYY-MM-DD HH:mm:ss');
          }
          // 对这个字段进行特殊处理
          if (key === 'delaylengthofterm') {
            const zh_value = this.zhDigitToArabic(values[key]);
            values[key] = zh_value || values[key];
          }
          // 仅对可编辑的字段进行保存
          const checkResult = fieldConfigCheck(formConfig, key);
          if (checkResult.isEditable) {
            postFormData[key] = values[key];
          }
        }
        this.props.handleSave && this.props.handleSave(postFormData);
      } else {
        console.info('222', err);
        validateForm(err);
        this.setState({
          isLoading: false
        });
      }
    });
  };

  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  // 执行业务逻辑的方法
  executeBusinessLogic = (wskey, postFormData, filename, fileName, sswsname, token, type, DSQM) => {
    if (wskey) {
      console.info('更新数据。。。');
      const formData = {
        ...postFormData,
        systemid: wskey
      };
      console.log('formData', formData);
      this.props
        .dispatch({
          type: `${filename}/updateData`,
          payload: {
            token,
            formData,
            systemid: wskey,
            filename: fileName,
            wsmc: sswsname,
            type
          }
        })
        .then(() => {
          message.info('保存成功');
          this.setState({
            isLoading: false
          });
          if (this.props.handleReportUpload) {
            this.props.handleReportUpload();
            window.location.reload();
          } else {
            this.handlePrintReport(DSQM);
            window.location.reload();
          }
        });
    } else {
      console.info('创建数据。。。');
      const { extraData, wsData, formData: initFormData, printUrl } = this.props;
      const { ajbh } = this.props.location.query;
      let formData = {
        ...postFormData,
        ajbh,
        reservation02: wsData.reservation02
      };
      if (extraData) {
        formData = {
          ...initFormData,
          ...extraData,
          ...formData
        };
      }
      this.props
        .dispatch({
          type: `${filename}/createData`,
          payload: {
            token,
            formData,
            filename: fileName,
            wsmc: sswsname
          }
        })
        .then((res) => {
          this.setState({
            isLoading: false
          });
          window.location = `/${printUrl || fileName}?wskey=${res.systemid}&token=${token}&type=edit`;
        });
    }
  };

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  handleGenWSZH = () => {
    const {
      location: { query }
    } = this.props;

    const { wskey, token } = query;

    this.props
      .dispatch({
        type: `wszh/getWszh`,
        payload: {
          systemid: wskey,
          token
        }
      })
      .then((res) => {
        console.log('文书字号请求返回结果', res);
        if (res && res.code === '1') {
          message.info(res.msg).then((res) => {
            window.location.reload();
          });
        } else if (res && res.code === '0') {
          message.info(res.msg);
        }
      });
  };
  handleSubmitAndQm = () => {
    this.setState({
      isLoading: true
    });
    this.props.form.validateFields((err, values) => {
      if (err) {
        message.warn('必填项校验未通过，请检查！');
        console.log('err', err);
        validateForm(err);
        setTimeout(() => {
          this.setState({
            isLoading: false
          });
        }, 1000);
        return;
      }
      const { formConfig } = this.props;
      const postFormData = {};
      if (!err) {
        if (!Object.keys(values).length) {
          this.handleQm();
        }
        for (const key of Object.keys(values)) {
          if (typeof values[key] === 'object') {
            // 先转换时间格式的字段
            values[key] = moment(values[key]).format('YYYY-MM-DD HH:mm:ss');
          }

          // 对这个字段进行特殊处理
          if (key === 'delaylengthofterm') {
            const zh_value = this.zhDigitToArabic(values[key]);
            values[key] = zh_value || values[key];
          }

          // 仅对可编辑的字段进行保存
          const checkResult = fieldConfigCheck(formConfig, key);
          if (checkResult.isEditable) {
            postFormData[key] = values[key];
          }

          // 对特殊字段进行处理 比如 checkbox
          const { dataConfig } = this.props;
          if (dataConfig.specialField) {
            const { specialField } = dataConfig;
            if (specialField.checkBox) {
              const { checkBox, toField } = specialField;
              const checkboxResult = [];
              for (let i = 0; i < checkBox.length; i++) {
                if (values[checkBox[i].toLowerCase()]) {
                  const index = checkBox[i].split('_')[1];
                  checkboxResult.push(index);
                }
              }
              postFormData[toField] = checkboxResult.join(',');
            }
          }
        }
        const {
          fileName,
          sswsname,
          location: { query }
        } = this.props;
        const filename = this.props.fileName || fileName;
        const { wskey, token, ajbh, type } = query;
        if (wskey) {
          const formData = {
            ...postFormData,
            systemid: wskey
          };

          this.props
            .dispatch({
              type: `${filename}/updateData`,
              payload: {
                token,
                formData,
                systemid: wskey,
                filename: fileName,
                wsmc: sswsname,
                type
              }
            })
            .then(() => {
              message.info('保存成功');
              this.setState({
                isLoading: false
              });
              this.handleQm();
            });
        } else {
          const { extraData, wsData, formData: initFormData, printUrl } = this.props;
          let formData = {
            ...postFormData,
            ajbh,
            reservation02: wsData.reservation02
          };
          if (extraData) {
            formData = {
              ...initFormData,
              ...extraData,
              ...formData
            };
          }
          this.props
            .dispatch({
              type: `${filename}/createData`,
              payload: {
                token,
                formData,
                filename: fileName,
                wsmc: sswsname
              }
            })
            .then((res) => {
              this.setState({
                isLoading: false
              });
              this.handleQm(res.systemid);
            });
        }
      } else {
        validateForm(err);
        this.setState({
          isLoading: false
        });
      }
      parent.postMessage && parent.postMessage('runSswsPrintReQuery', '*'); // eslint-disable-line
      window.opener && window.opener.postMessage && window.opener.postMessage('runSswsPrintReQuery', '*'); // eslint-disable-line
    });
  };
  // 电子签名
  handleQm = (systemid) => {
    const qmb = config.selectQmb;
    const {
      fileName,
      location: { query },
      sswsname,
      printUrl,
      formData,
      dispatch
    } = this.props;
    const { wskey, token, ajbh, systemCode } = query;
    query.dxbh = formData.dxbh;
    query.ajmc = formData.ajmc;
    const wskey1 = wskey || systemid;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    let url = '';
    if (systemCode) {
      url = `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl ||
        fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}&systemCode=${systemCode}`;
    } else {
      url = `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl ||
        fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}`;
    }
    if (qmb === 'JY') {
      // router.push({
      //   pathname: '/qmsp',
      //   query: {
      //     url,
      //     wskey: wskey || systemid,
      //     token,
      //     ajbh: ajbh || formData?.ajbh,
      //     sswsname,
      //     dxbh: formData.dxbh,
      //     ajmc: formData.ajmc,
      //     fileName: printUrl || fileName
      //   }
      // });
      // setTimeout(() => window.location.reload(), 200);

      // 创建一个带有查询参数的新 URL
      const searchParams = new URLSearchParams([
        ['url', url],
        ['wskey', wskey || systemid],
        ['token', token],
        ['ajbh', ajbh || formData?.ajbh],
        ['sswsname', sswsname],
        ['dxbh', formData.dxbh],
        ['ajmc', formData.ajmc],
        ['fileName', printUrl || fileName]
      ]);
      let fullUrl = '';
      if (['JAKB_GX'].includes(systemCode)) {
        fullUrl = `${wsreportContextPath}/jakbQm/qm?${searchParams.toString()}&sswsnameOfUrl=${query?.sswsname}`;
      } else {
        fullUrl = `${config.qmfeContentPath}/qmsp?${searchParams.toString()}`;
      }

      // 使用 window.open 打开新窗口
      window.open(fullUrl, '_blank');
    } else if (qmb === 'YF') {
      // 有方参数
      /*
       url --- pdf路径
       query --- 签名捺印完成后，跳转到归档页面的跳转参数
       X --- X坐标
       Y --- Y坐标
      */
      // 根据Pdf url 获取Pdf base64格式数据
      try {
        dispatch({
          type: 'qmb/getParameters',
          payload: { systemCode, token }
        }).then((r) => {
          let path = config.jakbPcJakbContextPath;
          if (r && r.value) {
            path = r.value;
          }
          const urlStr = `${path}/api/mobile/b_asj_sswss/getWsPdfByte/pz/${printUrl ||
            fileName}/print/${wskey}/${sswsname}/${token}/0`;
          request(urlStr, {
            method: 'get'
          }).then((res) => {
            // 初始化，以及打开PDF进行签名捺印
            if (res && res?.code === 1) {
              const base64Data = res?.data?.data.split(',')[1];
              const ZB = res?.data?.qmConfigList[0]?.location;
              const PageNoStart = res?.data?.qmConfigList[0]?.pagenostart;
              getDevId(
                'PDFCore',
                'O2SPDFCoreAdapter',
                'PDFCore',
                false,
                undefined,
                base64Data,
                ZB,
                PageNoStart,
                query,
                this.props
              );
            }
          });
        });
      } catch (e) {
        message.error('获取文件失败！');
      }
    } else {
      message.error('没有配置签名版');
    }
  };

  render() {
    const { dataConfig, isQM } = this.props;
    const { newConfig } = this.state;
    const { isOpenQM } = config;

    console.log('isOpenQM 是否开放了签名功能', isOpenQM);
    console.log('isQM 该文书是否需要签名', isQM);

    return (
      <Spin spinning={this.state.isLoading}>
        <Form
          layout="inline"
          onSubmit={this.handleSubmit}
          style={{
            marginTop: '60px'
          }}
        >
          {isQM && isOpenQM ? (
            <ToolbarQM
              onSliderChange={this.onSliderChange}
              handleScaleReport={this.handleScaleReport}
              handleGenWSZH={this.handleGenWSZH}
              handlePrintReport={this.handleSubmitAndPrint}
              scale={this.state.scale}
              handleSaveForm={this.handleSave}
              {...this.props}
            />
          ) : (
            <Toolbar
              onSliderChange={this.onSliderChange}
              handleScaleReport={this.handleScaleReport}
              handleGenWSZH={this.handleGenWSZH}
              handlePrintReport={this.handleSubmitAndPrint}
              handleQm={this.handleSubmitAndQm}
              scale={this.state.scale}
              handleSaveForm={this.handleSave}
              {...this.props}
            />
          )}
          {this.genPageContent(newConfig || dataConfig)}
        </Form>
      </Spin>
    );
  }
}

export default Index;
