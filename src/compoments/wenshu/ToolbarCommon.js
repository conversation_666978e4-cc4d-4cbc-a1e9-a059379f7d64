import React, { Component } from 'react';
import { connect } from 'dva';
import { Button, Select, Slider, Tooltip, Dropdown, Menu, message, Modal } from 'antd';
import { config } from '@/common/config';
import request from '@/utils/request';
import { getDevId } from '@/compoments/wenshu/utils/YfUtil';
import PDFView from '@/compoments/wenshu/PDFView';
import { router } from 'umi';

const { Option } = Select;
@connect(({ qmb }) => qmb)
class ToolbarCommon extends Component {
  state = {
    ModalVisible: false, // 查看签名弹窗
    pdfUrl: '' // 查看签名pdf
  };

  handlePrintReport = () => {
    const {
      fileName,
      location: { query },
      printUrl,
      specialType
    } = this.props;
    const { wskey = '', token, sswsname = '' } = query;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    console.info('开始打印------》');
    window.open(
      /* eslint-disable-next-line */
      `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl || fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${sswsname}${specialType || ''}`,
      '_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
    );
  };

  // 电子签名
  handleQm = (systemid) => {
    const qmb = config.selectQmb;
    const {
      fileName,
      location: { query },
      printUrl,
      formData,
      dispatch
    } = this.props;
    const { wskey, token, ajbh, systemCode, sswsname = '' } = query;
    query.dxbh = formData?.dxbh;
    query.ajmc = formData?.ajmc;
    const wskey1 = wskey || systemid;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    let url = '';
    // if(systemCode){
    //   url = `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl || fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}&systemCode=${systemCode}`
    // } else {
    //   url = `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${printUrl || fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}`
    // }
    if (systemCode) {
      url = `/print?url=${wsreportContextPath}/${printUrl ||
        fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}&systemCode=${systemCode}`;
    } else {
      url = `/print?url=${wsreportContextPath}/${printUrl ||
        fileName}&type=print&wskey=${wskey1}&token=${token}&sswsname=${sswsname}`;
    }
    if (qmb === 'JY') {
      // router.push({
      //   pathname: '/qmsp',
      //   query: {
      //     url,
      //     wskey: wskey || systemid,
      //     token,
      //     ajbh: ajbh || formData?.ajbh,
      //     sswsname,
      //     dxbh: formData.dxbh,
      //     ajmc: formData.ajmc,
      //     fileName: printUrl || fileName
      //   }
      // });
      // setTimeout(() => window.location.reload(), 200);

      // 创建一个带有查询参数的新 URL
      const searchParams = new URLSearchParams([
        ['url', url],
        ['wskey', wskey || systemid],
        ['token', token],
        ['ajbh', ajbh || formData?.ajbh],
        ['sswsname', sswsname],
        ['dxbh', formData.dxbh],
        ['ajmc', formData.ajmc],
        ['fileName', printUrl || fileName]
      ]);
      let fullUrl = '';
      if (['JAKB_GX'].includes(systemCode)) {
        fullUrl = `${wsreportContextPath}/jakbQm/qm?${searchParams.toString()}&sswsnameOfUrl=${query?.sswsname}`;
      } else {
        fullUrl = `${config.qmfeContentPath}/qmsp?${searchParams.toString()}`;
      }
      // 使用 window.open 打开新窗口
      window.open(fullUrl, '_blank');
    } else if (qmb === 'YF') {
      // 有方参数
      /*
       url --- pdf路径
       query --- 签名捺印完成后，跳转到归档页面的跳转参数
       X --- X坐标
       Y --- Y坐标
      */
      // 根据Pdf url 获取Pdf base64格式数据
      try {
        dispatch({
          type: 'qmb/getParameters',
          payload: { systemCode, token }
        }).then((r) => {
          let path = config.jakbPcJakbContextPath;
          if (r && r.value) {
            path = r.value;
          }
          const urlStr = `${path}/api/mobile/b_asj_sswss/getWsPdfByte/pz/${printUrl ||
            fileName}/print/${wskey}/${sswsname}/${token}/0`;
          request(urlStr, {
            method: 'get'
          }).then((res) => {
            // 初始化，以及打开PDF进行签名捺印
            if (res && res?.code === 1) {
              const base64Data = res?.data?.data.split(',')[1];
              const ZB = res?.data?.qmConfigList[0]?.location;
              const PageNoStart = res?.data?.qmConfigList[0]?.pagenostart;
              getDevId(
                'PDFCore',
                'O2SPDFCoreAdapter',
                'PDFCore',
                false,
                undefined,
                base64Data,
                ZB,
                PageNoStart,
                query,
                this.props
              );
            }
          });
        });
      } catch (e) {
        message.error('获取文件失败！');
      }
    } else {
      message.error('没有配置签名版');
    }
  };
  // 查看签名
  handleCkqm = () => {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'qmb/getSignedPdf',
      payload: {
        businessSystem: 'zfba',
        businessId: wskey,
        clmc: sswsname,
        token
      }
    }).then((res) => {
      if (res && res.code === 1) {
        this.setState({
          ModalVisible: true,
          pdfUrl: res?.data
        });
      } else {
        message.error(res?.msg || '查看签名失败');
      }
    });
  };

  // 关闭查看签名弹窗
  handleCloseCkqm = () => {
    this.setState({
      ModalVisible: false
    });
  };

  // 扫码缴费
  handleSmjf = () => {
    this.props.handleSmjf && this.props.handleSmjf();
  };
  render() {
    const { style: elemStyle } = this.props;
    const { showWsDzqm = true } = this.props;
    const allStyle = {
      ...elemStyle,
      ...{
        height: '48px',
        boxShadow: '0 2px 10px 0 rgba(0,0,0,.15)',
        left: '0',
        top: '0',
        position: 'fixed',
        width: '100%',
        zIndex: '999',
        background: '#fff'
      }
    };
    const { ModalVisible, pdfUrl } = this.state;
    const { location = {} } = this.props;
    const { volTitle = '', query = {} } = location;
    const { type = '' } = query;
    return (
      <div style={allStyle}>
        <div style={{ width: '640px', margin: '0 auto', height: '100%', display: 'flex', justifyContent: 'center' }}>
          {type === 'save' ? (
            ''
          ) : (
            <Button
              type="primary"
              style={{
                marginLeft: '10px',
                alignSelf: 'center',
                float: 'right'
              }}
              onClick={this.handlePrintReport}
            >
              {volTitle ? '上传' : `打印`}
            </Button>
          )}
          {showWsDzqm && (
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="0">
                    <Button type="primary" onClick={() => this.handleQm()}>
                      电子签名
                    </Button>
                  </Menu.Item>
                  <Menu.Item key="1">
                    <Button type="primary" onClick={() => this.handleCkqm()}>
                      查看签名文件
                    </Button>
                  </Menu.Item>
                </Menu>
              }
              placement="bottomCenter"
            >
              <Button
                style={{
                  marginLeft: '10px',
                  alignSelf: 'center',
                  float: 'right'
                }}
                onClick={this.handleQm}
              >
                电子签名
              </Button>
            </Dropdown>
          )}
        </div>
        <Modal
          title="查看签名"
          visible={ModalVisible}
          width="100%"
          onCancel={this.handleCloseCkqm}
          onOk={this.handleCloseCkqm}
          footer={null}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <PDFView pdfUrl={`${pdfUrl}`} />
            <Button type="primary" onClick={this.handleCloseCkqm}>
              关闭
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}

export default ToolbarCommon;
