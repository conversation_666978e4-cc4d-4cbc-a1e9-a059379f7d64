import React, { Component } from 'react';
import ContentEditable from 'react-contenteditable';
import wsStyle from './wsInput.less';

class FillingWsInput extends Component {
  constructor(props) {
    super(props);
    this.contentEditable = React.createRef();
    const value = props.value || '';
    this.state = {
      textValue: this.makeText(value),
      style: props.style || {},
      scaleTime: 0
    };
  }

  componentWillReceiveProps(nextProps) {
    const { style: elmStyle, pageType, id } = nextProps;
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { lineHeight, fontSize } = elmStyle;
      // const minLineHeight = parseInt(lineHeight) / 2;
      const widgetWidth = parseInt(elmStyle.width);
      const widgetHeight = parseInt(elmStyle.height);
      const nowTextValue = nextProps.value;
      const wsfontSize = parseInt(fontSize);

      const editWidth = this.contentEditable.current.offsetWidth;
      const editHight = this.contentEditable.current.offsetHeight;

      // console.info("editWidth==> id="+id,editWidth,widgetWidth);

      if (nowTextValue === '' || nowTextValue === undefined) {
        this.setState({ textValue: nowTextValue });
        // return { textValue: nowTextValue };
      }

      // 如果编辑的文本大于容器的宽度 就缩小字体
      if (parseInt(editWidth) >= widgetWidth - 10) {
        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize / 2}pt`,
          lineHeight: `${parseInt(lineHeight) / 2}px`
        };
        this.setState({ textValue: nowTextValue, style });
      } else if (parseInt(editWidth) < widgetWidth - 40) {
        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize}pt`,
          lineHeight: `${parseInt(lineHeight)}px`
        };
        this.setState({ textValue: nowTextValue, style });
      } else {
        this.setState({ textValue: nowTextValue });
      }

      if (parseInt(editHight) > widgetHeight) {
        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize / 2}pt`,
          lineHeight: `${parseInt(lineHeight) / 4}px`
        };
        this.setState({ style });
      }
    }
  }

  componentDidMount() {
    const { style: elmStyle, pageType, id } = this.props;
    // Should be a controlled component.
    if ('value' in this.props) {
      const { lineHeight, fontSize } = elmStyle;
      // const minLineHeight = parseInt(lineHeight) / 2;
      const widgetWidth = parseInt(elmStyle.width);
      const widgetHeight = parseInt(elmStyle.height);
      const nowTextValue = this.props.value;
      const wsfontSize = parseInt(fontSize);

      const editWidth = this.contentEditable.current.offsetWidth;
      const editHight = this.contentEditable.current.offsetHeight;

      console.info(`editWidth==> id=${id}`, editWidth, widgetWidth, parseInt(editWidth) >= widgetWidth - 10);

      if (nowTextValue === '' || nowTextValue === undefined) {
        this.setState({ textValue: nowTextValue });
        // return { textValue: nowTextValue };
      }

      // 如果编辑的文本大于容器的宽度 就缩小字体
      if (parseInt(editWidth) >= widgetWidth - 10) {
        console.info('我进来了');

        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize / 2}pt`,
          lineHeight: `${parseInt(lineHeight) / 2}px`
        };
        this.setState({ textValue: nowTextValue, style });
      } else if (parseInt(editWidth) < widgetWidth - 40) {
        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize}pt`,
          lineHeight: `${parseInt(lineHeight)}px`
        };
        this.setState({ textValue: nowTextValue, style });
      } else if (parseInt(editHight) > widgetHeight) {
      } else {
        this.setState({ textValue: nowTextValue });
      }

      if (parseInt(editHight) > widgetHeight) {
        const style = {
          ...elmStyle,
          fontSize: `${wsfontSize / 2}pt`,
          lineHeight: `${parseInt(lineHeight) / 4}px`
        };
        this.setState({ style });
      }
    }
  }

  makeText = (text) => {
    return `<span>${text}</span>`;
  };

  handleChange = (evt) => {
    const {
      target: { value }
    } = evt;
    const { onChange } = this.props;
    const reg = /<[^<>]+>/g;
    const realValue = value.replace(reg, '');
    if (onChange) {
      onChange(this.trimSpaces(realValue));
    }
  };
  trimSpaces = (string) => {
    return string
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<');
  };

  pasteAsPlainText = (event) => {
    event.preventDefault();
    const text = event.clipboardData.getData('text/plain');
    document.execCommand('insertHTML', false, text);
  };

  disableNewlines = (event) => {
    const keyCode = event.keyCode || event.which;

    if (keyCode === 13) {
      event.returnValue = false;
      if (event.preventDefault) event.preventDefault();
    }
  };

  highlightAll = () => {
    setTimeout(() => {
      document.execCommand('selectAll', false, null);
    }, 0);
  };

  render() {
    const { style } = this.state;
    // const { top, left, ...restprops } = style;
    const { id, editable, prefixText, pageType } = this.props;
    return (
      <span
        textname={id}
        style={{
          ...{
            zIndex: '1000',
            borderBottom: editable || pageType === 'filed' ? '1px solid #000' : 'none'
          },
          ...style
        }}
      >
        {prefixText ? <span>{prefixText}</span> : ''}
        <ContentEditable
          id={id}
          className={wsStyle.wsInput}
          onFocus={this.highlightAll}
          onKeyPress={this.disableNewlines}
          onPaste={this.pasteAsPlainText}
          placeholder="请输入 "
          innerRef={this.contentEditable}
          html={this.state.textValue ? this.state.textValue.toString() : ''} // innerHTML of the editable div
          disabled={!editable} // use true to disable editing
          onChange={this.handleChange} // handle innerHTML change
          tagName="span" // Use a custom HTML tag (uses a div by default)
        />
      </span>
    );
  }
}

export default FillingWsInput;
