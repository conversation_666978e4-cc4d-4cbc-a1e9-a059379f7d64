/**
 * Created by PJQ on 2019-05-08
 * 功能描述：页面布局--顶栏展示组件
 * 属性说明：
 *  title：标题
 *  logo：左侧图标;
 *  extra: 右侧内容
 */
import React, { PureComponent } from 'react';
import styles from './TopBar.less';

class TopBar extends PureComponent {
  render() {
    const { title = '', logo = '', extra = '', style = {} } = this.props;
    return (
      <div className={styles.topBar} style={style}>
        <div className={styles.logo}>{logo}</div>
        <div className={styles.title}>{title}</div>
        <div className={styles.extra}>{extra}</div>
      </div>
    );
  }
}

export default TopBar;
