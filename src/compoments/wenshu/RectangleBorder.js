import React, { Component } from 'react';
import styles from './rectangleBorder.less';

class RectangleBorder extends Component {
  render() {
    const { footText, borderLines, pageStyle } = this.props;

    const pageHeight = pageStyle.height || '953px';
    const secPageHeight = pageStyle.height ? `${parseInt(pageStyle.height) - 16}px` : '938px';

    return (
      <div id="borderComponent" className={styles.borderComponent}>
        <div
          datatype="doubleBorderComponent"
          style={{
            display: borderLines === 2 ? 'block' : 'none',
            height: footText ? pageHeight : '993px'
          }}
          className={styles.doubleBorderComponent}
        >
          <div className={styles.minBorder} style={{ height: footText ? secPageHeight : '978px' }} />
        </div>

        <div
          datatype="singleBorderComponent"
          style={{
            display: borderLines === 1 ? 'block' : 'none',
            height: footText ? pageHeight : '993px'
          }}
          className={styles.singleBorderComponent}
        />
        <div style={{ display: footText ? 'block' : 'none' }} className={styles.footText}>
          {footText}
        </div>
      </div>
    );
  }
}

export default RectangleBorder;
