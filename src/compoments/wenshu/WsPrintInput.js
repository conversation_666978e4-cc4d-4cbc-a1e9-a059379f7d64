import React, { Component } from 'react';
import ContentEditable from 'react-contenteditable';
import wsStyle from './wsInput.less';

class WsInput extends Component {
  static getDerivedStateFromProps(nextProps) {
    const { style: elmStyle = {}, pageType, editable } = nextProps;
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { lineHeight } = elmStyle;
      const minLineHeight = parseInt(lineHeight) / 2;
      const widgetWidth = parseInt(elmStyle.width);
      const nowTextValue = nextProps.value;
      const fontSizePixel = 22;
      // if(elmStyle.fontSize ===`16pt`){
      //   fontSizePixel = 22;
      // }else if(elmStyle.fontSize ===`14pt`){
      //   fontSizePixel = 19;
      // }
      // 如果是叙述式，则直接返回，不用计算长度
      if (pageType === 'narrative' || !editable) {
        return { textValue: nowTextValue || '' };
      } else if (!nowTextValue) {
        return {
          ...{ textValue: '    ' }
        };
      }
      // 原始字体大小为16pt 即每个字节长度为22,当当前的字节长度长过了容器长度则需要缩小字体再算一遍
      else if (nowTextValue.length * fontSizePixel > widgetWidth && nowTextValue.length * 19 < widgetWidth * 2) {
        nextProps = {
          ...nextProps,
          style: { ...elmStyle, fontSize: '14pt', lineHeight: `${minLineHeight}px` }
        };
        return {
          ...{ textValue: nextProps.value, style: nextProps.style }
        };
      } else if (nowTextValue.length * fontSizePixel > widgetWidth * 2) {
        return null;
      } else if (nowTextValue.length * fontSizePixel < widgetWidth) {
        return {
          ...{ textValue: nextProps.value }
        };
      } else {
        return null;
      }
    } else if (pageType === 'Narrative') {
      return { textValue: '    ' };
    }
  }

  constructor(props) {
    super(props);
    this.contentEditable = React.createRef();
    const value = props.value || '';
    this.state = {
      textValue: this.makeText(value),
      style: props.style || {},
      scaleTime: 0
    };
  }

  componentDidMount() {}

  makeText = (text) => {
    return `<span style="display: inline-block;padding: 0 6px;">${text}</span>`;
  };

  calculatePerFontSize = (fontSize) => {
    if (fontSize === `16pt`) {
      return 22;
    } else if (fontSize === `14pt`) {
      return 19;
    }
  };

  handleChange = (evt) => {
    const {
      target: { value }
    } = evt;
    const { onChange } = this.props;
    const reg = /<[^<>]+>/g;
    const realValue = value.replace(reg, '');
    if (onChange) {
      onChange(this.trimSpaces(realValue));
    }
    // const { offsetWidth: pwidth } = currentTarget;
    // const perFontSize = this.calculatePerFontSize(this.state.style.fontSize);
    // // 如果 文字长度超过容器宽度，则缩小字体并减小行间距
    // if (perFontSize * realValue.length > pwidth && this.state.scaleTime === 0) {
    //   const minLineHeight = parseInt(this.state.style.lineHeight) / 2;
    //   const minFontSize = parseInt(this.state.style.fontSize) - 2;
    //   this.setState({
    //     scaleTime: 1,
    //     textValue: this.makeText(realValue),
    //     style: {
    //       ...this.state.style,
    //       ...{ lineHeight: `${minLineHeight}px`, fontSize: `${minFontSize}pt` },
    //     },
    //   });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else if (perFontSize * realValue.length < pwidth * 2 && this.state.scaleTime === 1) {
    //   //当已变成两行但没超过两行
    //   this.setState({ scaleTime: 1, textValue: this.makeText(realValue) });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else if (this.state.scaleTime === 0) {
    //   this.setState({ textValue: this.makeText(realValue) });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else {
    //   this.setState({ textValue: this.state.textValue });
    //   message.warning('字数超过限制!');
    // }
  };
  trimSpaces = (string) => {
    return string
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<');
  };

  pasteAsPlainText = (event) => {
    event.preventDefault();
    const text = event.clipboardData.getData('text/plain');
    document.execCommand('insertHTML', false, text);
  };

  disableNewlines = (event) => {
    const keyCode = event.keyCode || event.which;

    if (keyCode === 13) {
      event.returnValue = false;
      if (event.preventDefault) event.preventDefault();
    }
  };

  highlightAll = () => {
    setTimeout(() => {
      document.execCommand('selectAll', false, null);
    }, 0);
  };

  render() {
    const { style } = this.state;
    // const { top, left, ...restprops } = style;
    const { id, editable, prefixText, pageType, placeholder } = this.props;
    const emptyText = this.props.emptyText ? this.props.emptyText : '  ';
    return (
      <span
        style={{
          ...{ zIndex: '1000', borderBottom: editable || pageType === 'filed' ? '1px solid #000' : 'none' },
          ...style
        }}
      >
        {prefixText ? <span>{prefixText}</span> : ''}
        <ContentEditable
          id={id}
          className={wsStyle.wsInput}
          onFocus={this.highlightAll}
          onKeyPress={this.disableNewlines}
          onPaste={this.pasteAsPlainText}
          placeholder={`请输入${placeholder}`}
          innerRef={this.contentEditable}
          html={this.state.textValue.toString() || emptyText} // innerHTML of the editable div
          disabled={!editable} // use true to disable editing
          onChange={this.handleChange} // handle innerHTML change
          tagName="span" // Use a custom HTML tag (uses a div by default)
        />
      </span>
    );
  }
}

export default WsInput;
