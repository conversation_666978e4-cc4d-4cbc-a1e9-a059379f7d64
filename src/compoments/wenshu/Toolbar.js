import React, { Component } from 'react';
import { But<PERSON>, message, Slider, Tooltip, Dropdown, <PERSON>u, Modal } from 'antd';
import { io } from 'SinoGear';
import { config } from '../../common/config';
import PDFView from '@/compoments/wenshu/PDFView';

class Toolbar extends Component {
  state = {
    ModalVisible: false, // 查看签名弹窗
    pdfUrl: '' // 查看签名pdf
  };
  handleScaleReport = (type) => {
    this.props.handleScaleReport && this.props.handleScaleReport(type);
  };

  onSliderChange = (value) => {
    this.props.onSliderChange && this.props.onSliderChange(value);
  };

  handlePrintReport = () => {
    if (this.props.wsData && this.props.wsData.wscode) {
      const {
        location: { query, volTitle = '' },
        wsData: { wscode, confirmnote = '' }
      } = this.props;
      const { sswsname, token } = query;
      if (config?.enablePrintLimit && !volTitle) {
        io.get(`${config.contextPath}/api/ws/b_asj_ws_sswsconfigs/getSswsFlag/wscode/${wscode}/wsmc/${sswsname}`, {
          headers: { Authorization: token }
        }).then((res) => {
          if (res?.data?.sswsflag === 'DYWS' || confirmnote) {
            this.props.handlePrintReport && this.props.handlePrintReport();
          } else {
            message.warn('文书审批结束才可以打印');
          }
        });
      } else {
        this.props.handlePrintReport && this.props.handlePrintReport();
      }
    } else {
      this.props.handlePrintReport && this.props.handlePrintReport();
    }

    // this.props.handlePrintReport && this.props.handlePrintReport();
  };

  handleGenWSZH = () => {
    this.props.handleGenWSZH && this.props.handleGenWSZH();
  };

  handlePreviewReport = () => {
    this.props.handlePreviewReport && this.props.handlePreviewReport();
  };

  handleSave = () => {
    this.props.handleSaveForm && this.props.handleSaveForm();
  };

  // 电子签名
  handleQm = () => {
    // this.openModal();
    this.props.handleQm && this.props.handleQm();
  };
  // 查看签名
  handleCkqm = () => {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'qmb/getSignedPdf',
      payload: {
        businessSystem: 'zfba',
        businessId: wskey,
        clmc: sswsname,
        token
      }
    }).then((res) => {
      if (res && res.code === 1) {
        this.setState({
          ModalVisible: true,
          pdfUrl: res?.data
        });
      } else {
        message.error(res?.msg || '查看签名失败');
      }
    });
  };

  // 关闭查看签名弹窗
  handleCloseCkqm = () => {
    this.setState({
      ModalVisible: false
    });
  };

  render() {
    const { ModalVisible, pdfUrl } = this.state;
    const { style: elemStyle } = this.props;
    const allStyle = {
      ...elemStyle,
      ...{
        height: '48px',
        boxShadow: '0 2px 10px 0 rgba(0,0,0,.15)',
        left: '0',
        top: '0',
        position: 'fixed',
        width: '100%',
        zIndex: '999',
        background: '#fff'
      }
    };

    const { location = {}, showWsDzqm = true } = this.props;
    const { volTitle = '', query = {} } = location;
    const { type = '' } = query;
    return (
      <div style={allStyle}>
        <div style={{ width: '640px', margin: '0 auto', height: '100%', display: 'flex' }}>
          <Tooltip title="缩小">
            <Button
              style={{ alignSelf: 'center', height: '28px', width: '28px' }}
              type="primary"
              shape="circle"
              icon="minus"
              onClick={this.handleScaleReport.bind(this, 'minus')}
            />
          </Tooltip>
          <Slider
            onChange={this.onSliderChange}
            style={{ width: '120px', alignSelf: 'center' }}
            value={this.props.scale}
          />

          <Tooltip title="放大">
            <Button
              onClick={this.handleScaleReport.bind(this, 'plus')}
              type="primary"
              shape="circle"
              icon="plus"
              style={{ marginLeft: '12px', alignSelf: 'center', height: '28px', width: '28px' }}
            />
          </Tooltip>
          {type === 'save' ? (
            <Button style={{ alignSelf: 'center', marginLeft: '320px' }} type="primary" onClick={this.handleSave}>
              保存
            </Button>
          ) : (
            <Button style={{ alignSelf: 'center', marginLeft: '180px' }} type="primary" htmlType="submit">
              保存
            </Button>
          )}
          {type === 'save' ? (
            ''
          ) : (
            <Button
              type="primary"
              ghost
              onClick={this.handleGenWSZH}
              style={{ marginLeft: '10px', alignSelf: 'center', float: 'right' }}
            >
              生成文书字号
            </Button>
          )}
          {type === 'save' ? (
            ''
          ) : (
            <Button
              type="primary"
              style={{
                marginLeft: '10px',
                alignSelf: 'center',
                float: 'right'
              }}
              onClick={this.handlePrintReport}
            >
              {volTitle ? '上传' : `打印`}
            </Button>
          )}
          {showWsDzqm && (
            <Dropdown
              overlay={
                <Menu>
                  <Menu.Item key="0">
                    <Button type="primary" onClick={() => this.handleQm()}>
                      电子签名
                    </Button>
                  </Menu.Item>
                  <Menu.Item key="1">
                    <Button type="primary" onClick={() => this.handleCkqm()}>
                      查看签名文件
                    </Button>
                  </Menu.Item>
                </Menu>
              }
              placement="bottomCenter"
            >
              <Button
                style={{
                  marginLeft: '10px',
                  alignSelf: 'center',
                  float: 'right'
                }}
                onClick={this.handleQm}
              >
                电子签名
              </Button>
            </Dropdown>
          )}
        </div>
        <Modal
          title="查看签名"
          visible={ModalVisible}
          width="100%"
          onCancel={this.handleCloseCkqm}
          onOk={this.handleCloseCkqm}
          footer={null}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <PDFView pdfUrl={`${pdfUrl}`} />
            <Button type="primary" onClick={this.handleCloseCkqm}>
              关闭
            </Button>
          </div>
        </Modal>
      </div>
    );
  }
}

export default Toolbar;
