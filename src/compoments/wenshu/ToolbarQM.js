import React, { Component } from 'react';
import { But<PERSON>, message, <PERSON>lider, Tooltip } from 'antd';
import { io } from 'SinoGear';
import { config } from '../../common/config';

class Toolbar extends Component {
  state = {
    qmText: '',
    isqm: false
  };

  componentDidMount() {
    const {
      location: { query }
    } = this.props;

    const { token } = query;

    io.get(`${config.contextPath}/api/loginInfo`, {
      headers: { Authorization: token }
    }).then((res) => {
      const userInfo = res.loginUserDTO;
      // const { openQMDS = '' } = config; // 开启签名的地市
      const { dept = '' } = userInfo;
      io.get(`${config.contextPath}/api/b_asj_ws_qm_config/${dept}`, { headers: { Authorization: token } }).then(
        (deptList) => {
          let isQM = false;
          if (deptList && deptList.length) {
            // const isQM = openDSQMList.find((value) => dept.indexOf(value) > -1);
            isQM = true;
          }
          const qmText = isQM ? '签名打印' : '打印';
          const isqm = isQM;
          console.log('isqm 该民警所在单位是否需要签名', isqm);
          this.setState({
            qmText,
            isqm
          });
        }
      );
    });
  }

  handleScaleReport = (type) => {
    this.props.handleScaleReport && this.props.handleScaleReport(type);
  };

  onSliderChange = (value) => {
    this.props.onSliderChange && this.props.onSliderChange(value);
  };

  handlePrintReport = () => {
    if ( this.props.wsData && this.props.wsData.wscode){
      const {
        location: { query },
        wsData: { wscode, confirmnote = '' }
      } = this.props;
      const { sswsname, token } = query;
      if (config?.enablePrintLimit) {
        io.get(`${config.contextPath}/api/ws/b_asj_ws_sswsconfigs/getSswsFlag/wscode/${wscode}/wsmc/${sswsname}`, {
          headers: { Authorization: token }
        }).then((res) => {
          if (res?.data?.sswsflag === 'DYWS' || confirmnote) {
            this.props.handlePrintReport && this.props.handlePrintReport(this.state.isqm);
          } else {
            message.warn('文书审批结束才可以打印');
          }
        });
      } else {
        this.props.handlePrintReport && this.props.handlePrintReport(this.state.isqm);
      }
    }else {
      this.props.handlePrintReport && this.props.handlePrintReport(this.state.isqm);
    }

  };

  handlePrintReport2 = () => {
    if ( this.props.wsData && this.props.wsData.wscode){
      const {
        location: { query, volTitle = '' },
        wsData: { wscode, confirmnote = '' }
      } = this.props;
      const { sswsname, token } = query;
      if (config?.enablePrintLimit && !volTitle) {
        io.get(`${config.contextPath}/api/ws/b_asj_ws_sswsconfigs/getSswsFlag/wscode/${wscode}/wsmc/${sswsname}`, {
          headers: { Authorization: token }
        }).then((res) => {
          if (res?.data?.sswsflag === 'DYWS' || confirmnote) {
            this.props.handlePrintReport && this.props.handlePrintReport(false); // 不需要签名
          } else {
            message.warn('文书审批结束才可以打印');
          }
        });
      } else {
        this.props.handlePrintReport && this.props.handlePrintReport(false); // 不需要签名
      }
    }else {
      this.props.handlePrintReport && this.props.handlePrintReport(false); // 不需要签名
    }

    // this.props.handlePrintReport && this.props.handlePrintReport(false); // 不需要签名
  };

  handleGenWSZH = () => {
    this.props.handleGenWSZH && this.props.handleGenWSZH();
  };

  handlePreviewReport = () => {
    this.props.handlePreviewReport && this.props.handlePreviewReport();
  };

  handleSave = () => {
    this.props.handleSaveForm && this.props.handleSaveForm();
  };

  render() {
    const { style: elemStyle } = this.props;
    const allStyle = {
      ...elemStyle,
      ...{
        height: '48px',
        boxShadow: '0 2px 10px 0 rgba(0,0,0,.15)',
        left: '0',
        top: '0',
        position: 'fixed',
        width: '100%',
        zIndex: '999',
        background: '#fff'
      }
    };

    const { location: query = {} } = this.props;
    const {
      volTitle,
      query: { type }
    } = query;

    return (
      <div style={allStyle}>
        <div style={{ width: '640px', margin: '0 auto', height: '100%', display: 'flex' }}>
          <Tooltip title="缩小">
            <Button
              style={{ alignSelf: 'center', height: '28px', width: '28px' }}
              type="primary"
              shape="circle"
              icon="minus"
              onClick={this.handleScaleReport.bind(this, 'minus')}
            />
          </Tooltip>
          <Slider
            onChange={this.onSliderChange}
            style={{ width: '120px', alignSelf: 'center' }}
            value={this.props.scale}
          />

          <Tooltip title="放大">
            <Button
              onClick={this.handleScaleReport.bind(this, 'plus')}
              type="primary"
              shape="circle"
              icon="plus"
              style={{ marginLeft: '12px', alignSelf: 'center', height: '28px', width: '28px' }}
            />
          </Tooltip>
          {type === 'save' ? (
            <Button style={{ alignSelf: 'center', marginLeft: '320px' }} type="primary" onClick={this.handleSave}>
              保存
            </Button>
          ) : (
            <Button style={{ alignSelf: 'center', marginLeft: '180px' }} type="primary" htmlType="submit">
              保存
            </Button>
          )}
          {type === 'save' ? (
            ''
          ) : (
            <Button
              type="primary"
              ghost
              onClick={this.handleGenWSZH}
              style={{ marginLeft: '10px', alignSelf: 'center', float: 'right' }}
            >
              生成文书字号
            </Button>
          )}
          {type === 'save' ? (
            ''
          ) : (
            <>
              <Button
                type="primary"
                style={{
                  marginLeft: '10px',
                  alignSelf: 'center',
                  float: 'right'
                }}
                onClick={this.handlePrintReport2}
              >
                {volTitle ? '上传' : '打印'}
              </Button>
              <Button
                type="primary"
                style={{
                  marginLeft: '10px',
                  alignSelf: 'center',
                  float: 'right',
                  display: `${this.state.isqm ? '' : 'none'}`
                }}
                onClick={this.handlePrintReport}
              >
                {this.state.qmText}
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }
}

export default Toolbar;
