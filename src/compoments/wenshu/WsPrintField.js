import React, { Component } from 'react';
import ContentEditable from 'react-contenteditable/lib/react-contenteditable';
import wsStyle from './wsInput.less';
class WsPrintField extends Component {
  static getDerivedStateFromProps(nextProps) {
    let { style: elmStyle, pageType, editable } = nextProps;
    // Should be a controlled component.
    if ('value' in nextProps) {
      const { lineHeight } = elmStyle;
      const minLineHeight = parseInt(lineHeight) / 2;
      const widgetWidth = parseInt(elmStyle.width);
      const nowTextValue = nextProps.value;
      let fontSizePixel = 22;
      // if(elmStyle.fontSize ===`16pt`){
      //   fontSizePixel = 22;
      // }else if(elmStyle.fontSize ===`14pt`){
      //   fontSizePixel = 19;
      // }
      // 如果是叙述式，则直接返回，不用计算长度
      if (pageType === 'narrative' || !editable) {
        return { textValue: nowTextValue || '' };
      } else if (!nowTextValue) {
        return {
          ...{ textValue: '    ' },
        };
      }
      // 原始字体大小为16pt 即每个字节长度为22,当当前的字节长度长过了容器长度则需要缩小字体再算一遍
      else if (
        nowTextValue.length * fontSizePixel > widgetWidth &&
        nowTextValue.length * 19 < widgetWidth * 2
      ) {
        nextProps = {
          ...nextProps,
          style: { ...elmStyle, fontSize: '14pt', lineHeight: `${minLineHeight}px` },
        };
        return {
          ...{ textValue: nextProps.value, style: nextProps.style },
        };
      } else if (nowTextValue.length * fontSizePixel > widgetWidth * 2) {
        return null;
      } else if (nowTextValue.length * fontSizePixel < widgetWidth) {
        return {
          ...{ textValue: nextProps.value },
        };
      } else {
        return null;
      }
    } else {
      if (pageType === 'Narrative') {
        return { textValue: '    ' };
      }
    }
  }

  constructor(props) {
    super(props);
    this.contentEditable = React.createRef();
    const value = props.value || '';
    this.state = {
      textValue: this.makeText(value),
      style: props.style || {},
      scaleTime: 0,
    };
  }

  componentDidMount() {}

  makeText = text => {
    return `<span>${text}</span>`;
  };

  calculatePerFontSize = fontSize => {
    if (fontSize === `16pt`) {
      return 22;
    } else if (fontSize === `14pt`) {
      return 19;
    }
  };

  handleChange = evt => {
    const {
      target: { value },
    } = evt;
    const { onChange } = this.props;

    let reg = /<[^<>]+>/g;
    const realValue = value.replace(reg, '');
    if (onChange) {
      onChange(this.trimSpaces(realValue));
    }
    // const { offsetWidth: pwidth } = currentTarget;
    // const perFontSize = this.calculatePerFontSize(this.state.style.fontSize);
    // // 如果 文字长度超过容器宽度，则缩小字体并减小行间距
    // if (perFontSize * realValue.length > pwidth && this.state.scaleTime === 0) {
    //   const minLineHeight = parseInt(this.state.style.lineHeight) / 2;
    //   const minFontSize = parseInt(this.state.style.fontSize) - 2;
    //   this.setState({
    //     scaleTime: 1,
    //     textValue: this.makeText(realValue),
    //     style: {
    //       ...this.state.style,
    //       ...{ lineHeight: `${minLineHeight}px`, fontSize: `${minFontSize}pt` },
    //     },
    //   });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else if (perFontSize * realValue.length < pwidth * 2 && this.state.scaleTime === 1) {
    //   //当已变成两行但没超过两行
    //   this.setState({ scaleTime: 1, textValue: this.makeText(realValue) });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else if (this.state.scaleTime === 0) {
    //   this.setState({ textValue: this.makeText(realValue) });
    //   if (onChange) {
    //     onChange(this.trimSpaces(realValue));
    //   }
    // } else {
    //   this.setState({ textValue: this.state.textValue });
    //   message.warning('字数超过限制!');
    // }
  };
  trimSpaces = string => {
    return string
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&gt;/g, '>')
      .replace(/&lt;/g, '<');
  };


  render() {
    const { style } = this.state;

    const { id } = this.props;
    return (

      <ContentEditable
        id={id}
        className={wsStyle.wsInput}
        style={{
          ...style,
          ...{ zIndex: '1000' },
        }}
        innerRef={this.contentEditable}
        html={this.state.textValue} // innerHTML of the editable div
        disabled={true} // use true to disable editing
        onChange={this.handleChange} // handle innerHTML change
        tagName="span" // Use a custom HTML tag (uses a div by default)
      />
    );
  }
}

export default WsPrintField;
