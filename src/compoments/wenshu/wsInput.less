[contenteditable=true]:empty:after {
  content: attr(placeholder);
  display: inline;
  color: #aaa;
  font-size: 12pt;
}

//[contenteditable=true]

.blinkStyle:empty:before {
  content: '|';
  height: 21px;
  //background: #000;
  animation: blink  1s infinite;
  display: inline-block;
}

@keyframes blink {
  from {
    visibility:hidden;
  }
  50% {
    visibility:hidden;
  }
  to {
    visibility:visible;
  }
}


[contenteditable] {
  outline: 0px solid transparent;
}
.wsInput{
  padding-left: 1px;
  word-break: break-all;
}
