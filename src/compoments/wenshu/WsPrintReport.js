import React, { Component } from 'react';
import { Form } from 'antd';
import reactStringReplace from 'react-string-replace';
import moment from 'moment';
import { QRCodeSVG } from 'qrcode.react';
import uuidv4 from 'uuid/v4';
import wsStyle from '@/compoments/wenshu/WenShuReport.less';
import { pagination } from '@/compoments/wenshu/utils/pagination';
import WsPage from './WsPage';
import WsInput from './WsPrintInput';
import Paragraph from './Paragraph';
import StaticText from './StaticText';
import { calcParagraphSize } from './utils/SizeUtil';
import { isDate } from './utils/CommonUtil';
import spyz from '../../assets/spyz.png';

const dateFormat = 'YYYY年MM月DD日';

@Form.create({
  onFieldsChange: () => {
    calcParagraphSize();
  },
  mapPropsToFields(props) {
    // conditionValue 是 connect中传过来的取值
    const { wsData } = props;
    const keys = Object.keys(wsData);

    if (keys.length) {
      const formData = {};
      for (const key of keys) {
        // formData[key] = Form.createFormField({
        //   ...key,
        //   value: wsData[key]
        // });

        if (key !== 'zdddsj' && isDate(wsData[key])) {
          wsData[key] = Date.parse(
            wsData[key]
              .replace(/[年月]/g, '-')
              .replace(/日/, ' ')
              .replace(/[时分]/g, ':')
              .replace(/秒/, '')
          );
          formData[key] = Form.createFormField({
            ...key,
            value: moment(wsData[key]).format(dateFormat)
          });
        } else {
          formData[key] = Form.createFormField({
            ...key,
            value: wsData[key]
          });
        }
      }
      return formData;
    } else {
      return {};
    }
  }
})
class Index extends Component {
  state = {
    newConfig: null
  };

  componentDidMount() {
    calcParagraphSize();
    const newConfig = pagination(this.props);
    this.setState({
      newConfig
    });
  }

  componentDidUpdate(prevProps, prevState) {
    calcParagraphSize();
  }

  genPageContent = (dataConfig) => {
    const { pages = [], borderLines } = dataConfig;
    const { showPage } = this.props;
    const totalPage = pages.map((item) => {
      const { paragraph, footText, elementStyle, pageStyle } = item;
      const paragraphList = this.genParagraph(paragraph);
      return (
        <WsPage
          key={item.key}
          borderLines={borderLines}
          footText={footText}
          elementStyle={elementStyle}
          pageStyle={pageStyle}
        >
          {paragraphList}
        </WsPage>
      );
    });

    if (showPage && totalPage.length >= parseInt(showPage)) {
      return totalPage[showPage - 1];
    } else {
      return totalPage;
    }
  };

  genParagraph = (paragraphList) => {
    return paragraphList.map((item) => {
      const { style, contents } = item;
      const contentList = contents.map((contentItem) => {
        if (contentItem.type === 'staticText') {
          return this.genStaticText(contentItem);
        } else if (contentItem.type === 'textField') {
          return this.genTextField(contentItem);
        } else if (contentItem.type === 'bigText') {
          return this.genBigText(contentItem);
        } else if (contentItem.type === 'img') {
          return this.genYZimg(contentItem);
        } else if (contentItem.type === 'qrcode') {
          return this.genQrcode(contentItem);
        } else if (contentItem.type === 'zdyImg') {
          return this.genImg(contentItem);
        } else if (contentItem.type === 'HTML') {
          return this.genHTML(contentItem);
        } else {
          return null;
        }
      });
      return (
        <Paragraph key={item.key} style={style}>
          {contentList}
        </Paragraph>
      );
    });
  };

  genQrcode = (qrcodeObject) => {
    const { elementStyle = {} } = qrcodeObject;
    const { formData } = this.props;
    const data = encodeURI(formData.qrcodeData);

    return (
      formData.qrcodeData && (
        <div style={elementStyle}>
          <div>(请扫描此二维码缴纳保证金)</div>
          <QRCodeSVG value={data} size={128} level="H" fgColor="#000000" bgColor="#ffffff" />
        </div>
      )
    );
  };

  genImg = (imgObject) => {
    const { elementStyle = {}, textName = '' } = imgObject;
    const yzstyle = {
      ...elementStyle
      // height: '120pt',
      // width: '120pt',
      // zIndex: '-1'
    };
    const { formData } = this.props;
    return <img style={yzstyle} src={`data:image/jpeg;base64,${formData[textName] || ''}`} alt="" />;
  };

  genYZimg = (imgObject) => {
    const { elementStyle = {} } = imgObject;
    const yzstyle = { ...elementStyle, height: '120pt', width: '120pt', zIndex: '-1' };
    const { formData } = this.props;
    // return <img style={yzstyle} src={formData.spdwyz?`data:image/jpeg;base64,${formData.spdwyz}`:spyz} alt="" />;
    return <img style={yzstyle} src={`data:image/jpeg;base64,${formData.spdwyz || formData.cqdwyz}`} alt="" />;
  };

  genBigText = (bigTextObj) => {
    const { fields, tempStr, elementStyle } = bigTextObj;
    const { wsData } = this.props;
    const { getFieldDecorator } = this.props.form;
    const {
      dataConfig: { wsType }
    } = this.props;
    const changeStr = tempStr
      .replace(/\r\n/g, '<br>')
      .replace(/\n/g, '<br>')
      .replace(/\s/g, '&ensp;');

    const bigTextList = reactStringReplace(changeStr, /<%([^%>]+)?%>/g, (match, i) => {
      let itemComponent;
      const matchField = fields && fields.find((item) => item.textName === match);
      if (matchField) {
        if (matchField.textType === 'list') {
          const wplist = wsData[match.toLowerCase()];
          let wpListNode = '';
          let wpLen = 0;
          if (wplist) {
            wpLen = wplist.length;
            // 判断特征字段超过38个字就让字体变小到8pt
            wpListNode = wplist.map((item, index) => {
              return (
                <tr style={{ breakInside: 'avoid' }} className={wsStyle.wptr} key={item.systemid}>
                  {matchField.listConfig.column.map((row) => {
                    return (
                      <td
                        key={`${row.key}_${index}`}
                        width={row.width}
                        style={{ fontSize: item[row.key] && item[row.key].length > 38 ? '8pt' : '12pt' }}
                      >
                        {row.key === 'index'
                          ? row.rowStartIndex
                            ? row.rowStartIndex + index + 1
                            : index + 1
                          : item[row.key]}
                      </td>
                    );
                  })}

                  {/* <td width="10%">{index + 1}</td> */}
                  {/* <td width="22%">{item.wpmc}</td> */}
                  {/* <td width="16%"> */}
                  {/*  {item.wpsl} */}
                  {/*  {item.jldw} */}
                  {/* </td> */}
                  {/* <td width="62%" style={{ fontSize: item.wptz && item.wptz.length > 38 ? '8pt' : '12pt' }}> */}
                  {/*  {item.wptz} */}
                  {/* </td> */}
                </tr>
              );
            });
          }
          const restLen = 5 - wpLen;
          const restTr = [];
          let restTd = '';
          if (matchField.listConfig.column.length > 4) {
            const tds = [];
            const restLong = matchField.listConfig.column.length - 4;
            for (let j = 0; j < restLong; j++) {
              tds.push(<td />);
            }
            restTd = tds;
          }
          if (restLen > 0) {
            if (!matchField.listConfig.emptyRow) {
              for (let j = 0; j < restLen; j++) {
                if (j === 0) {
                  const trNode = (
                    <tr className={wsStyle.wptr}>
                      <td>以</td>
                      <td>下</td>
                      <td>空</td>
                      <td>白</td>
                      {restTd}
                    </tr>
                  );
                  restTr.push(trNode);
                }
                const trNode = (
                  <tr className={wsStyle.wptr}>
                    <td />
                    <td />
                    <td />
                    <td />
                    {restTd}
                  </tr>
                );
                restTr.push(trNode);
              }
            }
          }

          itemComponent = (
            <table className={wsStyle.table}>
              <tbody>
                <tr>
                  {matchField.listConfig.column.map((item) => {
                    return (
                      <td key={item.key} width={item.width}>
                        {item.name}
                      </td>
                    );
                  })}
                  {/* <td width="10%">编号</td> */}
                  {/* <td width="22%">名称</td> */}
                  {/* <td width="16%">数量</td> */}
                  {/* <td width="62%">特征</td> */}
                </tr>
                {wpListNode}
                {restTr}
              </tbody>
            </table>
          );
        } else if (matchField.customField === true) {
          itemComponent = (
            <span key={matchField.key}>
              {wsData[matchField.textName.toLowerCase()] &&
              `${wsData[matchField.textName.toLowerCase()]}`.indexOf('年') > -1
                ? wsData[matchField.textName.toLowerCase()]
                : matchField.dateFormat && wsData[matchField.textName.toLowerCase()]
                ? moment(wsData[matchField.textName.toLowerCase()]).format(matchField.dateFormat)
                : wsData[matchField.textName.toLowerCase()]}
            </span>
          );
        } else {
          itemComponent = (
            <span key={matchField.key}>
              {getFieldDecorator(matchField.textName.toLowerCase())(
                <WsInput pageType={wsType} style={matchField.elementStyle} {...matchField} />
              )}
            </span>
          );
        }

        if (matchField?.textType === 'HTML') {
          itemComponent = matchField?.element || '没有传入element属性，即dom节点，请检查';
        }

        // const matchField = fields.find((item)=>item.textFiled === match);

        // if (matchField.textType === 'string') {
        //   itemComponent = (
        //     <span key={matchField.key}>
        //       {getFieldDecorator(matchField.textName.toLowerCase())(
        //         <WsInput pageType={wsType} style={matchField.elementStyle} />,
        //       )}
        //     </span>
        //   );
        // } else if (matchField.textType === 'date') {
        //   itemComponent = (
        //     <span key={matchField.key}>
        //       {getFieldDecorator(matchField.textName.toLowerCase())(
        //         <WsInput pageType={wsType} style={matchField.elementStyle} />,
        //       )}
        //     </span>
        //   );
        // } else if (matchField.textType === 'dict') {
        //   itemComponent = (
        //     <span key={matchField.key}>
        //       {getFieldDecorator(matchField.textName.toLowerCase())(
        //         <WsInput pageType={wsType} style={matchField.elementStyle} />,
        //       )}
        //     </span>
        //   );
        // }
      }
      return itemComponent;
    }).map((item) => {
      if (typeof item === 'string') {
        return <span dangerouslySetInnerHTML={{ __html: item }} />;
      } else {
        return item;
      }
    });

    return (
      <div key={uuidv4()} style={elementStyle}>
        {bigTextList}
      </div>
    );
  };

  genStaticText = (staticTextObj) => {
    const { textValue, elementStyle } = staticTextObj;
    return <StaticText key={staticTextObj.key} text={textValue} style={elementStyle} />;
  };

  genHTML = (staticTextObj) => {
    const { element, elementStyle } = staticTextObj;
    return <div style={elementStyle}>{element}</div>;
  };

  genTextField = (textFieldObj) => {
    const { getFieldDecorator } = this.props.form;
    const { textName, elementStyle, textType } = textFieldObj;
    const {
      dataConfig: { wsType }
    } = this.props;
    if (textType === 'string') {
      return getFieldDecorator(textName.toLowerCase())(
        <WsInput
          prefixText={textFieldObj.prefixText || ''}
          key={textFieldObj.key}
          pageType={wsType}
          style={elementStyle}
        />
      );
    } else if (textType === 'date') {
      return getFieldDecorator(textName.toLowerCase())(
        <WsInput key={textFieldObj.key} pageType={wsType} style={elementStyle} />
      );
    }
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        console.log('Received values of form: ', values);
      }
    });
  };

  render() {
    const { dataConfig } = this.props;
    const { newConfig } = this.state;
    return (
      <Form layout="inline" onSubmit={this.handleSubmit}>
        {this.genPageContent(newConfig || dataConfig)}
      </Form>
    );
  }
}

export default Index;
