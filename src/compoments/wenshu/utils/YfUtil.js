import { message } from 'antd';
import { config } from '@/common/config';
import { io } from 'SinoGear';
import moment from 'moment';
import CryptoJS from 'crypto-js';
import request from '../../../utils/request';
import {router} from "umi";

// 初始化以及初始化之后打开PDF
export const getDevId = async (categoryName, adapterName, deviceName, noUseCache, deviceInfo, base64Data, ZB, PageNoStart, query) => {
  const urlStr = 'http://127.0.0.1:7030/device/create';
  const sendStr = {
    categoryName,
    adapterName,
    deviceName,
    noUseCache,
    deviceInfo
  };
  const envalue = Encrypt(JSON.stringify(sendStr));
  try {
    const res1 = await request(urlStr, {
      headers: {
        // 'Content-Type': 'application/x-www-form-urlencode;charset=utf-8',
        // 'user': 'miracle'
      },
      body: envalue,
      method: 'post'
    });
    const devId = Decrypt(res1.Data);
    if (devId.length > 0) {
      message.success('初始化成功！');
      // 打开PDF
      OpenPDFCoreNoCallBack(devId, base64Data, ZB, PageNoStart, query);
    } else {
      message.error('初始化失败！');
    }
  } catch (e) {
    message.error('初始化异常');
  }
};

// 打开PDF
async function OpenPDFCoreNoCallBack(devId, base64Data, ZB, PageNoStart, query) {
  console.log(base64Data, '111111111111111111')
  console.log(ZB, '222222222222222222222')
  console.log(PageNoStart, '3333333333333333333333333')
  const urlStr = 'http://127.0.0.1:7030/device/command';
  const value = DeviceCommand(devId, 'OpenPDFCore', {
    PDFCoreInfo: {
      // PDFbase64数据
      PDFData: base64Data,
      ScreenType: '-1', // 屏幕类型
      FingerType: '-1', // 指纹仪类型
      ShowPreviousStep: false, // 是否显示上一步按钮
      ShowNextStep: false, // 是否显示下一步按钮
      ShowRedo: true, // 是否显示重签/重捺按钮
      ShowInput: true, // 是否显示签字捺印按钮
      IShowSubmit: true, // 是否显示提交按钮
      IShowPrint: true, // 是否显示打印按钮
      ShowExit: true, // 是否显示退出按钮
      IsVideo: true, // 是否录像
      SaveSignatureToFile: true, // 轨迹文件保存
      Tip: '',
      SignDevInfo: {
        PenW: 15, // 笔迹宽度
        PenColor: 'Black', // 签字颜色
        Background: 'White' // 签字板背景颜色
      },
      FingerDevInfo: null, // 捺印设备信息
      ListSignInfo: [
        {
          ImgData: null,
          ImgPath: null,
          Id: 15, // 编号 每次PDF签字不能有重复ID
          ISuccess: false, // 是否签字捺印成功 如果签字成功传入 ImgData 会自动插入PDF中。
          Name: '核对意见', // 名称
          IsSkip: true, // 能否跳过
          SignSize: 2, // 签字大小 1.正常模式 2.长语句模式
          Btntext: '核对意见', // 按钮名称
          Describe: '请输入核对意见', // 描述 语音提示
          ObjType: '0', // 对象类型 0 - 签名；1 - 指纹
          Mode: '1', // 定位方式 1 - 绝对坐标；2 - 隐藏标记；3 - 关键字
          PageNoStart, // 搜索开始页
          PageNoEnd: '0', // 搜索结束页
          LocationOrKeyWord: ZB, // 绝对坐标 或 关键字/标记. 绝对坐标格式为“x,y”；关键字/标记为文本，区分大小写和全角半角，可以对多个标记执行插入，标记间用|分隔，如："x1,y1|x2,y2"。
          OffsetX: '-50', // 非坐标定位后，x方向偏移调整，仅在非绝对坐标模式下有效
          OffsetY: '-20', // 非坐标定位后，x方向偏移调整，仅在非绝对坐标模式下有效
          Zoomfactor: '0.2', // 图像缩放系数
          ImgWidth: 50, // 设置图片插入宽度，如果为0则为图片实际值。
          ImgHeight: 30 // 设置图片插入高度，如果为0则为图片实际值。
        },
        {
          ImgData: null,
          ImgPath: null,
          Id: 12, // 编号 每次PDF签字不能有重复ID
          ISuccess: false, // 是否签字捺印成功 如果签字成功传入 ImgData 会自动插入PDF中。
          Name: '核对意见', // 名称
          SignSize: 1, // 签字大小 1.正常模式 2.长语句模式
          Btntext: '核对意见', // 按钮名称
          Describe: '请输入核对意见', // 描述 语音提示
          ObjType: '1', // 对象类型 0 - 签名；1 - 指纹
          DeviceType: '2',
          Mode: '1', // 定位方式 1 - 绝对坐标；2 - 隐藏标记；3 - 关键字
          PageNoStart, // 搜索开始页
          PageNoEnd: '0', // 搜索结束页
          Orientation: '3',
          LocationOrKeyWord: ZB, // 绝对坐标 或 关键字/标记. 绝对坐标格式为“x,y”；关键字/标记为文本，区分大小写和全角半角，可以对多个标记执行插入，标记间用|分隔，如："x1,y1|x2,y2"。
          OffsetX: '-50', // 非坐标定位后，x方向偏移调整，仅在非绝对坐标模式下有效
          OffsetY: '-20', // 非坐标定位后，x方向偏移调整，仅在非绝对坐标模式下有效
          Zoomfactor: '0.2', // 图像缩放系数
          StreamType: '1',
          ImgWidth: 50, // 设置图片插入宽度，如果为0则为图片实际值。
          ImgHeight: 30 // 设置图片插入高度，如果为0则为图片实际值。
        }
      ]
    }
  });
  const res = await request(urlStr, {
    dataType: 'json',
    body: value,
    method: 'post'
  });
  if (res) {
    const PDFdata = JSON.parse(res.Data);
    // status -1 上一步， -2 提交， -4 下一步
    if (PDFdata && PDFdata.status === -2) {
      const signImage = PDFdata.data[0].ImgData; // 签名图片
      const PointData = PDFdata.data[0].PointData; // 签名轨迹
      const fingerPrintImage = PDFdata.data[1].ImgData; // 指纹图片
      const signedPdf = PDFdata.pdfdata; // 签名之后PDF
      const videofilepath = PDFdata.videofilepath; // 签名视频
      // 上传数据到S3
      S3Upload(base64Data, signedPdf, signImage, fingerPrintImage, PointData, videofilepath, query);
    }
  }
}
// 签名完成之后证据上传
async function S3Upload(originPdf, signedPdf, signImage, fingerPrintImage, PointData, videofilepath, query) {
  const urlStr = `${config.qmbContextPath}/api/evidence/S3Upload/String`;
  const data = {
    originPdf,
    signedPdf,
    signImage: [`${signImage}`],
    fingerPrintImage: [`${fingerPrintImage}`],
    xml: [`${PointData}`],
  };
  const res = await io.post(urlStr, data);
  if (res && res.code === 1) {
    const fingerPrintImageUrl = res?.data?.fingerPrintImage?.join(','); // 指纹图片路径
    const originPdfUrl = res?.data.originPdf; // 原pdf路径
    const signImageUrl = res?.data.signImage?.join(','); // 签名图片路径
    const signedPdfUrl = res?.data?.signedPdf; // 签名pdf路径
    const xml = res?.data.xml?.join(','); // 签名轨迹
    const video = videofilepath; // 签名视频
    // 文件以及视频上传到签名捺印的接口
    evidencesUpload(signedPdf, originPdfUrl, signedPdfUrl, signImageUrl, fingerPrintImageUrl, xml, video, query);
  } else {
    message.error('证据上传失败');
  }
}
// 文件以及视频上传到签名捺印的接口
async function evidencesUpload(signedPdf, originPdfUrl, signedPdfUrl, signImageUrl, fingerPrintImageUrl, xml, video, query) {
  const { token, wskey, ajbh, sswsname, dxbh, ajmc } = query;
  const userInfo = await request(`${config.contextPath}/api/loginInfo?access_token=${token.split(' ')[1]}`);
  if (userInfo) {
    const data = {
      businessSystem: 'zfba',
      businessId: wskey,
      businessType: '01',
      ajbh,
      ajmc,
      clmc: sswsname,
      zxdx: dxbh || '',
      uploaderJh: userInfo?.loginUserDTO?.jh, // 上传者警号
      uploader: userInfo?.loginUserDTO?.name, // 上传者姓名
      uploaderDept: userInfo?.loginUserDTO?.workdept, // 上传者单位
      departmentCode: userInfo?.loginUserDTO?.dept,
      uploaderTime: moment.now(),
      serialNumber: 'O2SPDFCoreAdapter', // 设备序列号
      originalPdf: originPdfUrl, // 原始pdf文件存储路径
      signedPdf: signedPdfUrl, // 签名后pdf存储路径
      signName: signImageUrl, // 签名图片存储路径
      fingerPrint: fingerPrintImageUrl, // 指纹图片存储路径
      handwriting: xml, // 签名笔迹存储路径
      signVideo: video, // 签名视频存储路径
      token
    };
    const options = {
      headers: {
        Authorization: token
      }
    };
    const res = await io.post(`${config.qmbContextPath}/api/evidence/upload/v1`, data, options);
    if (res) {
      // toFinish(signedPdf, query);
    }
  } else {
    message.error('获取信息失败');
  }
}

// 跳转到签名完成页面
function toFinish(pdfUrl, query) {
  const { wskey, token, ajbh, sswsname } = query;
  sessionStorage.setItem('pdfUrl', pdfUrl);
  router.push({
    pathname: `/qmfinish`,
    query: {
      wskey,
      token,
      ajbh,
      sswsname
    }
  });
  // setTimeout(() => window.location.reload(), 200);
}

// 整理传递字符串格式
function DeviceCommand(deviceID, commandName, paramter) {
  const paramters = [];
  let index = 0;
  for (const key in paramter) {
    const value = paramter[key];
    const obj = {};
    obj.Name = key;
    if (value === undefined) {
      value = null;
    }
    obj.Value = value;
    obj.CallBackType = 0;
    obj.IsOut = true;
    paramters[index] = obj;
    index += 1;
  }
  const req = {
    Handle: deviceID,
    CommandName: commandName,
    Paramters: paramters
  };
  return JSON.stringify(req);
}

// aes加密,key长度16、24、32，iv16
function Encrypt(data) {
  const key = CryptoJS.enc.Utf8.parse('JSDKWIDJRKRKCXIE');
  const iv = CryptoJS.enc.Utf8.parse('KSJEKSIEKDJSKAIE');
  if (typeof data === 'string') {
    if (data) {
      const srcs = CryptoJS.enc.Utf8.parse(data);
      return CryptoJS.AES.encrypt(srcs, key, {
        keySize: 128 / 8,
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Iso10126
      }).toString();
    }
  } else if (typeof data === 'object') {
    for (const k in data) {
      if (data[k]) {
        const srcs = CryptoJS.enc.Utf8.parse(data[k]);
        data[k] = CryptoJS.AES.encrypt(srcs, key, {
          keySize: 128 / 8,
          iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Iso10126
        }).toString();
      }
    }
  }
  return data;
}
// aes解密
function Decrypt(data) {
  const key = CryptoJS.enc.Utf8.parse('JSDKWIDJRKRKCXIE');
  const iv = CryptoJS.enc.Utf8.parse('KSJEKSIEKDJSKAIE');
  const decrypt = CryptoJS.AES.decrypt(data, key, {
    keySize: 128 / 8,
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Iso10126
  });
  const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr;
}
