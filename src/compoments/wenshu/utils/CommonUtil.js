export const isDate = (data) => {
  // return typeof data === 'string' && data.length >= 10 && isNaN(data) && !isNaN(Date.parse(data.replace(/[年月]/g,'-').replace(/日/,' ').replace(/[时分]/g,':').replace(/秒/,'')));
  const reg = /^([1-2]\d{3})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|30|31) (0\d|1\d|2[0-4]):(0\d|[1-5]\d):(0\d|[1-5]\d)$/;
  const reg1 = /^([1-2]\d{3})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|30|31)$/;
  return (
    typeof data === 'string' &&
    data.length > 5 &&
    isNaN(data) &&
    !isNaN(
      Date.parse(
        data
          .replace(/[年月]/g, '-')
          .replace(/日/, ' ')
          .replace(/[时分]/g, ':')
          .replace(/秒/, '')
      )
    ) &&
    (reg.test(data) || reg1.test(data))
  );
};

// 业务相关, 判断字段是否只读
export const fieldConfigCheck = (formConfig, checkField) => {
  const checkItem = formConfig && formConfig.find((item) => item.varname.toLowerCase() === checkField.toLowerCase());
  const itemConfig = {
    isEditable: false,
    isRequired: false,
    chname: '222'
  };
  if (checkItem && parseInt(checkItem.reservation02) !== 1) {
    itemConfig.isEditable = true;
  }
  if (checkItem && parseInt(checkItem.reservation01) === 1) {
    itemConfig.isRequired = true;
  }
  if (checkItem) {
    itemConfig.chname = checkItem.chname;
  }

  // console.info(checkField,itemConfig);

  return itemConfig;
};
