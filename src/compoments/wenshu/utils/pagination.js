import uuidv4 from 'uuid/v4';
import _ from 'lodash';

export const pagination = (props) => {
  const pageDom = document.querySelectorAll('[datatype="wspage"]');
  // const paragraph = pageDom[0].querySelectorAll('[datatype="paragraph"]');
  // const poffsetHeight = paragraph[0].offsetHeight;
  // const poffsetHeight1 = paragraph[1].offsetHeight;
  const { dataConfig } = props;

  // 先复制一份页面配置，留作分页后新的配置
  const newDataConfig = _.cloneDeep(dataConfig);

  const { borderLines } = dataConfig;
  const borderType = borderLines === 2 ? 'double' : 'single';
  const insertTag = 0;
  newDataConfig.pages = [];
  pageDom.forEach((item, i) => {
    const borderContainer = item.querySelectorAll(`[datatype="${borderType}BorderComponent"]`)[0];
    console.log("borderContainer.offsetHeight",borderContainer.offsetHeight)
    // const borderContainerHeight = borderContainer.offsetHeight || 993; // 如果没有边框，就给页面默认高度
    const borderContainerHeight = 993; // 2022年07月29日09:50:30 hqw  固定高度，印章如果超过边界 也会印到页脚
    console.log('borderContainerHeight', borderContainerHeight);
    // 计算 页面内 paragraph 总高度
    const paragraphContainer = item.querySelectorAll(`[datatype="paragraph"]`);
    let totalParagraphHeight = 0;
    paragraphContainer.forEach((p, j) => {
      const paragraphHeight = p.offsetHeight;
      totalParagraphHeight += paragraphHeight;
    });
    console.log('totalParagraphHeight', totalParagraphHeight);
    // 如果当前页段落高度(totalParagraphHeight) > 当前页边框高度(borderContainerHeight),则在当前页插入一个空白页
    if (totalParagraphHeight > borderContainerHeight && borderContainerHeight>0) {
      const { pages } = dataConfig;
      const currentPage = pages[i];
      const pageLength = Math.floor(totalParagraphHeight / borderContainerHeight);
      // 如果当前页有页脚，就删除，移到下一页去
      // eslint-disable-next-line prefer-destructuring
      const footText = currentPage.footText;
      if (footText && footText.length > 0) {
        currentPage.footText = null;
      }
      newDataConfig.pages.push(currentPage);

      for (let j = 0; j < pageLength; j++) {
        const newPage = _.cloneDeep(currentPage);
        // 给新页面一个空段落即可
        newPage.paragraph = [];
        newPage.key = uuidv4();
        // 当是最后一页，加上页脚
        if (j === pageLength - 1) {
          newPage.footText = footText;
        }
        newDataConfig.pages.push(newPage);
      }
    } else {
      newDataConfig.pages.push(dataConfig.pages[i]);
    }
  });

  return newDataConfig;
};
