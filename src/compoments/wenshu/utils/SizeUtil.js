export const calcParagraphSize = () => {
  // 每当表单发生数据变化都要检测高度变化进行调整
  const paragraphNodeList = document.querySelectorAll("[datatype='paragraph']");
  //轮训节点里面的子节点，如果子节点中有多个节点，就取最大，再把子节点高度相加
  paragraphNodeList.forEach((pNone, index) => {
    let maxTop = 0;
    let maxTopElemHeight = 0;
    const pNoneList = pNone.childNodes;
    pNoneList.forEach((children) => {
      // console.info("children.offsetTop",children.offsetTop);
      if (children.offsetTop >= maxTop && children.nodeName !== 'IMG') {
        maxTop = children.offsetTop;
        // console.info("children.clientHeight",children.clientHeight);
        maxTopElemHeight = children.clientHeight;
      }
    });
    const totalHeight = maxTop + maxTopElemHeight;
    // console.info("totalHeight",totalHeight);
    // 给节点加上高度，
    pNone.style.height = `${totalHeight}px`;
    // pNone.style.display = 'inherit';
  });
};
