/**
 * Created by PJQ on 2019-12-19
 * 功能描述：印章组件
 */

import React, { PureComponent } from 'react';

const paramHeader = 'data:image/png;base64,';

class Seal extends PureComponent {
  render() {
    const { imageBase64, width, height, style = {} } = this.props;
    const sealStyle = {
      position: 'absolute',
      zIndex: -1,
      width: width || '120pt',
      height: height || '120pt',
      ...style
    };
    return (
      <div style={sealStyle}>
        {imageBase64 ? <img alt="" src={`${paramHeader}${imageBase64}`} width="100%" height="100%" /> : ''}
      </div>
    );
  }
}

export default Seal;
