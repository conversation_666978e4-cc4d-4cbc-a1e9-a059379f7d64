/**
 * Created by PJQ on 2019-12-19
 * 功能描述：签名组件
 */

import React, { PureComponent } from 'react';

const paramHeader = 'data:image/png;base64,';

class Sign extends PureComponent {
  render() {
    const { imageBase64, width = '70pt', height = '42pt', style = {} } = this.props;
    const signStyle = {
      position: 'absolute',
      zIndex: -1,
      width,
      height,
      ...style
    };
    return (
      <div style={signStyle}>
        {imageBase64 ? (
          Array.isArray(imageBase64) ? (
            imageBase64.map(
              (item) =>
                item && <img alt="" src={`${paramHeader}${item}`} width="100%" height="100%" style={{ margin: 4 }} />
            )
          ) : (
            <img alt="" src={`${ imageBase64?.includes(paramHeader) ? "" : paramHeader}${imageBase64}`} width="100%" height="100%" style={{ margin: 4 }} />
          )
        ) : (
          ''
        )}
      </div>
    );
  }
}

export default Sign;
