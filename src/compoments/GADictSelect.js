/**
 * Created by qsh on 2018/6/5.
 */
import React, { Component } from 'react';
import { DictSelect } from 'SinoGear';
import { cmp, common } from '../utils/func';

const { isString, isNumber } = common;

class GADictSelect extends Component {
  state = {
    current: 1,
    total: 0,
    data: [],
    keyword: '',
    value: this.props.value,
    needTranslate: false,
    transValue: undefined,
    isOnChangeValue: false,
  };

  componentWillMount() {
    const { value } = this.state;
    //console.log('--->componentWillMount', value);
    if (isNumber(value) || isString(value)) {
      // 字符串翻译
      this.setState({
        needTranslate: true,
      });
    }
  }

  componentDidMount() {
    //console.log('--->componentDidMount');
    const { needTranslate } = this.state;
    if (needTranslate) {
      this.translate();
    }
  }

  componentWillReceiveProps(nextProps) {
    // console.log('--->componentWillReceiveProps', nextProps);
    // console.log('--->this props value', this.props.value);

    // const { isOnChangeValue } = this.state;
    const { value, type, params, kind } = this.props;
    //console.log('--->componentWillReceiveProps this.props.value', this.props.value );
    //console.log('--->componentWillReceiveProps nextProps.value', nextProps.value);

    if ('value' in nextProps && nextProps.value !== value) {
      if (nextProps.value) {
        if (isString(nextProps.value) || isNumber(nextProps.value)) {
          //console.log('--->componentWillReceiveProps isOnChangeValue', isOnChangeValue);

          this.setState({
            value: nextProps.value ? nextProps.value : [],
            needTranslate: value !== nextProps.value,
          });
          /*if (isOnChangeValue) {
            this.setState({
              value: nextProps.value ? nextProps.value : [],
              needTranslate: value !== nextProps.value
            });
          } else {
            this.setState({
              isOnChangeValue: false
            });
          }*/
        } else if (value !== nextProps.value) {
          this.setState({
            value: nextProps.value ? nextProps.value : [],
            transValue: nextProps.value,
          });
        }
      } else {
        // 没值
        this.setState({
          value: nextProps.value,
          transValue: undefined,
        });
      }
    }
    if (type === 'dynamic') {
      if (!cmp(params, nextProps.params)) {
        // 级联参数变化
        this.setState({
          data: [],
        });
      }
    } else if (type === 'static') {
      if (kind !== nextProps.kind) {
        this.setState({
          data: [],
        });
      }
    }
  }

  componentDidUpdate(prevProps, prevState) {
    //console.log('--->componentDidUpdate');
    const { needTranslate } = this.state;
    // console.log('--->componentDidUpdate', needTranslate);
    if (prevState.needTranslate !== needTranslate && needTranslate) {
      this.translate();
    }
  }

  onChange = value => {
    const { onChange, labelInValue, multiple, id, setQueryDictProps } = this.props;
    let val;
    if (labelInValue) {
      val = value;
      if (multiple) {
        val = value && value.length === 0 ? undefined : value;
      } else if (value && 'key' in value) {
        val = this.getDictFromData(value.key);
      }
    } else if (multiple) {
      val = !value ? undefined : value.length === 0 ? undefined : value.map(v => v.key).join(',');
    } else {
      val = value && value.key;
    }
    this.setState(
      {
        isOnChangeValue: true,
      },
      () => {
        if (onChange) {
          onChange(val, id, { setQueryDictProps });
        }
      },
    );
  };

  getDictFromData = code => {
    const { data, transValue } = this.state;
    let object;
    if (transValue && code === transValue.code) {
      object = transValue;
    } else {
      const array = data.filter(item => item.code === code);
      object = array.shift();
    }
    return object;
  };

  paginationOnchange = page => {
    const { type } = this.props;
    const { keyword } = this.state;
    this.setState({ current: page }, () => {
      if (keyword !== '') {
        this.searchStatic(page);
      } else {
        if (type === 'static') {
          this.fetchStatic(page);
        } else if (type === 'dynamic') {
          this.fetchDynamic(page);
        }
      }
    });
  };

  fetchStatic = page => {
    const { kind, dispatch } = this.props;
    dispatch({
      type: 'dict/fetchStaticDict',
      payload: {
        kind,
        page,
      },
    }).then(res => {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
      });
    });
  };

  fetchDynamic = (page, params = this.props.params) => {
    const { dispatch } = this.props;
    let paramStr = '';
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        paramStr += `${key}=${params[key]}&`;
      }
    }
    paramStr = paramStr.substr(0, paramStr.length - 1);

    dispatch({
      type: 'dict/fetchDynamicDict',
      payload: {
        paramStr,
        page,
      },
    }).then(res => {
      if (res) {
        const { dictItems = [], totalCount } = res;
        this.setState({
          data: dictItems,
          total: totalCount,
        });
      }
    });
  };

  transStatic = value => {
    // console.log('--->transStatic');
    const { kind, dispatch, labelInValue, multiple } = this.props;
    dispatch({
      type: 'dict/translateStatic',
      payload: {
        kind,
        code: value,
      },
    }).then(res => {
      if (res) {
        const transValue = multiple ? res : res[0];
        //console.log('------->transValue', transValue);
        this.setState(
          {
            transValue,
            needTranslate: false,
          },
          () => {
            if (labelInValue) {
              this.onChange(transValue);
            }
          },
        );
      }
    });
  };

  transDynamic = value => {
    const { params, dispatch, labelInValue, multiple } = this.props;
    let paramStr = '';
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        paramStr += `${key}=${params[key]}&`;
      }
    }
    paramStr = paramStr.substr(0, paramStr.length - 1);
    dispatch({
      type: 'dict/translateDynamic',
      payload: {
        paramStr,
        code: value,
      },
    }).then(res => {
      const transValue = multiple ? res : res[0];
      if (res) {
        this.setState(
          {
            transValue,
            needTranslate: false,
          },
          () => {
            if (labelInValue) {
              this.onChange(transValue);
            }
          },
        );
      }
    });
  };

  translate = () => {
    const { type } = this.props;
    const { value } = this.state;
    if (type === 'static') {
      this.transStatic(value);
    } else if (type === 'dynamic') {
      this.transDynamic(value);
    }
  };

  focus = () => {
    const { type } = this.props;
    const { data } = this.state;
    if (data.length === 0) {
      // 初始化字典
      if (type === 'static') {
        this.fetchStatic(1);
      } else if (type === 'dynamic') {
        this.fetchDynamic(1);
      }
    }
  };

  searchStatic = page => {
    const { kind, dispatch } = this.props;
    const { keyword } = this.state;
    dispatch({
      type: 'dict/searchStaticDict',
      payload: {
        keyword,
        kind,
        page,
      },
    }).then(res => {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
        current: page,
      });
    });
  };

  searchDynamic = page => {
    const { params, dispatch } = this.props;
    const { keyword } = this.state;
    dispatch({
      type: 'dict/searchDynamicDict',
      payload: {
        keyword,
        params,
        page,
      },
    }).then(res => {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
        current: page,
      });
    });
  };

  onSearch = value => {
    //console.log('--->onSearch', value);
    const { type } = this.props;
    this.setState(
      {
        keyword: value,
      },
      () => {
        if (value !== '') {
          if (type === 'static') {
            this.searchStatic(1);
          } else if (type === 'dynamic') {
            this.searchDynamic(1);
          }
        } else {
          if (type === 'static') {
            this.fetchStatic(1);
          } else if (type === 'dynamic') {
            this.fetchDynamic(1);
          }
        }
      },
    );
  };

  onClearAll = () => {
    //console.log('--->onClearAll');
    this.setState({
      keyword: '',
      data: [],
      current: 1,
    });
  };

  render() {
    const { data, current, total, transValue } = this.state;
    const { type, kind, params, value, ...rest } = this.props;
    const pagination = {
      current,
      pageSize: 10,
      total,
      onChange: this.paginationOnchange,
    };
    // console.log('--->value', value);
    //console.log('--->transValue', transValue);

    return (
      <DictSelect
        {...rest}
        reverseEllipsis
        labelInValue
        value={transValue}
        onChange={this.onChange}
        onSearch={this.onSearch}
        onClearAll={this.onClearAll}
        searchDelay={3000}
        allowClear
        showSearch
        data={data}
        pagination={pagination}
        onFocus={this.focus}
        placeholder="请选择..."
      />
    );
  }
}

export default GADictSelect;
