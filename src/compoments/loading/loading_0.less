.my-loading-container {
  position: absolute;
  left: 50%;
  top: 50%;
  bottom: 0;
  right: 0;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  //background-color: #f0ebeb;
  width: 100%;
  height: 100%;
  cursor: pointer;
  font-size: 14px;
}

.my-loading-span {
  position: relative;
  display: inline-block;
  font-size: 32px;
  width: 1em;
  height: 1em;
  transform: rotateZ(45deg);
  transition: transform .3s cubic-bezier(.78, .14, .15, .86);
  animation: Rotate45 1.2s infinite linear;
}

.my-loading-span > i {
  height: 14px;
  width: 14px;
  background-color: #1e93ff;
  display: block;
  position: absolute;
  border-radius: 100%;
  transform: scale(.75);
  transform-origin: 50% 50%;
  opacity: .3;
  animation: myAnimationMove 1s infinite linear alternate;
}

.my-loading-span:nth-child(1) {
  top: 0;
  left: 0;
}

.my-loading-span :nth-child(2) {
  top: 0;
  right: 0;
  animation-delay: .4s;
}

.my-loading-span :nth-child(3) {
  bottom: 0;
  right: 0;
  animation-delay: .8s;
}

.my-loading-span :nth-child(4) {
  left: 0;
  bottom: 0;
  animation-delay: 1.2s;
}

.my-loading-container > .my-text-container {
  padding-top: 5px;
  text-shadow: 0 1px 2px #fff;
  color: #8bc7ff;
  font-variant: tabular-nums;
  font-feature-settings: 'tnum';
  font-size: 14px;
}

@keyframes Rotate45 {
  to {
    transform: rotate(405deg);
  }
}

@keyframes myAnimationMove {
  to {
    opacity: 1;
  }
}
