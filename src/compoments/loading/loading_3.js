import React from 'react';
import styles from './loading_3.less';

function HookApi(props) {
  return (
    <div className={styles.loading}>
      <div className={`${styles.shape} ${styles['shape-1']}`} />
      <div className={`${styles.shape} ${styles['shape-2']}`} />
      <div className={`${styles.shape} ${styles['shape-3']}`} />
      <div className={`${styles.shape} ${styles['shape-4']}`} />
    </div>
  );
}

HookApi.Hzloading = 'div';

export default HookApi;
