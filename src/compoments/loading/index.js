import React from 'react';
import ReactDOM from 'react-dom';
import styles from './index.less';
import Hzloading0 from './loading_0';
import Hzloading1 from './loading_1';
import Hzloading2 from './loading_2';
import Hzloading3 from './loading_3';
const LoadingNode = (props) => {
  let { content, type } = props;
  type = type || 0;
  return (
    <div className={styles['loading-mask-box-npsp']}>
      <div className={styles['loading-la-dl']}>
        {type === 0 && <Hzloading0 />}
        {type === 1 && <Hzloading1 />}
        {type === 2 && <Hzloading2 />}
        {type === 3 && <Hzloading3 />}
        <div className={styles['loading-cir-la-content']}>{content || ''}</div>
      </div>
    </div>
  );
};

class Loading extends React.Component {
  constructor(props) {
    super(props);
    const element = document.createElement('div');
    element.id = 'other-root';
    this.state = {
      element,
    };
    this.timer = null
  }

  countTime=(props)=>{
      let { delay = 8  } = props;
      this.timer = setInterval(() => {
        delay =  delay - 1;
        if(delay == 0){
          clearInterval(this.timer);
          this.close();
        }
    }, 1000)
  }

  open = (data) => {
    clearInterval(this.timer)
    const { element } = this.state;
    document.body.appendChild(element);
    this.countTime({...data});
    ReactDOM.render(<LoadingNode content={(data && data.content) || ''} type={data?.type}/>, element);
  };

  close = () => {
    const { element } = this.state;
    element.remove();
    clearInterval(this.timer);
  };
}

const loading = new Loading();
export default loading;
