/**
 * Created by qsh on 2019/3/28.
 * 树形字典
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import {SGTreeSelect} from 'SinoGear';
import styles from './GATreeSelect.less';

export default class GATreeSelect extends Component {

  state = {
    data: [],
    isLoad: false
  };

  componentDidMount() {
    const { type } = this.props;
    if (type === 'static') {
      this.transStatic();
    } else if (type === 'dynamic') {
      this.transDynamic();
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { type, value, multiple } = this.props;

    if (value) {
      if (multiple ? prevProps.value && value.toString() !== prevProps.value.toString() : value !== prevProps.value) {
        if (type === 'static') {
          this.transStatic();
        } else if (type === 'dynamic') {
          this.transDynamic();
        }
      }
    }
  }

  fetchStatic = () => {
    const { kind, dispatch } = this.props;
    dispatch({
      type: 'dict/fetchStaticTree',
      payload: {
        kind
      }
    }).then((res) => {
      if (res) {
        this.setState({
          isLoad: true,
          data: this.setInterceptItemData(res)
        });
      }
    });
  };

  fetchDynamic = () => {
    const { dispatch, params } = this.props;
    let paramStr = '';
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        paramStr += `${key}=${params[key]}&`;
      }
    }
    paramStr = paramStr.substr(0, paramStr.length - 1);

    dispatch({
      type: 'dict/fetchDynamicTree',
      payload: {
        paramStr
      }
    }).then((res) => {
      if (res) {
        this.setState({
          isLoad: true,
          data: this.setInterceptItemData(res)
        });
      }
    });
  };

  focus = () => {
    const { type } = this.props;
    const { isLoad } = this.state;
    if (!isLoad) {
      // 初始化字典
      if (type === 'static') {
        this.fetchStatic();
      } else if (type === 'dynamic') {
        this.fetchDynamic();
      }
    }
  };

  transDynamic = () => {
    const { isLoad } = this.state;
    const { dispatch, params, value } = this.props;
    if (value && !isLoad) {
      let paramStr = '';
      for (const key in params) {
        if (Object.prototype.hasOwnProperty.call(params, key)) {
          paramStr += `${key}=${params[key]}&`;
        }
      }

      paramStr = paramStr.substr(0, paramStr.length - 1);

      dispatch({
        type: 'dict/translateDynamicTree',
        payload: {
          code: value,
          paramStr
        }
      }).then((res) => {
        if (res) {
          this.setState({
            data: res || []
          });
        }
      });
    }
  };

  transStatic = () => {
    const { isLoad } = this.state;
    const { kind, dispatch, value } = this.props;
    if (value && !isLoad) {
      dispatch({
        type: 'dict/translateStaticTree',
        payload: {
          kind,
          code: value
        }
      }).then((res) => {
        this.setState({
          data: res || []
        });
      });
    }
  };

  setInterceptItemData = (data = []) => {
    if (!this.__codeSets) {
      this.__codeSets = new Set([]);
    }
    data.forEach((itemData) => {
      this.__codeSets.add(itemData.superCode);
    });
    const { parentNodeSelectable } = this.props;
    data.forEach((itemData) => {
      if (this.__codeSets.has(itemData.code)) {
        itemData.selectable = true;
        itemData.disabled = !parentNodeSelectable;
      }
    });
    return data;
  };

  render() {
    const { data } = this.state;
    const { kind, params, type, id, ...rest } = this.props;
    return(
      <div id={id}>
        <SGTreeSelect
          treeDataSimpleMode={{
            id: "code",
            pId: "superCode"
          }}
          treeNodeFilterProp="title"
          optionLabelProp="title"
          data={data || []}
          name="dict1"
          title="树形加载单选"
          showSearch
          allowClear
          placeholder="请选择..."
          filterOption={false}
          onFocus={this.focus}
          {...rest}
        />
      </div>
    )
  }
}

GATreeSelect.defaultProps = {
  ...GATreeSelect.defaultProps,
  parentNodeSelectable: false, // 默认父节点不可选择
};
GATreeSelect.propTypes = {
  ...GATreeSelect.propTypes,
  parentNodeSelectable: PropTypes.bool, // 父节点是否可以选择
};
