
export function setDictSelectCallback(res) {
  if (res) {
    const { dictItems = [], totalCount } = res;
    this.setState({
      data: dictItems,
      total: totalCount,
      loading: false
    });
  }
}

export function fetchData(page = 1) {
  const { tabActiveLabel } = this.state;
  if (tabActiveLabel === '历史') {
    fetchHistoryUsedData.call(this, { page, callback: setDictSelectCallback.bind(this) })
  } else if (tabActiveLabel === '常用') {
    fetchCommonlyUsedData.call(this, { page, callback: setDictSelectCallback.bind(this) })
  } else if (tabActiveLabel === '列表') {
    fetchListTypeData.call(this, { page, callback: setDictSelectCallback.bind(this) })
  } else if (tabActiveLabel === '树形') {
    fetchTreeTypeData.call(this, { page, callback: setDictSelectCallback.bind(this) })
  }
}

/** 历史数据 */
function fetchHistoryUsedData({ page, callback }) {
  const { params = {} } = this.props;
  const historyData = JSON.parse(localStorage.getItem(JSON.stringify(params).substr(0, 20)) || '[]').reverse();
  callback({ dictItems: historyData, totalCount: historyData.length });
}

/** 设置历史选择项 */
export function setHistoryUsedData(selectValue) {
  if (!selectValue) return;
  const { multiple, params = {} } = this.props;
  const cacheKey = JSON.stringify(params).substr(0, 20);
  let historyData = JSON.parse(localStorage.getItem(cacheKey) || '[]');
  let svArray; // 当前选中值数组
  if (multiple) {
    svArray = selectValue.map(({ key, label }) => ({ code: key, id: key, detail: label }));
  } else {
    svArray = [{ code: selectValue.key, id: selectValue.key, detail: selectValue.label }];
  }
  svArray.forEach((item) => {
    let existIdx = -1;
    let existHis = historyData.some((hisItem, index) => {
      if (item.code === hisItem.code) {
        existIdx = index;
        return true;
      } else {
        return false;
      }
    });
    if (existHis) {
      historyData.splice(existIdx, 1);
    }
  });
  historyData = historyData.concat(svArray);
  if (historyData.length > 10) {
    historyData.splice(0, historyData.length - 10);
  }
  localStorage.setItem(cacheKey, JSON.stringify(historyData));
}

/** 常用数据 */
export function fetchCommonlyUsedData({ callback }) {
  const { params = {}, dispatch, pageSize } = this.props;
  const { kind, configId } = params;
  dispatch({
    type: 'dict/fetchUserDict',
    payload: {
      ...params,
      dictId: kind || configId,
      pageSize,
    }
  }).then(callback);
}

export function setCommonlyUsedData(_currentValue, _lastSelectValue, callback) {
  if (!_currentValue) return;
  const currentValue = Array.isArray(_currentValue) ? _currentValue : [_currentValue];
  const lastSelectValue = Array.isArray(_lastSelectValue) ? _lastSelectValue : [_lastSelectValue];
  const selectValue = currentValue.filter(({key}) => {
    return !lastSelectValue.some((lKey) => key === lKey);
  });
  if (selectValue.length === 0) return;

  const { multiple, params = {}, dispatch } = this.props;
  const { kind, configId } = params;
  let svArray; // 当前选中值数组
  if (multiple) {
    svArray = selectValue.map(({ key }) => key);
  } else {
    svArray = [selectValue[0].key];
  }
  dispatch({
    type: 'dict/saveUserDict',
    payload: {
      ...params,
      dictId: kind || configId,
      codes: svArray.join(','),
      type: kind ? 0 : 1
    }
  }).then(callback);
}

/** 列型数据 */
export function fetchListTypeData({ page, callback }) {
  const { type, params = {}, dispatch, pageSize, limit = true } = this.props;
  const { kind, configId } = params;

  const myCallBack = (res) => {
    const { dictItems = [], ...ots } = res;
    callback({
      ...ots,
      dictItems: dictItems.map((item) => {
        return { ...item, disabled: limit ? !!item.parentNode : false }
      })
    });
  };

  if (type === 'static') {
    dispatch({
      type: 'dict/fetchStaticDict',
      payload: {
        kind,
        page,
        pageSize,
      }
    }).then(myCallBack);
  } else if (type === 'dynamic') {
    const paramStr = Object.keys(params).map((key) => `${key}=${params[key]}`).join('&');
    dispatch({
      type: 'dict/fetchDynamicDict',
      payload: {
        paramStr,
        page,
        pageSize,
      }
    }).then(myCallBack);
  }
}

/** 树形数据 */
function fetchTreeTypeData({ page, callback }) {
  const { type, params = {}, dispatch, treeNodeWithChildCanSelect = true, pageSize } = this.props;
  const { kind, configId } = params;
  const paramStr = Object.keys(params).map((key) => `${key}=${params[key]}`).join('&');
  dispatch({
    type: type === 'static' ? 'dict/fetchStaticMixinTreeDict' : 'dict/fetchDynamicMixinTreeDict',
    payload: {
      paramStr,
      page,
      pageSize,
    }
  }).then((res) => {
    if (res) {
      const { dictItems = [], ...ots } = res;
      callback({ ...ots, dictItems: handleTreeDataChildrenDisabled(dictItems, { treeNodeWithChildCanSelect }) });
    } else {
      callback(res);
    }
  });
}

/** 异步载入树的数据 */
export function fetchTreeTypeDataAsync({ dataRef }) {
  const { type, params = {}, dispatch, treeNodeWithChildCanSelect = true, pageSize } = this.props;
  const { kind, configId } = params;
  const { key: parentCode, initData } = dataRef;
  const myCallback = (res) => {
    if (res) {
      const { dictItems: _dictItems = [], totalCount } = res;
      const dictItems = handleTreeDataChildrenDisabled(_dictItems, { treeNodeWithChildCanSelect });
      if (initData) {
        this.setState({
          data: dictItems,
          total: totalCount,
          loading: false
        });
      } else {
        dataRef.children = dictItems.length === 0 ? null : dictItems;
        this.setState({
          data: this.state.data,
          total: totalCount,
          loading: false
        });
      }
    }
  };
  // 异步载入数据
  const paramStr = Object.keys(params).map((key) => `${key}=${params[key]}`).join('&');
  dispatch({
    type: type === 'static' ? 'dict/fetchStaticMixinTreeDictAsync' : 'dict/fetchDynamicMixinTreeDictAsync',
    payload: {
      paramStr,
      parentCode: parentCode === 0 ? '' : parentCode,
      pageSize,
    }
  }).then(myCallback);
}

export function searchData(page = 1) {
  const { keyword } = this.state;
  const { type, params = {}, dispatch, searchField, pageSize } = this.props;
  const { kind, configId } = params;
  const callback = (res) => {
    const { dictItems = [], totalCount } = res;
    this.setState({
      data: dictItems,
      total: totalCount,
      current: page
    });
  };
  if (type === 'static') {
    dispatch({
      type: 'dict/searchStaticDict',
      payload: {
        keyword,
        kind,
        page,
        searchField,
        pageSize,
      }
    }).then(callback);
  } else if (type === 'dynamic') {
    dispatch({
      type: 'dict/searchDynamicDict',
      payload: {
        keyword,
        params,
        page,
        searchField,
        pageSize,
      }
    }).then(callback);
  }
}

export function translate() {
  const { type, params = {}, dispatch, labelInValue, multiple, pageSize } = this.props;
  const { kind, configId } = params;
  const { value } = this.state;
  const callback = (res) => {
    if (!res) return;
    const transValue = multiple ? res : res[0];
    this.setState(
      {
        transValue,
        needTranslate: false
      },
      () => labelInValue && this.handleChange(transValue)
    );
  };
  if (type === 'static') {
    dispatch({
      type: 'dict/translateStatic',
      payload: {
        kind,
        code: value,
        pageSize,
      }
    }).then(callback);
  } else if (type === 'dynamic') {
    const paramStr = Object.keys(params).map((key) => `${key}=${params[key]}`).join('&');
    dispatch({
      type: 'dict/translateDynamic',
      payload: {
        paramStr,
        code: value,
        pageSize
      }
    }).then(callback);
  }
}

export function getDictFromData(code) {
  const { data, transValue } = this.state;
  let object;
  if (transValue && code === transValue.code) {
    object = transValue;
  } else {
    const array = data.filter(item => item.code === code);
    object = array.shift();
  }
  return object;
}

function handleTreeDataChildrenDisabled(dictItems = [], { treeNodeWithChildCanSelect = true }) {
  function resetChildren(children) {
    if (Array.isArray(children)) {
      return children.map(({ parentNode, children, disabled, ...ots }) => ({
        ...ots, children: resetChildren(children), isLeaf: !parentNode, selectable: treeNodeWithChildCanSelect || !parentNode, disabled: !!disabled
      }));
    } else {
      return children;
    }
  }
  const newDictItems = dictItems.map(({ parentNode, children, disabled, ...ots }) =>
    ({ ...ots, children: resetChildren(children), isLeaf: !parentNode, selectable: treeNodeWithChildCanSelect || !parentNode, disabled: !!disabled })); // 后端反馈说字段disabled被占用，因此这边需做转换
  return newDictItems;
}