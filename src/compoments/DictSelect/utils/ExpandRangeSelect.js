
export function fetchData(page = 1) {
  const callback = (res) => {
    if (res) {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
        loading: false
      });
    }
  };
  fetchListTypeData.call(this, { page, callback });
}

function fetchListTypeData({ page, callback }) {
  const { type, params = {}, dispatch, mixinModel, pageSize } = this.props;
  const { tabActiveIndex } = this.state;
  const { label, defaultActive, type: mixinType, ...others } = mixinModel[tabActiveIndex];
  const { kind, configId } = params;
  if (type === 'static') {
    dispatch({
      type: 'dict/fetchStaticDict',
      payload: {
        kind,
        page,
        ...others,
        pageSize,
      }
    }).then(callback);
  } else if (type === 'dynamic') {
    const newParams = { ...params, ...others };
    const paramStr = Object.keys(newParams).map((key) => `${key}=${newParams[key]}`).join('&');
    dispatch({
      type: 'dict/fetchDynamicDict',
      payload: {
        paramStr,
        page,
        pageSize,
      }
    }).then(callback);
  }
}

export function searchData(page = 1) {
  const { keyword, tabActiveIndex } = this.state;
  const { type, params = {}, dispatch, searchField, mixinModel, pageSize } = this.props;
  const { kind, configId } = params;
  const { label, defaultActive, type: mixinType, ...others } = mixinModel[tabActiveIndex];
  const callback = (res) => {
    const { dictItems = [], totalCount } = res;
    this.setState({
      data: dictItems,
      total: totalCount,
      current: page
    });
  };
  if (type === 'static') {
    dispatch({
      type: 'dict/searchStaticDict',
      payload: {
        keyword,
        kind,
        params: { ...params, ...others },
        page,
        searchField,
        pageSize
      }
    }).then(callback);
  } else if (type === 'dynamic') {
    dispatch({
      type: 'dict/searchDynamicDict',
      payload: {
        keyword,
        params: { ...params, ...others },
        page,
        searchField,
        pageSize
      }
    }).then(callback);
  }
}

export function translate() {
  const { type, params = {}, dispatch, labelInValue, multiple, pageSize } = this.props;
  const { kind, configId } = params;
  const { value } = this.state;
  const callback = (res) => {
    if (!res) return;
    const transValue = multiple ? res : res[0];
    this.setState(
      {
        transValue,
        needTranslate: false
      },
      () => labelInValue && this.handleChange(transValue)
    );
  };
  if (type === 'static') {
    dispatch({
      type: 'dict/translateStatic',
      payload: {
        kind,
        code: value,
        pageSize,
      }
    }).then(callback);
  } else if (type === 'dynamic') {
    const paramStr = Object.keys(params).map((key) => `${key}=${params[key]}`).join('&');
    dispatch({
      type: 'dict/translateDynamic',
      payload: {
        paramStr,
        code: value,
        pageSize,
      }
    }).then(callback);
  }
}

export function getDictFromData(code) {
  const { data, transValue } = this.state;
  let object;
  if (transValue && code === transValue.code) {
    object = transValue;
  } else {
    const array = data.filter(item => item.code === code);
    object = array.shift();
  }
  return object;
}
