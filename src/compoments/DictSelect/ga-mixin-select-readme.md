版本边界 v20200506

# 集成配置

当前组件为零依赖集成方式，请访问组件SVN检录下来

> http://dzzw-svn.hnisi.com.cn:8300/svn/ga_code_bmjck/1主干开发区/公安架构4.0/production-base/frontend-component/GAMixinSelect

将检录下来的 `DictSelect` 文件内容，在当前项目根目录进行覆盖，覆盖内容包含如下

> src\modules\bases\models\dict.js // 新增树形字典服务逻辑
> src\modules\bases\services\dict.js  // 新增树形字典服务接口
> src\components\DictSelect // 新增GAMixinSelect相关文件

涉及更新文档列表有

```
[*] src\common\config.js
[*] src\modules\bases\models\dict.js
[*] src\modules\bases\services\dict.js
[+] src\components\DictSelect\utils\ExpandMixinSelect.js
[+] src\components\DictSelect\GAMixinSelect.js
[*] src\components\DictSelect\index.js
[*] package.json
```

## 安装依赖

### 前端

定位到项目根目录 `package.json` 文件，修改 `dependencies` 运行依赖版本为 `"SinoGear": "1.1.4"`，**删除根目录yarn.lock文件**，执行如下命令更新依赖

> yarn install --registry http://ggjs-app-03.hnisi.com.cn:8090

配置字典服务，定位到 `src\common\config.js` 全局配置，配置字典服务

```javascript
{
  api: {
    // 静态字典
    staticMixinTreeDict: `/StaticDict/loadTree?`,
    staticMixinTreeDictAsync: `/StaticDict/loadTreeAsync?`,
    // 动态字典
    dynamicMixinTreeDict: `/DynamicDict/loadTree?`,
    dynamicMixinTreeDictAsync: `/DynamicDict/loadTreeAsync?`
  }
}
```

### 后端

后端字典数据接口规格说明

> http://gs-wiki.hnisi.com.cn/pages/viewpage.action?pageId=163287685

后端需升级字典服务接口相关代码，并涉及使用改字典的数据需格式清洗改造，具体查阅

> xxx

# GAMixinSelect 组件说明

多标签混合字典组件，包含列表、树形、历史、常用标签字典展示

## Props

集成 `GADictSelect` 字典属性，扩展出如下属性

参数 | 描述 | 类型 | 默认值 | 备注
-|-|-|-|-
allTreeData | 异步加载树形数据 | bool | false | 默认为异步加载树形数据

## Demo

```jsx harmony
import React, { Component } from 'react';
import { GAMixinSelect } from '../../../../components/DictSelect';

@Form.create()
export default class TestPage extends Component {
  
  render(){
    const { dispatch } = this.props;
    return (
      <div>
        <GAMixinSelect type="static" params={{ kind: '07' }} allTreeData dispatch={dispatch} />
        <GAMixinSelect type="dynamic" multiple params={{ configId: '10003' }} dispatch={dispatch} />
      </div>
    );
  }
}
```
