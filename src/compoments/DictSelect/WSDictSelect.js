/**
 * Created by qsh on 2018/6/5.
 */
import React, { Component } from 'react';
import { Button } from 'antd';
import GADictSelect from './GADictSelect';
import styles from './WSDictSelect.less';

class WSDictSelect extends Component {

  handleOk = () => {
    const { onOk, value } = this.props;
    if (onOk) {
      onOk(value);
    }
  };

  render () {
    const { ...rest } = this.props;
    return(
      <div className={styles.select} >
        <div style={{ width: '150px' }}>
          <GADictSelect {...rest} size="small" />
        </div>
        <Button type="primary" size="small" onClick={this.handleOk}>确定</Button>
      </div>
    )
  }
}

export default WSDictSelect;