/** 多标签混入字典 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { DictSelect } from 'SinoGear';
import { cmp, common } from '../../utils/func';
import { fetchData, searchData, translate, getDictFromData } from './utils/ExpandRangeSelect';
import './GARangeDictSelect.less';

const { isString, isNumber } = common;

export default class GARangeDictSelect extends Component {
  constructor(props) {
    super(props);
    let tabActiveIndex = 0;
    (props.mixinModel || []).some((item, index) => {
      if (item.defaultActive) {
        tabActiveIndex = index;
        return true;
      } else {
        return false;
      }
    });
    this.state = {
      current: 1,
      total: 0,
      data: [],
      keyword: '',
      value: this.props.value,
      needTranslate: isNumber(this.props.value) || isString(this.props.value), // 字符串翻译
      transValue: undefined,
      isOnChangeValue: false,
      tabActiveIndex
    };
  }

  componentDidMount() {
    const { needTranslate } = this.state;
    needTranslate && translate.call(this);
  }

  componentWillReceiveProps(nextProps) {
    const { isOnChangeValue } = this.state;
    const { value, type, params = {} } = this.props;
    const { kind, configId } = params;
    if ('value' in nextProps && nextProps.value !== value) {
      if (nextProps.value) {
        if (isString(nextProps.value) || isNumber(nextProps.value)) {
          if (!isOnChangeValue) {
            this.setState({
              value: nextProps.value ? nextProps.value : [],
              needTranslate: value !== nextProps.value
            });
          } else {
            this.setState({
              isOnChangeValue: false
            });
          }
        } else if (value !== nextProps.value) {
          this.setState({
            value: nextProps.value ? nextProps.value : [],
            transValue: nextProps.value
          });
        }
      } else {
        // 没值
        this.setState({
          value: nextProps.value,
          transValue: undefined
        });
      }
    }
    if (type === 'dynamic') {
      if (!cmp(params, nextProps.params)) {
        // 级联参数变化
        this.setState({
          data: []
        });
      }
    } else if (type === 'static') {
      if (kind !== nextProps.params.kind) {
        this.setState({
          data: []
        });
      }
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { needTranslate } = this.state;
    if (prevState.needTranslate !== needTranslate && needTranslate) {
      translate.call(this);
    }
  }

  /** 分页事件监听，目前只支持列形 */
  paginationOnchange = (page) => {
    const { keyword = '' } = this.state;
    this.setState({ current: page }, () => {
      if (keyword) {
        searchData.call(this, page);
      } else {
        fetchData.call(this, page);
      }
    });
  };

  /** 标签切换 */
  handleTabChange = (index) => {
    this.setState({
      tabActiveIndex: index,
    }, () => {
      this.handleClearAll();
    });
  };

  handleFocus = () => {
    const { onFocus } = this.props;
    const { data } = this.state;
    if (data.length === 0) {
      fetchData.call(this); // 初始化字典
    }
    if (!!onFocus) {
      this.setState({ isOnChangeValue: false }, () => onFocus())
    }
  };

  handleChange = (value) => {
    if (this.handleChangeFlag) return; // 标记阀，屏蔽下方setState导致触发赛姬字典二次监听刷新问题
    const { onChange, labelInValue, multiple, id, setQueryDictProps } = this.props;
    let val;
    if (labelInValue) {
      val = value;
      if (multiple) {
        val = value && value.length === 0 ? undefined : value;
      } else if (value && 'key' in value) {
        val = getDictFromData.call(this, value.key);
      }
    } else if (multiple) {
      val = !value ? undefined : value.length === 0 ? undefined : value.map((v) => v.key).join(',');
    } else {
      val = value && value.key;
    }
    this.handleChangeFlag = true;
    this.setState(
      {
        isOnChangeValue: true,
        transValue: value
      },
      () => {
        setTimeout(() => {
          this.handleChangeFlag = false;
          if (onChange) {
            onChange(val, id, { setQueryDictProps });
          }
        }, 100);
      }
    );
  };

  handleSearch = (text = '') => {
    this.setState({
      keyword: text
    }, () => {
      if (text) {
        searchData.call(this);
      } else {
        fetchData.call(this);
      }
    });
  };

  handleClearAll = () => {
    this.setState({
      keyword: '',
      current: 1,
      data: []
    }, () => {
      fetchData.call(this);
    });
  };

  toSpanWidth = (codeWidth = 'primary') => {
    let spanWidth = {};
    if (codeWidth === "primary") {
      spanWidth = {
        colSpan: '9',
        detailSpan: '15'
      }
    } else if (codeWidth === "small") {
      spanWidth = {
        colSpan: '6',
        detailSpan: '18'
      }
    } else if (codeWidth === "large") {
      spanWidth = {
        colSpan: '12',
        detailSpan: '12'
      }
    }
    return spanWidth
  };

  render() {
    const { data, current, total, transValue } = this.state;
    const {
      id,
      type,
      params,
      value,
      mixinModel,
      pageSize,
      ...rest } = this.props;
    const pagination = {
      current,
      pageSize,
      total,
      onChange: this.paginationOnchange
    };

    return (
      <DictSelect
        {...rest}
        placeholder="请选择..."
        data={data}
        pagination={pagination}
        value={transValue}
        labelInValue // "SinoGear": 1.1.2及以下不支持
        searchDelay={1000}
        loading
        allowClear
        showSearch
        mixinFilter
        mixinModel={mixinModel}
        onTabChange={this.handleTabChange}
        onFocus={this.handleFocus}
        onChange={this.handleChange}
        onSearch={this.handleSearch}
        onClearAll={this.handleClearAll}
      />
    );
  }
}

GARangeDictSelect.defaultProps = {
  ...GARangeDictSelect.defaultProps,
  pageSize: 10, // 默认分页内条数10
};

GARangeDictSelect.propTypes = {
  ...GARangeDictSelect.propTypes,
  pageSize: PropTypes.number, // 分页内条数
};
