/**
 * Created by qsh on 2018/6/5.
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { DictSelect } from 'SinoGear';
import { cmp, common } from '../../utils/func';
import styles from './GADictSelect.less';

const { isString, isNumber } = common;

export default class GADictSelect extends Component {
  state = {
    current: 1,
    total: 0,
    data: [],
    keyword: '',
    value: this.props.value,
    needTranslate: false,
    transValue: undefined
  };

  componentWillMount() {
    const { value } = this.state;
    //console.log('--->componentWillMount', value);
    if (isNumber(value) || isString(value)) {
      // 字符串翻译
      this.setState({
        needTranslate: true
      });
    }
  }

  componentDidMount() {
    //console.log('--->componentDidMount');
    const { needTranslate } = this.state;
    if (needTranslate) {
      this.translate();
    }
  }

  componentWillReceiveProps(nextProps) {
    const { value, type, params, kind } = this.props;

    if ('value' in nextProps && nextProps.value !== value) {
      if (nextProps.value) {
        if (isString(nextProps.value) || isNumber(nextProps.value)) {
          this.setState({
            value: nextProps.value ? nextProps.value : [],
            needTranslate: value !== nextProps.value
          });
        } else if (value !== nextProps.value) {
          this.setState({
            value: nextProps.value ? nextProps.value : [],
            transValue: nextProps.value
          });
        }
      } else {
        // 没值
        this.setState({
          value: nextProps.value,
          transValue: undefined
        });
      }
    }
    if (type === 'dynamic') {
      if (!cmp(params, nextProps.params)) {
        // 级联参数变化
        this.setState({
          data: []
        });
      }
    } else if (type === 'static') {
      if (kind !== nextProps.kind) {
        this.setState({
          data: []
        });
      }
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { needTranslate } = this.state;
    if (prevState.needTranslate !== needTranslate && needTranslate) {
      this.translate();
    }
  }

  onChange = (value) => {
    const { onChange, labelInValue, multiple, id, setQueryDictProps } = this.props;
    let val;
    if (labelInValue) {
      val = value;
      if (multiple) {
        val = !value || value.length === 0
          ? undefined
          : value.map((v, index) => {
            const key = v.key ? v.key : v.code;
            return this.getDictFromData(key, index);
          });
      } else if (value && 'key' in value) {
        val = this.getDictFromData(value.key);
      }
    } else if (multiple) {
      val = !value ? undefined : value.length === 0 ? undefined : value instanceof Array ? value.map((v) => v.key).join(',') : value;
    } else {
      val = value && value.key;
    }
    if (onChange) {
      setTimeout(() => {
        onChange(val, id, { setQueryDictProps, originalValue: value, detail: value ? value.label || value.detail : undefined });
      }, 100)
    }
  };

  getDictFromData = (code, index) => {
    const { data, transValue } = this.state;
    const dealTransValue = transValue instanceof Array ? transValue[index] : transValue;
    let object;
    if (transValue && dealTransValue && code === dealTransValue.code) {
      object = dealTransValue;
    } else {
      const array = data.filter(item => item.code === code);
      object = array.shift();
    }
    return object;
  };

  paginationOnchange = (page) => {
    const { type } = this.props;
    const { keyword } = this.state;
    this.setState({ current: page }, () => {
      if (keyword !== '') {
        if (type === 'static') {
          this.searchStatic(page);
        } else if (type === 'dynamic') {
          this.searchDynamic(page);
        }
      } else {
        if (type === 'static') {
          this.fetchStatic(page);
        } else if (type === 'dynamic') {
          this.fetchDynamic(page);
        }
      }
    });
  };

  fetchStatic = (page) => {
    const { kind, dispatch, pageSize, params } = this.props;
    dispatch({
      type: 'dict/fetchStaticDict',
      payload: {
        ...params,
        kind,
        page,
        pageSize
      }
    }).then((res) => {
      const { dictItems = [], totalCount } = res;
	    this.setState({
        data: dictItems,
        total: totalCount
      });
    });
  };

  fetchDynamic = (page, params = this.props.params) => {
    const { dispatch, pageSize } = this.props;
    let paramStr = '';
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        paramStr += `${key}=${params[key]}&`;
      }
    }
    paramStr = paramStr.substr(0, paramStr.length - 1);

    dispatch({
      type: 'dict/fetchDynamicDict',
      payload: {
        paramStr,
        page,
        pageSize
      }
    }).then((res) => {
      if (res) {
        const { dictItems = [], totalCount } = res;
        this.setState({
          data: dictItems,
          total: totalCount
        });
      }
    });
  };

  transStatic = (value) => {
    const { kind, dispatch, labelInValue, multiple } = this.props;
    dispatch({
      type: 'dict/translateStatic',
      payload: {
        kind,
        code: value
      }
    }).then((res) => {
      if (res) {
        // console.log('res', res);
        const transValue = multiple ? res : res[0];
        this.setState(
          {
            transValue,
            needTranslate: false
          },
          () => {
            if (labelInValue) {
              this.onChange(transValue);
            }
          }
        );
      }
    });
  };

  transDynamic = (value) => {
    const { params, dispatch, labelInValue, multiple } = this.props;
    let paramStr = '';
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        paramStr += `${key}=${params[key]}&`;
      }
    }
    paramStr = paramStr.substr(0, paramStr.length - 1);
    dispatch({
      type: 'dict/translateDynamic',
      payload: {
        paramStr,
        code: value
      }
    }).then((res) => {
      const transValue = multiple ? res : res[0];
      if (res) {
        this.setState(
          {
            transValue,
            needTranslate: false
          },
          () => {
            if (labelInValue) {
              this.onChange(transValue);
            }
          }
        );
      }
    });
  };

  translate = () => {
    const { type } = this.props;
    const { value } = this.state;
    if (type === 'static') {
      this.transStatic(value);
    } else if (type === 'dynamic') {
      this.transDynamic(value);
    }
  };

  focus = () => {
    const { type, onFocus } = this.props;
    const { data } = this.state;
    if (data.length === 0) {
      // 初始化字典
      if (type === 'static') {
        this.fetchStatic(1);
      } else if (type === 'dynamic') {
        this.fetchDynamic(1);
      }
    }
    onFocus && onFocus();
  };

  blur = () => {
    const { onBlur } = this.props;
    this.setState({
      keyWord: '',
    },() => {
      this.paginationOnchange(1);
      onBlur && onBlur();
    });
  };

  searchStatic = (page) => {
    const { kind, dispatch, pageSize, searchField, params } = this.props;
    const { keyword } = this.state;
    dispatch({
      type: 'dict/searchStaticDict',
      payload: {
        ...params,
        keyword,
        kind,
        page,
        pageSize,
        searchField
      }
    }).then((res) => {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
        current: page
      });
    });
  };

  searchDynamic = (page) => {
    const { params, dispatch, pageSize, searchField } = this.props;
    const { keyword } = this.state;
    dispatch({
      type: 'dict/searchDynamicDict',
      payload: {
        keyword,
        params,
        page,
        pageSize,
        searchField
      }
    }).then((res) => {
      const { dictItems = [], totalCount } = res;
      this.setState({
        data: dictItems,
        total: totalCount,
        current: page
      });
    });
  };

  onSearch = (value) => {
    const { type, onSearch } = this.props;
    onSearch && onSearch(value);
    this.setState({
      keyword: value
    }, () => {
      if (value !== '') {
        if (type === 'static') {
          this.searchStatic(1);
        } else if (type === 'dynamic') {
          this.searchDynamic(1);
        }
      } else {
        if (type === 'static') {
          this.fetchStatic(1);
        } else if (type === 'dynamic') {
          this.fetchDynamic(1);
        }
      }
    });
  };

  onClearAll = () => {
    const { onClearAll, type } = this.props;
    onClearAll && onClearAll();
    const { keyword: prevKeyword } = this.state;
    this.setState({
      keyword: '',
    }, () => {
      if (prevKeyword) {
        if (type === 'static') {
          this.fetchStatic(1);
        } else if (type === 'dynamic') {
          this.fetchDynamic(1);
        }
      }
    });
  };

  toSpanWidth = (codeWidth = 'primary') => {
    let spanWidth = {};
    if (codeWidth === "primary") {
      spanWidth = {
        colSpan: '9',
        detailSpan: '15'
      }
    } else if (codeWidth === "small") {
      spanWidth = {
        colSpan: '6',
        detailSpan: '18'
      }
    } else if (codeWidth === "large") {
      spanWidth = {
        colSpan: '12',
        detailSpan: '12'
      }
    }
    return spanWidth
  };

  render() {
    const { data, current, total, transValue } = this.state;
	  const {
	    type,
      kind,
      params,
      value,
      pageSize,
      codeWidth,
      showCode=true,
      showCustomCode=false,
      customCodeTitle='编号',
      searchDelay=10,
      ...rest
	  } = this.props;
	  const pagination = {
      current,
      pageSize,
      total,
      onChange: this.paginationOnchange
    };
    const spanWidth = this.toSpanWidth(codeWidth);
    const codeKey = !showCustomCode ? 'code' : 'superCode';
    const colModel = [{ key: codeKey, title: customCodeTitle, span: spanWidth.colSpan },{ key: 'detail', title: '名称', span: spanWidth.detailSpan }];

    return (
      <div className={styles.gaDictSelect}>
        <DictSelect
          reverseEllipsis={false}
          value={transValue}
          loading="加载中"
          searchDelay={searchDelay}
          allowClear
          showSearch
          data={data}
          pagination={pagination}
          placeholder="请选择..."
          pattern="table"  //{showCode ? 'table' : ''} 强制使用table模式
          colModel={showCode ? 'code|detail' : colModel}
          {...rest}
          labelInValue
          onChange={this.onChange}
          onSearch={this.onSearch}
          onClearAll={this.onClearAll}
          onFocus={this.focus}
          onBlur={this.blur}
        />
      </div>
    );
  }
}

GADictSelect.defaultProps = {
  ...GADictSelect.defaultProps,
  pageSize: 10, // 默认分页内条数10
};

GADictSelect.propTypes = {
  ...GADictSelect.propTypes,
  pageSize: PropTypes.number, // 分页内条数
};
