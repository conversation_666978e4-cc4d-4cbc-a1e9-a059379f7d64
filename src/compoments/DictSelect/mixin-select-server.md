混合多标签字典 - 后端服务接口需求，版本边界 v20200429

# 字典静态树形格式数据(全部)

## 接口服务地址

> GET {hostServer}/StaticDict/loadTree

## 接口服务参数

参数 | 描述 | 类型 | 默认值
-|-|-|-
kind | 字典kind | string |  |

## 接口返回数据格式

```json
{
  "dictItems": [
    {
      "code": "440000000000",
      "id": "440000000000",
      "detail": "广东省公安厅",
      "children": [
        {
          "code": "440200000000",
          "id": "440200000000",
          "detail": "珠海市公安局"
        },
        {
          "code": "440100000000",
          "id": "440100000000",
          "detail": "广州市公安局",
          "children": [
            {
              "code": "440105000000",
              "id": "440105000000",
              "detail": "广州市黄埔区分局",
              "children": null
            },
            {
              "code": "440106000000",
              "id": "440106000000",
              "detail": "广州市天河区分局",
              "children": null
            }
          ]
        }
      ]
    }
  ]
}
```

# 字典静态树形格式数据(异步获取)

## 接口服务地址

> GET {hostServer}/StaticDict/loadTreeAsync

## 接口服务参数

参数 | 描述 | 类型 | 默认值
-|-|-|-
kind | 字典kind | string |  |
parentCode | 父亲节点code，默认''为获取根节点数据 | string | '' |

## 接口返回数据格式

若请求参数中parentCode=''，则返回根节点数据

```json
{
  "dictItems": [
    {
      "code": "440000000000",
      "id": "440000000000",
      "detail": "广东省公安厅"
    }
  ]
}
```

在点击某个节点数据过程，例如点击了 `广州市公安局`，则 `parentCode=440100000000`，并触发异步请求，获取当前公安局下的下属分局数据

```json
{
  "dictItems": [
    {
      "code": "440105000000",
      "id": "440105000000",
      "detail": "广州市黄埔区分局",
      "children": null
    },
    {
      "code": "440106000000",
      "id": "440106000000",
      "detail": "广州市天河区分局",
      "children": null
    }
  ]
}
```

# 字典动态树形结构数据(全部)

## 接口服务地址

> GET {hostServer}/DynamicDict/loadTree

## 接口服务参数

参数 | 描述 | 类型 | 默认值
-|-|-|-
configId | 字典configId | string |  |

## 接口返回数据格式

和 `字典静态树形格式数据(全部) - 接口返回数据格式` 数据格式一致

# 字典动态树形格式数据(异步获取)

## 接口服务地址

> GET {hostServer}/DynamicDict/loadTreeAsync

## 接口服务参数

参数 | 描述 | 类型 | 默认值
-|-|-|-
configId | 字典configId | string |  |
parentCode | 父亲节点code，默认''为获取根节点数据 | string | '' |

## 接口返回数据格式

和 `字典静态树形格式数据(异步获取) - 接口返回数据格式` 数据格式一致