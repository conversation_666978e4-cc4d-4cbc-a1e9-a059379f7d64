.gaDictSelect {
  display: inherit;

  :global {
    .ant-select-selection__placeholder {
      color: #aaa !important;
    }
  }
}

:global {
  .sg-dictselect-table {
    .ant-row-flex {
      flex-flow: inherit;
    }

    .sg-dictselect-ellipsis {
      float: left;
    }

    .sg-dictselect-ellipsis:nth-child(odd) {
      overflow: inherit;
    }

    .sg-dictselect-ellipsis:nth-child(even) {
      direction: rtl !important;
      padding-left: 4px;
    }
  }
}
