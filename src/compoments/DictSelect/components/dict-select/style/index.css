.sg-dictselect.ant-select-lg .ant-select-selection--multiple {
  height: 32px;
}
.sg-dictselect.ant-select-open.ant-select-lg .ant-select-selection--multiple {
  height: auto;
}
.sg-dictselect.ant-select-sm .ant-select-selection--multiple {
  height: 22px;
}
.sg-dictselect.ant-select-open.ant-select-sm .ant-select-selection--multiple {
  height: auto;
}
.sg-dictselect {
  bottom: -5px;
}
.sg-dictselect ul {
  width: 1000px;
}
.sg-dictselect ul li {
  float: left;
}
.sg-dictselect .ant-select-selection__rendered {
  overflow: hidden;
  height: 100%;
}
.sg-dictselect .ant-select-selection--single .ant-select-selection__clear {
  right: 25px;
}
.sg-dictselect ul.ant-select-selection__rendered {
  width: 99%;
  overflow: hidden;
}
.sg-dictselect.ant-select-open ul {
  width: auto;
}
.sg-dictselect-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ant-select-dropdown .sg-select-top {
  height: 3em;
  font-weight: bolder;
}
.ant-select-dropdown .ant-select-tree {
  max-height: 300px;
}
.sg-pagebutton {
  padding-left: 0px;
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
