/**
 * 焦点获取失去伸缩
 */
.sg-dictselect.ant-select-lg {
  .ant-select-selection--multiple{ height: 32px;}
}

.sg-dictselect.ant-select-open.ant-select-lg {
  .ant-select-selection--multiple{ height: auto;}
}


.sg-dictselect.ant-select-sm {
  .ant-select-selection--multiple{ height: 22px;}
}

.sg-dictselect.ant-select-open.ant-select-sm {
  .ant-select-selection--multiple{ height: auto;}
}

.sg-dictselect {
  bottom: -5px;
   ul {
     width: 1000px;

     li{
       float:left
     }
   }

  .ant-select-selection__rendered {
    overflow: hidden;
    height: 100%;
  }

  .ant-select-selection--single {
    .ant-select-selection__clear {
      right: 25px;
    }
  }

  ul.ant-select-selection__rendered{
    width:99%;
    overflow: hidden;
  }
}

.sg-dictselect.ant-select-open {
  ul {
    width: auto;
  }
}

.sg-dictselect-ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space:nowrap;
}

.ant-select-dropdown{
  .sg-select-top {
    height: 3em;
    font-weight: bolder;
  }

  .ant-row-flex {
  }

  .ant-select-tree{
    max-height: 300px;
  }
}

.sg-pagebutton {
  padding-left: 0px;
}

