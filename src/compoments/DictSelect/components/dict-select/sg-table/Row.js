/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/4/18.
 */
import React, { Component } from 'react';
import { Row as AntRow, Col as AntCol } from 'antd';

export default class Row extends Component {
  static isSelectRow = true;

  renderLis() {
    const { data, valueField, colModel } = this.props;
    let span = 24 / colModel.length;
    const lis = colModel.map((o) => {
      span = typeof o.span !== 'undefined' ? o.span : span;
      return (
        // edit 修复key重复问题
        <AntCol className="sg-dictselect-ellipsis" key={`${o.key}`} span={span} title={data[o.key]}>
          {data[o.key]}
        </AntCol>
      );
    });

    return lis;
  }

  render() {
    const lis = this.renderLis();
    return <AntRow type="flex">{lis}</AntRow>;
  }
}
