/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/5/22.
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import DictSelect from '../DictSelect';
import { hasChangeAttribute, isArray, isObject } from '../util';
import { onFetch, onTranslate } from '../asyncHandler';

/* eslint-disable react/no-unused-prop-types */
function noop() {}

/**
 * <AUTHOR>
 * @class 字典组件异步封装类
 * @returns {Function} 返回高阶函数
 */
export function connectDictSelectAsync() {
  return function wrapWithConnect(WrapComponent) {
    class ConnectDictSelectAsyncComponent extends Component {
      static defaultProps = {
        defaultTranslateUrl: '',
        defaultLoadUrl: '',
        multiple: false,
        showSearch: true,
        valueField: 'code',
        labelField: 'detail',
        searchField: 'detail',
        onFetch,
        onTranslate,
        onFocus: noop,
        onChange: noop,
        onSearch: noop,
        paginationAble: true,
        keyUpDelay: 300,
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          onChange: noop
        }
      };

      constructor(props) {
        super(props);
        this.hasFocus = false;
        this.cache = {};
        this.firstLoad = true;

        this.state = this.getStateFromProps(props);
      }

      componentWillReceiveProps(nextProps) {
        this.state = this.getStateFromProps(nextProps, true);
      }

      componentWillUnmount() {
        this.cache = null;
      }

      getStateFromProps(props, isWillReceiveProps) {
        const { pagination, paginationAble } = props;
        const state = {};

        const paginationInner = Object.assign({}, pagination);
        paginationInner.onChange = this.onPageChange;

        const initState = () => {
          const { pattern } = props;
          if (pattern === 'tab') {
            state.tabIndex = this.firstLoad ? 0 : 1;
          }

          if (!paginationAble) {
            state.pagination = null;
          } else {
            state.pagination = paginationInner;
          }

          if ('value' in props) {
            this.localTranslate(state, props.value);
          }
        };

        if (isWillReceiveProps) {
          const hasChangeValue = hasChangeAttribute(this.props, props, 'value');
          if (
            hasChangeAttribute(this.props, props, 'param') ||
            hasChangeAttribute(this.props, props, 'defaultLoadUrl') ||
            hasChangeValue
          ) {
            this.localTranslate(state, props.value);
          }
        } else {
          initState();
        }

        return Object.assign({}, this.state, state);
      }

      /**
       * 本地字典翻译
       * @param {object} state 组件状态
       * @param {string} value 被翻译的值
       * @returns {void} 无
       */
      localTranslate(state, value) {
        const { props } = this;
        const realVals = [];
        let newValue = null;
        const theState = state;
        let flag = true;
        if (value !== '' && value !== null && typeof value !== 'undefined') {
          const vals = value.split(',');
          for (let i = 0, len = vals.length; i < len; i += 1) {
            const realVal = this.cache[vals[i]];
            if (!realVal) {
              realVals.push({ label: vals[i], key: vals[i] });
              flag = false;
            } else {
              realVals.push(realVal);
            }
          }

          newValue = realVals;

          if (!flag) {
            this.translate(value);
          }
        } else if (props.mode === 'multiple') {
          newValue = [];
        } else {
          newValue = null;
        }

        theState.value = newValue;
      }

      async translate(values) {
        const { props } = this;
        if (props.translate) {
          props.translate(values);
          return;
        }
        let valueFields = values;
        if (isArray(valueFields)) {
          valueFields = valueFields.join(',');
        }

        const param = props.param || {};
        let params = {};
        params[props.valueField] = valueFields;

        params = Object.assign({}, param, params);

        let conditions = [];
        for (const key in params) {
          if (Object.prototype.hasOwnProperty.call(params, key)) {
            conditions.push(`${key}=${params[key]}`);
          }
        }
        conditions = conditions.join('&');

        const labelValue = await props.onTranslate(props.defaultTranslateUrl, params, conditions, props);

        this.setState({ value: labelValue });
      }

      /**
       * 由Select组件往外传递的值，单选情况下出返回的是{}Object形式；
       * 多选情况下返回的是[{}]形式
       * @param {object} values 选中的值
       * @return {string} keys  keys为逗号隔开的字符串
       *
       */
      onChange = (values) => {
        const { props } = this;
        if (!this.context.form) {
          this.setState({ value: values });
        }
        let keys = values;
        if (isObject(values)) {
          keys = values.key;
          this.cache[keys] = this.cache[keys] || values;
        } else if (isArray(values)) {
          keys = [];
          for (let i = 0, len = values.length; i < len; i += 1) {
            if (isObject(values[i])) {
              const key = values[i].key;
              keys.push(key);
              this.cache[key] = this.cache[keys] || values[i];
            } else {
              keys.push(values[i]);
            }
          }
          keys = keys.join(',');
        }
        props.onChange(keys);
      };

      handleFocus = () => {
        const { props } = this;
        if (!this.hasFocus) {
          this.fetch('', props.pagination || props.defaultPagination);
        }
        this.hasFocus = true;
      };

      /**
       * 字典搜索
       * @param {string} searchs 搜索文本
       * @returns {void} 无
       */
      onSearch = (searchs) => {
        const { props } = this;
        this.clearDelayTimer();
        this.delayTimer = setTimeout(() => {
          this.fetch(searchs, props.pagination || props.defaultPagination);
          this.clearDelayTimer();
        }, props.keyUpDelay);

        props.onSearch(searchs);
      };

      clearDelayTimer() {
        if (this.delayTimer) {
          clearTimeout(this.delayTimer);
          this.delayTimer = null;
        }
      }
      /**
       * 分页切换
       * @param {number} current   当前页
       * @param {number} pageSize  页大小
       * @param {number} total     总数据
       * @returns {void} 无
       */
      onPageChange = (current, pageSize, total) => {
        const { props, state } = this;
        this.fetch(state.query, { current, pageSize });
        if (props.pagination) {
          props.pagination.onChange(current, pageSize, total);
        }
      };

      /**
       * 加载数据
       * @param {string} query     搜索过滤条件
       * @param {Object} pageInfo  分页参数
       * @returns {Promise.<void>}  返回Promise
       */
      async fetch(query, pageInfo) {
        const { props } = this;
        const param = props.param || {};

        let params = {};
        params.query = query;
        params.page = pageInfo.current;
        params.pageSize = pageInfo.pageSize;
        params.searchField = props.searchField;

        params = Object.assign({}, param, params);

        const rs = await props.onFetch(props.defaultLoadUrl, params);

        const data = rs.data;
        const pagination = Object.assign({}, {}, this.state.pagination);
        pagination.current = pageInfo.current;
        pagination.total = rs.pagination.total;

        this.setState({ data, pagination, query });
      }

      render() {
        const { state } = this;
        // const { onFocus, value, pagination, onChange, onSearch, ...restProps } = this.props;
        const selectValue = state.value
          ? state.value
          : this.props.mode === 'multiple' || this.props.multiple
            ? []
            : { key: '', label: '' };
        return (
          <WrapComponent
            labelInValue
            {...this.props}
            data={state.data}
            value={selectValue}
            pagination={state.pagination}
            onChange={this.onChange}
            onFocus={this.handleFocus}
            onSearch={this.onSearch}
          />
        );
      }
    }

    ConnectDictSelectAsyncComponent.propTypes = {
      // 字典翻译地址
      defaultTranslateUrl: PropTypes.string,
      // 字典加载地址
      defaultLoadUrl: PropTypes.string,
      // 配置了则表示多选
      multiple: PropTypes.bool,
      // 配置了则表示可以搜索，多选情况下不需要手动配置，默认支持
      showSearch: PropTypes.bool,
      // 字典代码
      valueField: PropTypes.string,
      // 字典项
      labelField: PropTypes.string,
      // 字典搜索时，根据什么进行搜索，一般是字典项
      searchField: PropTypes.string,
      // 选中节点时调用此函数
      onChange: PropTypes.func,
      // 字典搜索回调函数
      onSearch: PropTypes.func,
      // 获取焦点回调函数
      onFocus: PropTypes.func,
      /**
       * 加载字典列表回调，可以使async函数，也可以返回Promise对象
       * 数据格式: {data:[], pagination:{total:100,current:1,pageSize:10}}
       */
      onFetch: PropTypes.func,
      /**
       * 翻译字典回调, 可以使用async函数或者返回Promise对象
       * 数据格式:[{key:'1', label:'数字一'}]
       *
       **/
      onTranslate: PropTypes.func,
      // 是否允许分页
      paginationAble: PropTypes.bool,
      // 搜索延迟
      keyUpDelay: PropTypes.number,
      // 分页信息
      pagination: PropTypes.object
    };

    ConnectDictSelectAsyncComponent.contextTypes = {
      form: PropTypes.object
    };

    return ConnectDictSelectAsyncComponent;
  };
}

const DictSelectAsync = connectDictSelectAsync()(DictSelect);
export { DictSelectAsync };
