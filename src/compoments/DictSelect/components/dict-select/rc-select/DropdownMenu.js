import React, { cloneElement } from 'react';
import { findDOMNode } from 'react-dom';
import PropTypes from 'prop-types';
import toArray from 'rc-util/lib/Children/toArray';
import Menu from 'rc-menu';
import { Row, Col, Button } from 'antd';
import scrollIntoView from 'dom-scroll-into-view';
import PageButton from '../../pageButton';
import { getSelectKeys, preventDefaultEvent, saveRef } from './util';

export default class DropdownMenu extends React.Component {
  static propTypes = {
    defaultActiveFirstOption: PropTypes.bool,
    value: PropTypes.any,
    dropdownMenuStyle: PropTypes.object,
    multiple: PropTypes.bool,
    onPopupFocus: PropTypes.func,
    onMenuDeSelect: PropTypes.func,
    onMenuSelect: PropTypes.func,
    prefixCls: PropTypes.string,
    menuItems: PropTypes.any,
    inputValue: PropTypes.string,
    visible: PropTypes.bool
  };

  componentWillMount() {
    this.lastInputValue = this.props.inputValue;
  }

  componentDidMount() {
    this.scrollActiveItemToView();
    this.lastVisible = this.props.visible;
  }

  shouldComponentUpdate(nextProps) {
    if (!nextProps.visible) {
      this.lastVisible = false;
    }
    // freeze when hide
    return nextProps.visible;
  }

  componentDidUpdate(prevProps) {
    const props = this.props;
    if (!prevProps.visible && props.visible) {
      this.scrollActiveItemToView();
    }
    this.lastVisible = props.visible;
    this.lastInputValue = props.inputValue;
  }

  scrollActiveItemToView = () => {
    // scroll into view
    const itemComponent = findDOMNode(this.firstActiveItem);
    const props = this.props;

    if (itemComponent) {
      const scrollIntoViewOpts = {
        onlyScrollIfNeeded: true
      };
      if ((!props.value || props.value.length === 0) && props.firstActiveValue) {
        scrollIntoViewOpts.alignWithTop = true;
      }

      scrollIntoView(itemComponent, findDOMNode(this.menuRef), scrollIntoViewOpts);
    }
  };

  renderMenu() {
    const props = this.props;
    const {
      menuItems,
      defaultActiveFirstOption,
      value,
      prefixCls,
      multiple,
      onMenuSelect,
      inputValue,
      firstActiveValue
    } = props;
    if (menuItems && menuItems.length) {
      const menuProps = {};
      if (multiple) {
        menuProps.onDeselect = props.onMenuDeselect;
        menuProps.onSelect = onMenuSelect;
      } else {
        menuProps.onClick = onMenuSelect;
      }

      const selectedKeys = getSelectKeys(menuItems, value);
      const activeKeyProps = {};

      let clonedMenuItems = menuItems;
      if (selectedKeys.length || firstActiveValue) {
        if (props.visible && !this.lastVisible) {
          activeKeyProps.activeKey = selectedKeys[0] || firstActiveValue;
        }
        let foundFirst = false;
        // set firstActiveItem via cloning menus
        // for scroll into view
        const clone = (item) => {
          if (
            (!foundFirst && selectedKeys.indexOf(item.key) !== -1) ||
            (!foundFirst && !selectedKeys.length && firstActiveValue.indexOf(item.key) !== -1)
          ) {
            foundFirst = true;
            return cloneElement(item, {
              ref: (ref) => {
                this.firstActiveItem = ref;
              }
            });
          }
          return item;
        };

        clonedMenuItems = menuItems.map((item) => {
          if (item.type.isMenuItemGroup) {
            const children = toArray(item.props.children).map(clone);
            return cloneElement(item, {}, children);
          }
          return clone(item);
        });
      }

      // clear activeKey when inputValue change
      const lastValue = value && value[value.length - 1];
      if (inputValue !== this.lastInputValue && (!lastValue || !lastValue.backfill)) {
        activeKeyProps.activeKey = '';
      }

      return (
        <Menu
          ref={saveRef(this, 'menuRef')}
          style={this.props.dropdownMenuStyle}
          defaultActiveFirst={defaultActiveFirstOption}
          {...activeKeyProps}
          multiple={multiple}
          focusable={false}
          {...menuProps}
          selectedKeys={selectedKeys}
          prefixCls={`${prefixCls}-menu`}>
          {clonedMenuItems}
        </Menu>
      );
    }
    return null;
  }

  // @edit 增加分页信息
  renderPage() {
    const { props } = this;
    let opeartion = null;
    let page = null;
    if (props.multiple) {
      opeartion = (
        <Button.Group size="small">
          <Button title="全选" type="primary" onClick={props.onCheckAll}>
            全选
          </Button>
          <Button title="清空" onClick={props.onClearAll}>
            清空
          </Button>
        </Button.Group>
      );
    }

    if (props.pagination || props.tabIndex) {
      page = <PageButton size="small" {...props.pagination} />;
    }

    return (
      <div style={{ padding: '10px' }}>
        <Row type="flex">
          <Col span={12} style={{ textAlign: 'left' }}>
            {opeartion}
          </Col>
          <Col span={12} style={{ textAlign: 'right' }}>
            {page}
          </Col>
        </Row>
      </div>
    );
  }

  // @edit 增加顶部表格、tab情况
  renderTop() {
    const { props } = this;
    if (props.pattern === 'table') {
      const colModel = props.colModel;
      let span = 24 / colModel.length;
      const cols = colModel.map((o, i) => {
        span = typeof o.span !== 'undefined' ? o.span : span;
        return (
          <Col className="SG-select-ellipsis" key={o.key} span={span}>
            {o.title}
          </Col>
        );
      });
      return (
        <Row className="sg-select-top" type="flex" justify="center" align="middle">
          {cols}
        </Row>
      );
    } else if (props.pattern === 'tab') {
      const tabWidth = 56;
      const style = { width: `${tabWidth}px` };
      style.transform = `translate3d(${tabWidth * props.tabIndex}px, 0px, 0px)`;

      return (
        <div className="sg-select-top ant-tabs-nav-wrap ant-tabs-mini ant-tabs-line">
          <div className="ant-tabs-nav-scroll">
            <div className="ant-tabs-nav ant-tabs-nav-animated">
              <div className="ant-tabs-ink-bar ant-tabs-ink-bar-animated" style={style} />
              <div
                className="ant-tabs-tab"
                onClick={() => {
                  props.tabChange(0);
                }}>
                常用
              </div>
              <div
                className="ant-tabs-tab"
                onClick={() => {
                  props.tabChange(1);
                }}>
                全部
              </div>
            </div>
          </div>
        </div>
      );
    }
  }

  render() {
    const renderMenu = this.renderMenu();
    // @edit 增加底部和顶部分页
    const renderPage = this.renderPage();
    const renderTop = this.renderTop();
    return renderMenu ? (
      <div style={{ overflow: 'auto' }} onFocus={this.props.onPopupFocus} onMouseDown={preventDefaultEvent}>
        {renderTop}
        {renderMenu}
        {renderPage}
      </div>
    ) : null;
  }
}

DropdownMenu.displayName = 'DropdownMenu';
