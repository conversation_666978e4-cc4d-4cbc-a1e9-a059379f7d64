/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/4/6.
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Select from './select';
import { Row } from './sg-table';

require('./style/index.scss');

const Option = Select.Option;

function noop() {}

/**
 * <AUTHOR>
 * @class 字典组件-table和tab形式
 */
export default class DictSelect extends Component {
  static defaultProps = {
    valueField: 'code',
    labelField: 'detail',
    placeholder: '请输入选择',
    labelInValue: false,
    onTabChange: noop,
    onChange: noop,
    onSearch: noop,
    onFocus: noop,
    className: 'sg-dictselect'
  };

  static propTypes = {
    valueField: PropTypes.string,
    labelField: PropTypes.string,
    placeholder: PropTypes.string,
    labelInValue: PropTypes.bool,
    onTabChange: PropTypes.func,
    onChange: PropTypes.func,
    onSearch: PropTypes.func,
    onFocus: PropTypes.func,
    className: PropTypes.string
  };

  constructor(props) {
    super(props);
    this.state = {};
    if (props.pattern === 'tab') {
      this.state.tabIndex = 0;
    }
  }

  /**
   * tab页签改变事件
   * @param {int} tabIndex 改变的tab的索引
   * @return {void}
   */
  tabChange = (tabIndex) => {
    this.setState({ tabIndex });
    this.props.onTabChange(tabIndex);
  };

  renderOptions() {
    const { data = [], labelField, valueField, popular, ...restState } = this.props;
    let render = null;
    let child = null;
    let trueData = data;

    const pattern = this.props.pattern;
    if (pattern === 'tab') {
      trueData = this.state.tabIndex ? data : popular;
    }

    render = trueData.map((o) => {
      if (pattern === 'table') {
        child = <Row key={o.valueField} data={o} labelField={labelField} valueField={valueField} {...restState} />;
      } else {
        child = o[labelField];
      }
      return (
        <Option key={o[valueField]} value={o[valueField]}>
          {child}
        </Option>
      );
    });

    return render;
  }

  render() {
    // 拿出外面传递的tabChange出来，替换成本组件的tabChange
    const { ...restProps } = this.props;
    const { tabIndex } = this.state;
    const options = this.renderOptions();
    return (
      <Select filterOption={false} tabIndex={tabIndex} {...restProps} tabChange={this.tabChange}>
        {options}
      </Select>
    );
  }
}
