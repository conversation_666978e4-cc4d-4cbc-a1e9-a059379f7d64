/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/4/26.
 */

/**
 * 判断数据类型是否为Object
 * @param {object} obj 被检验参数
 * @returns {boolean} 比较结果
 */
export function isObject(obj) {
  return Object.prototype.toString.call(obj) === '[object Object]';
}

/**
 * 判断数据类型是否为数组
 * @param {array} obj 被检验参数
 * @returns {boolean} 比较结果
 */
export function isArray(obj) {
  return Object.prototype.toString.call(obj) === '[object Array]';
}

/**
 * 判断数据类型是否为字符串
 * @param {string} obj 被检验参数
 * @returns {boolean} 比较结果
 */
function isString(obj) {
  return Object.prototype.toString.call(obj) === '[object String]';
}

/**
 * 判断对象是否一致
 * @param {Object} a  第一个传入的参数
 * @param {Object} b  第二个传入的参数
 * @returns {boolean} 比较结果
 */
export function isObjectValueEqual(a, b) {
  if (typeof a === 'number' && typeof b === 'number') {
    return a === b;
  }

  if (typeof a !== typeof b) {
    return false;
  }

  if (a == null || b == null || typeof a === 'undefined' || typeof b === 'undefined') {
    return a === b;
  }
  const aProps = Object.getOwnPropertyNames(a);
  const bProps = Object.getOwnPropertyNames(b);

  if (aProps.length !== bProps.length) {
    return false;
  }

  for (let i = 0; i < aProps.length; i += 1) {
    const propName = aProps[i];
    if (
      Object.prototype.toString(a[propName]) === '[Object Object]' ||
      Object.prototype.toString(b[propName]) === '[Object Object]'
    ) {
      isObjectValueEqual(a[propName], b[propName]);
    }
    if (a[propName] !== b[propName]) {
      return false;
    }
  }
  return true;
}

/**
 * 判断组件属性值是否改变
 * @param {object} props  前一个状态
 * @param {object} nextProps  当前状态
 * @param {string} attribute  属性名称
 * @returns {boolean} 返回结果
 */
export function hasChangeAttribute(props, nextProps, attribute) {
  return !isObjectValueEqual(props[attribute], nextProps[attribute]);
}

/**
 * 根据是否为字符串判断是否需要翻译
 * @param {Object} values 被传入值
 * @returns {boolean} 比较结果
 */
export function isNeedTranslate(values) {
  return isString(values);
}
