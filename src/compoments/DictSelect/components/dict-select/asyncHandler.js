/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/5/11.
 */
import { io } from '../../services/SGCore';

/**
 * 默认处理电政字典报文，并转换为{data:[], pagination:{current:1, total:200}}格式
 * pagination是antd对应的组件
 * @param {string} url 请求地址
 * @param {object} params 参数
 * @returns {Promise.<*|Promise>} 返回Promise
 */
export async function onFetch(url, params) {
  let conditions = [];
  for (const key in params) {
    if (Object.prototype.hasOwnProperty.call(params, key)) {
      conditions.push(`${key}=${params[key]}`);
    }
  }

  conditions = conditions.join('&');

  const theurl = `${url}${conditions}`;
  const result = await io.get(theurl);
  const rs = {
    data: result.dictItems,
    pagination: {
      total: result.totalCount
    }
  };
  return rs;
}

/**
 * 字典翻译
 * @param {string} url  请求地址
 * @param {object} param  参数
 * @param {string} conditions 参数get方式
 * @param {object} props  组件props
 * @returns {Promise.<Array>} 返回Promise
 */
export async function onTranslate(url, param, conditions, props) {
  const theurl = `${url}${conditions}`;
  const result = await io.get(theurl);
  const labelValue = [];
  if (result && result.length) {
    for (let i = 0; i < result.length; i += 1) {
      labelValue.push({ key: result[i][props.valueField], label: result[i][props.labelField] });
    }
  }
  return labelValue;
}
