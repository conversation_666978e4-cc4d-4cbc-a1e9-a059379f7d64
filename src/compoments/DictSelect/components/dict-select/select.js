/* eslint-disable */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import RcSelect, { Option, OptGroup } from './rc-select';
import classNames from 'classnames';
// import warning from '../_util/warning';

export default class Select extends Component {
  static Option = Option;
  static OptGroup = OptGroup;

  static defaultProps = {
    prefixCls: 'ant-select',
    showSearch: false,
    transitionName: 'slide-up',
    choiceTransitionName: 'zoom'
  };

  static propTypes = {
    prefixCls: PropTypes.string,
    className: PropTypes.string,
    size: PropTypes.oneOf(['default', 'large', 'small']),
    combobox: PropTypes.bool,
    notFoundContent: PropTypes.any,
    showSearch: PropTypes.bool,
    optionLabelProp: PropTypes.string,
    transitionName: PropTypes.string,
    choiceTransitionName: PropTypes.string
  };

  static context = {
    antLocale: PropTypes.object
  };

  getLocale() {
    const { antLocale } = this.context;
    if (antLocale && antLocale.Select) {
      return antLocale.Select;
    }
    return {
      notFoundContent: 'Not Found'
    };
  }

  render() {
    const {
      prefixCls,
      className = '',
      size,
      mode,
      // @deprecated
      multiple,
      tags,
      combobox,
      pagination,
      ...restProps
    } = this.props;
    // warning(
    //   !multiple && !tags && !combobox,
    //   '`Select[multiple|tags|combobox]` is deprecated, please use `Select[mode]` instead.',
    // );

    const cls = classNames(
      {
        [`${prefixCls}-lg`]: size === 'large',
        [`${prefixCls}-sm`]: size === 'small'
      },
      className
    );

    const locale = this.getLocale();
    let { notFoundContent = locale.notFoundContent, optionLabelProp } = this.props;
    if (this.props.children <= 0) {
      if (pagination !== undefined && pagination.total !== undefined) {
        pagination.total = 1;
      }
    }
    const isCombobox = mode === 'combobox' || combobox;
    if (isCombobox) {
      notFoundContent = null;
      // children 带 dom 结构时，无法填入输入框
      optionLabelProp = optionLabelProp || 'value';
    }

    const modeConfig = {
      multiple: mode === 'multiple' || multiple,
      tags: mode === 'tags' || tags,
      combobox: isCombobox
    };

    return (
      <RcSelect
        {...restProps}
        {...modeConfig}
        prefixCls={prefixCls}
        className={cls}
        pagination={pagination}
        optionLabelProp={optionLabelProp || 'children'}
        notFoundContent={notFoundContent}
      />
    );
  }
}
