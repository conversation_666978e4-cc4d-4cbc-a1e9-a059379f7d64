/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/4/5.
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';

import './style/index.js';

function noop() {}
/**
 * <AUTHOR>
 * @class   迷你分页按钮
 */
export default class PageButton extends Component {
  static defaultProps = {
    size: 'default',
    total: 0,
    defaultPageSize: 10,
    onChange: noop,
    defaultCurrent: 1
  };

  /* eslint-disable */
  static propTypes = {
    pageSize: PropTypes.number
  };
  /* eslint-enable */

  constructor(props) {
    super(props);

    const pageSize = props.pageSize || props.defaultPageSize;
    const total = props.total;
    const current = props.current || props.defaultCurrent;
    const totalPage = Math.ceil(total / pageSize);
    this.state = {
      pageSize,
      total,
      current,
      totalPage
    };
  }

  componentWillReceiveProps(nextProps) {
    if ('current' in nextProps) {
      this.state.current = nextProps.current;
    }

    if ('total' in nextProps) {
      this.state.total = nextProps.total;
    }

    if ('pageSize' in nextProps) {
      this.state.pageSize = nextProps.pageSize;
    }
    const totalPage = Math.ceil(this.state.total / this.state.pageSize);
    this.state.totalPage = totalPage;
    this.setState(this.state);
  }

  onClick(flag) {
    const { state, props } = this;
    let { current } = state;
    const { pageSize, totalPage } = state;
    switch (flag) {
      case 'first':
        current = 1;
        break;
      case 'prev':
        current = current <= 1 ? current : current - 1;
        break;
      case 'next':
        current = current >= totalPage ? totalPage : current + 1;
        break;
      case 'last':
        current = totalPage;
        break;
      default:
        break;
    }
    /**
     * 如果外部有传current，则current由外部控制
     */
    if (!('current' in props)) {
      this.state.current = current;
      this.setState(this.state);
    }

    props.onChange(current, pageSize, totalPage);
  }

  /**
   * 分别判断四个按钮的状态
   * @returns {[boolean,boolean,boolean,boolean]} 返回按钮状态数组
   */
  disabledBtn() {
    const { current, totalPage } = this.state;
    const arr = [false, false, false, false];

    if (current <= 1) {
      arr[0] = true;
      arr[1] = true;
    }
    if (current >= totalPage) {
      arr[2] = true;
      arr[3] = true;
    }
    return arr;
  }

  render() {
    const { state } = this;
    const disabledBtn = this.disabledBtn();

    const disbaledClass = 'sg-pagebutton-disabled';
    const prevClass = disabledBtn[0] ? `${disbaledClass} sg-pagebutton-prev` : 'sg-pagebutton-prev';
    const nextClass = disabledBtn[3] ? `${disbaledClass} sg-pagebutton-next` : 'sg-pagebutton-next';

    const title = `${state.current}/${state.totalPage}`;
    /* eslint-disable jsx-a11y/anchor-has-content */
    return (
      <div>
        <ul className="sg-pagebutton sg-pagebutton-simple ">
          <li
            title="前一页"
            className={prevClass}
            onClick={() => {
              if (!disabledBtn[0]) {
                this.onClick('prev');
              }
            }}>
            <a />
          </li>
          <li title={title} className="sg-pagebutton-simple-pager">
            <a
              disabled={disabledBtn[1]}
              onClick={() => {
                if (!disabledBtn[1]) {
                  this.onClick('first');
                }
              }}>
              {state.current}
            </a>
            <span className="sg-pagebutton-slash">／</span>
            <a
              disabled={disabledBtn[2]}
              onClick={() => {
                if (!disabledBtn[2]) {
                  this.onClick('last');
                }
              }}>
              {state.totalPage}
            </a>
          </li>
          <li
            title="下一页"
            className={nextClass}
            onClick={() => {
              if (!disabledBtn[3]) {
                this.onClick('next');
              }
            }}>
            <a />
          </li>
        </ul>
      </div>
    );
  }
}
