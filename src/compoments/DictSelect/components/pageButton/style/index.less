.sg-pagebutton {
  font-size: 12px;
}

.sg-pagebutton:after {
  content: ' ';
  display: block;
  height: 0;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}

.sg-pagebutton-prev {
  margin-right: 8px;
}

.sg-pagebutton-prev,
.sg-pagebutton-next {
  font-family: Arial;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
  list-style: none;
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  display: inline-block;
}

.sg-pagebutton-prev,
.sg-pagebutton-next {
  border: 1px solid #d9d9d9;
  background-color: #fff;
}

.sg-pagebutton-prev a,
.sg-pagebutton-next a {
  color: rgba(0, 0, 0, 0.65);
}

.sg-pagebutton-prev a:after,
.sg-pagebutton-next a:after {
  display: inline-block;
  font-size: 12px;
  font-size: 8px \9;
  -webkit-transform: scale(0.66666667) rotate(0deg);
  -ms-transform: scale(0.66666667) rotate(0deg);
  transform: scale(0.66666667) rotate(0deg);
  /* IE6-IE8 */
  -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(sizingMethod='auto expand', M11=1, M12=0, M21=0, M22=1)";
  zoom: 1;
  display: block;
  height: 26px;
  line-height: 27px;
  font-family: 'anticon';
  text-align: center;
}

:root .sg-pagebutton-prev a:after,
:root .sg-pagebutton-next a:after {
  -webkit-filter: none;
  filter: none;
}

:root .sg-pagebutton-prev a:after,
:root .sg-pagebutton-next a:after {
  font-size: 12px;
}

.sg-pagebutton-prev:hover,
.sg-pagebutton-next:hover {
  border-color: #108ee9;
}

.sg-pagebutton-prev:hover a,
.sg-pagebutton-next:hover a {
  color: #108ee9;
}

.sg-pagebutton-prev a:after {
  content: '\E620';
  display: block;
}
.sg-pagebutton-next a:after {
  content: '\E61F';
  display: block;
}

.sg-pagebutton-disabled {
  cursor: not-allowed;
}

.sg-pagebutton-disabled:hover {
  border-color: #d9d9d9;
}

.sg-pagebutton-disabled:hover a {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}

.sg-pagebutton-disabled a {
  color: rgba(0, 0, 0, 0.25);
}

.sg-pagebutton-slash {
  margin: 0 10px 0 5px;
}

.sg-pagebutton-simple .sg-pagebutton-prev,
.sg-pagebutton-simple .sg-pagebutton-next {
  border: 0;
  height: 24px;
  line-height: 24px;
  margin: 0;
  font-size: 18px;
}

.sg-pagebutton-simple .sg-pagebutton-simple-pager {
  display: inline-block;
  margin-right: 8px;
}
