/** 多标签混入字典 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { DictSelect } from 'SinoGear';
import { cmp, common } from '../../utils/func';
import { fetchData, fetchTreeTypeDataAsync, searchData, translate, getDictFromData, setHistoryUsedData, setCommonlyUsedData } from './utils/ExpandMixinSelect';

const { isString, isNumber } = common;
const DefaultActiveLabel = '列表';
const DefaultMixinModel = [
  { label: '列表', defaultActive: true, type: 'dict' },
  { label: '树形', defaultActive: false, type: 'tree' },
  { label: '历史', defaultActive: false, type: 'dict' },
  { label: '常用', defaultActive: false, type: 'dict' },
];

export default class GAMixinSelect extends Component {
  state = {
    current: 1,
    total: 0,
    data: [],
    keyword: '',
    value: this.props.value,
    needTranslate: isNumber(this.props.value) || isString(this.props.value), // 字符串翻译
    transValue: undefined,
    isOnChangeValue: false,
    tabActiveLabel: DefaultActiveLabel
  };

  componentDidMount() {
    const { needTranslate } = this.state;
    needTranslate && translate.call(this);
  }

  componentWillReceiveProps(nextProps) {
    const { isOnChangeValue } = this.state;
    const { value, type, params = {} } = this.props;
    const { kind, configId } = params;
    if ('value' in nextProps && nextProps.value !== value) {
      if (nextProps.value) {
        if (isString(nextProps.value) || isNumber(nextProps.value)) {
          if (!isOnChangeValue) {
            this.setState({
              value: nextProps.value ? nextProps.value : [],
              needTranslate: value !== nextProps.value
            });
          } else {
            this.setState({
              isOnChangeValue: false
            });
          }
        } else if (value !== nextProps.value) {
          this.setState({
            value: nextProps.value ? nextProps.value : [],
            transValue: nextProps.value
          });
        }
      } else {
        // 没值
        this.setState({
          value: nextProps.value,
          transValue: undefined
        });
      }
    }
    if (type === 'dynamic') {
      if (!cmp(params, nextProps.params)) {
        // 级联参数变化
        this.setState({
          data: []
        });
      }
    } else if (type === 'static') {
      if (kind !== nextProps.params.kind) {
        this.setState({
          data: []
        });
      }
    }
  }

  componentDidUpdate(prevProps, prevState) {
    const { needTranslate } = this.state;
    if (prevState.needTranslate !== needTranslate && needTranslate) {
      translate.call(this);
    }
  }

  /** 分页事件监听，目前只支持列形 */
  paginationOnchange = (page) => {
    const { keyword = '' } = this.state;
    this.setState({ current: page }, () => {
      if (keyword) {
        searchData.call(this, page);
      } else {
        fetchData.call(this, page);
      }
    });
  };

  /** 标签切换 */
  handleTabChange = (index) => {
    this.setState({
      tabActiveLabel: DefaultMixinModel[index].label
    }, () => {
      this.handleClearAll();
    });
  };

  handleFocus = () => {
    const { data } = this.state;
    const { onFocus } = this.props;
    if (data.length === 0) {
      fetchData.call(this); // 初始化字典
    }
    if(onFocus){
      onFocus()
    }
  };

  handleChange = (value) => {
    if (this.handleChangeFlag) return; // 标记阀，屏蔽下方setState导致触发赛姬字典二次监听刷新问题
    const { onChange, labelInValue, multiple, id, setQueryDictProps } = this.props;
    let val;
    if (labelInValue) {
      val = value;
      if (multiple) {
        val = value && value.length === 0 ? undefined : value;
      } else if (value && 'key' in value) {
        val = getDictFromData.call(this, value.key);
      }
    } else if (multiple) {
      val = !value ? undefined : value.length === 0 ? undefined : value.map((v) => v.key).join(',');
    } else {
      val = value && value.key;
    }
    this.handleChangeFlag = true;
    const lastSelectValue = this.state.transValue || [];
    this.setState(
      {
        isOnChangeValue: true,
        transValue: value
      },
      () => {
        setTimeout(() => {
          this.handleChangeFlag = false;
          setHistoryUsedData.call(this, value); // val是代码字符串
          setCommonlyUsedData.call(this, value, lastSelectValue); // val是代码字符串
          if (onChange) {
            onChange(labelInValue ? value : val, id, { setQueryDictProps });
          }
        }, 100);
      }
    );
  };

  handleSearch = (text = '') => {
    const { tabActiveLabel } = this.state;
    if (tabActiveLabel === '列表') {
      this.setState({
        keyword: text
      }, () => {
        if (text) {
          searchData.call(this);
        } else {
          fetchData.call(this);
        }
      });
    }
  };

  handleClearAll = () => {
    const { tabActiveLabel } = this.state;
    const { allTreeData } = this.props;
    this.setState({
      keyword: '',
      current: 1,
      data: []
    }, () => {
      if (tabActiveLabel === '树形' && !allTreeData) {
        this.handleLoadData({
          props: {
            dataRef: {
              key: 0,
              initData: true
            }
          }
        });
      } else {
        fetchData.call(this);
      }
    });
  };

  /** 树形数据异步载入 */
  handleLoadData = (treeNode) => {
    const { props: { dataRef } } = treeNode;
    return new Promise((resolve) => {
      if (treeNode.props.children) {
        resolve();
        return;
      }
      fetchTreeTypeDataAsync.call(this, { dataRef });
      resolve();
    });
  }

  render() {
    const { data, current, total, transValue, tabActiveLabel } = this.state;
    const { id, type, params, value, allTreeData, pageSize, ...rest } = this.props;
    const pagination = {
      current,
      pageSize,
      total,
      onChange: this.paginationOnchange
    };

    return (
      <DictSelect
        {...rest}
        placeholder="请选择..."
        data={data}
        pagination={pagination}
        value={transValue}
        labelInValue // "SinoGear": 1.1.2及以下不支持
        searchDelay={1000}
        loading
        allowClear
        showSearch={tabActiveLabel === DefaultActiveLabel}
        mixinFilter
        mixinModel={DefaultMixinModel}
        onTabChange={this.handleTabChange}
        onFocus={this.handleFocus}
        onChange={this.handleChange}
        onSearch={this.handleSearch}
        onClearAll={this.handleClearAll}
        onLoadData={allTreeData ? null : this.handleLoadData}
      />
    );
  }
}

GAMixinSelect.defaultProps = {
  ...GAMixinSelect.defaultProps,
  pageSize: 10, // 默认分页内条数10
};

GAMixinSelect.propTypes = {
  ...GAMixinSelect.propTypes,
  pageSize: PropTypes.number, // 分页内条数
  allTreeData: PropTypes.bool, // 树形数据是否全部加载
};
