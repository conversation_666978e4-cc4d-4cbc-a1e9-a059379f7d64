import React, {useState} from 'react';
import { connect } from 'dva';
import moment from 'moment';
import zhCN from 'antd/es/locale/zh_CN';
import { ConfigProvider } from 'antd';
import styles from './index.css';
import loading from '../compoments/loading/index';
import { getDatabaseLocalTimePort } from '../utils/func';

window['$loading'] = loading;
function BasicLayout(props) {
  const { location } = props;
  const {
    query: {  token }
  } = location;
  const [hasResetMoment, setHasResetMoment] = useState(false);
  (async () => {
    if (!hasResetMoment) {
      await getDatabaseLocalTimePort({ token });
      /**
       * 根据数据库时间重新设置moment()/moment.now()的值
       * @returns {moment.Moment}
       */
      moment.now = () => {
        let realNowTime;
        let offsetTime = sessionStorage.getItem('offsetTime');
        // eslint-disable-next-line radix
        offsetTime = parseInt(offsetTime);
        if (offsetTime) {
          realNowTime = new Date().getTime() + offsetTime;
        } else {
          realNowTime = new Date().getTime();
        }
        const now = moment(realNowTime);
        // eslint-disable-next-line no-underscore-dangle
        delete now._i;
        return now;
      };
      setHasResetMoment(true);
    }
  })();
  return (
    <ConfigProvider locale={zhCN}>
      <div className={styles.normal}>{props.children}</div>
    </ConfigProvider>
  );
}

export default connect(({ wszh }) => {
  return {
    wszh
  };
})(BasicLayout);
