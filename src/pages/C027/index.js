import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { getDataConfig } from '../C027/reportConfig/C027';
import styles from './index.less';

const SSWSNAME = '拘留证';
const FILENAME = 'C027';

@connect(({ C027 }) => C027)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'C027/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page, dept = '' }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig({ ...formData, dept })}
          wsData={formData}
          formConfig={formConfig}
          showPage={page}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          isQM
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport
          dataConfig={getDataConfig({ ...formData, dept })}
          wsData={formData}
          sswsname={SSWSNAME}
          {...this.props}
        />
      );
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
