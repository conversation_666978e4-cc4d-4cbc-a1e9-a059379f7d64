import React, { Component } from 'react';
import { connect } from 'dva';
import { countFontSize, drawLine, digitUppercase } from '../utils/func';
import commonStyles from '../lesses/index.less';
import styles from './index.less';

class Index extends Component {
  constructor() {
    super();
    this.state = {
      rowNum: 14
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'list/queryList',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    }).then(() => {
      const { rowNum } = this.state;
      const { list } = this.props;
      const { wpclqdxmList = [] } = list.listData;
      const y = (rowNum - wpclqdxmList.length) * 50;
      drawLine(0, 0, 664, y, 'lineCanvas');
      drawLine(0, 0, 106, 50, 'wptz');
      drawLine(0, 0, 119, 50, 'wply');
      drawLine(0, 0, 92, 50, 'clyy');
    });
  }
  buildBlankBox = () => {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [] } = listData;
    const blankBox = [];
    for (let i = 0; i < rowNum - wpclqdxmList.length; i++) {  // eslint-disable-line
      blankBox.push(
        <tr>
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
        </tr>
      );
    }
    return blankBox;
  };
  render() {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [] } = listData;
    const canvasHeight = (rowNum - wpclqdxmList.length) * 50;
    const listView = listData.systemid ? (
      <div className={commonStyles.normal}>
        <div className={commonStyles.wsTitle}>销毁清单</div>
        <table className={commonStyles.table}>
          <tbody>
            <tr>
              <th style={{ width: '7%' }}>编号</th>
              <th style={{ width: '18%' }}>名称</th>
              <th style={{ width: '10%' }}>数量</th>
              <th style={{ width: '16%' }}>特征</th>
              <th style={{ width: '18%' }}>来源</th>
              <th style={{ width: '14%' }}>销毁理由</th>
              <th style={{ width: '17%' }}>备注</th>
            </tr>
            {wpclqdxmList.map((item, index) => (
              <tr>
                <td>{index + 1}</td>
                <td style={{ fontSize: countFontSize(item.wpmc, 120, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wpmc || ''}
                </td>
                <td
                  style={{
                    fontSize: countFontSize(digitUppercase(item.wpsl || '', item.jldw || ''), 67, 50, 19, 'px'),
                    lineHeight: '1'
                  }}
                >
                  {digitUppercase(item.wpsl || '', item.jldw || '')}
                </td>
                <td style={{ fontSize: countFontSize(item.wptz, 106, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wptz || <canvas className="wptz" width={106} height={50} />}
                </td>
                <td style={{ fontSize: countFontSize(item.wply, 119, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wply || <canvas className="wply" width={119} height={50} />}
                </td>
                <td style={{ fontSize: countFontSize(item.clyy, 92, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.clyy || <canvas className="clyy" width={92} height={50} />}
                </td>
                <td style={{ fontSize: countFontSize(item.bz, 112, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.bz || ''}
                </td>
              </tr>
            ))}
            {this.buildBlankBox()}
            <tr>
              <td colSpan={7} className={styles.footerTd}>
                <div className={styles.footerDiv}>
                  <div style={{ height: '100px' }}>批 准 人：</div>
                  <div className={styles.footerRq}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
                <div className={styles.footerDiv}>
                  <div style={{ height: '100px' }}>办 案 人：</div>
                  <div className={styles.footerRq}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
                <div className={styles.footerDiv}>
                  <div style={{ height: '100px' }}>监 销 人：</div>
                  <div className={styles.footerRq}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
          <canvas
            className="lineCanvas"
            width={664}
            height={canvasHeight}
            style={{ position: 'absolute', bottom: 140 }}
          />
        </table>
        <div className={commonStyles.footer}>本清单附卷。</div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ list }) => ({
  list
}))(Index);
