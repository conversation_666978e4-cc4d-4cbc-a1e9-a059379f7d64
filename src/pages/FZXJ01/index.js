import React, { Component } from 'react';
// import {Form} from 'antd';
import { connect } from 'dva';
import Toolbar from '../../compoments/wenshu/Toolbar';
import styles from './index.less';
import { config } from '@/common/config';

const SSWSNAME = '收容教养犯罪少年呈批表';
const FILENAME = 'FZXJ01';

// @Form.create()
@connect(({ FZXJ01 }) => FZXJ01)
class Index extends Component {
  state = {
    scale: 100
  };

  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'FZXJ01/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token: token,
        filename: FILENAME,
        type
      }
    });
  }

  handlePrintReport = () => {
    const {
      fileName,
      location: { query }
    } = this.props;

    const { wskey, token, type } = query;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    window.open(
      `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`
    ,
      '_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0');
  };
  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  render() {
    // const {getFieldDecorator} = this.props.form;
    const { formData, location } = this.props;
    const {
      query: { type }
    } = location;
    return (
      <div>
        <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
          <Toolbar
            style={{ display: type === 'edit' ? 'inherit' : 'none' }}
            onSliderChange={this.onSliderChange}
            handleScaleReport={this.handleScaleReport}
            handlePrintReport={this.handlePrintReport}
            handlePreviewReport={this.handlePreviewReport}
            scale={this.state.scale}
          />
          <div className={styles.wsTitle}>
            <span>收容教养犯罪少年呈批表</span>
          </div>
          <table className={styles.table}>
            <tbody>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>案 由</td>
                <td colSpan={5}>{formData.briefreason}</td>
                <td rowSpan={5} style={{ width: '130px' }}></td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>姓 名</td>
                <td>{formData.zwxm}</td>
                <td>曾 用 名</td>
                <td>{formData.cym}</td>
                <td>性别</td>
                <td>{formData.sex}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>家庭出身</td>
                <td>{formData.sf}</td>
                <td>有无前科</td>
                <td>{formData.qbhslx}</td>
                <td>年龄</td>
                <td>{formData.age}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>健康状况</td>
                <td>{formData.jkzh}</td>
                <td>文化程度</td>
                <td>{formData.culture}</td>
                <td>民族</td>
                <td>{formData.nation}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>籍 贯</td>
                <td>{formData.nativeplace}</td>
                <td>
                  所在学校
                  <br />
                  或街道
                </td>
                <td colSpan={3}>{formData.xzdz}</td>
              </tr>
            </tbody>
          </table>
          <table className={styles.secTable}>
            <tbody>
              <tr className={styles.normalTr}>
                <td style={{ width: '113px' }}>住 址</td>
                <td>{formData.xzdz}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ height: '80px' }}>简 历</td>
                <td>{formData.resume}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ height: '80px' }}>
                  家庭成员
                  <br />及<br />
                  经济状况
                </td>
                <td>{formData.transact}</td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ height: '400px' }}>
                  违<br />法<br />犯<br />罪<br />事<br />实<br />及<br />证<br />据
                </td>
                <td>{formData.analysiscase && formData.analysiscase.substring(0)}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
          <Toolbar
            style={{ display: type === 'edit' ? 'inherit' : 'none' }}
            onSliderChange={this.onSliderChange}
            handleScaleReport={this.handleScaleReport}
            handlePrintReport={this.handlePrintReport}
            handlePreviewReport={this.handlePreviewReport}
            scale={this.state.scale}
          />

          <table className={styles.table}>
            <tbody>
              <tr>
                <td style={{ height: '180px' }}>
                  <div className={styles.wsTitle} style={{ marginTop: '-30px' }}>
                    收容教养的法律依据和期限
                  </div>
                  <div style={{ marginTop: '10px', marginLeft: '20px' }}>{formData.analysisinlaw}</div>
                  <div style={{ marginTop: '10px', marginLeft: '20px' }}>
                    承办人： <span style={{ paddingLeft: '380px' }}>{formData.submittime}</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <table className={styles.secTable}>
            <tbody>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>
                  办案单位
                  <br />
                  意见
                </td>
                <td>
                  <div className={styles.tableDiv}>{formData.yj1}</div>
                  <div className={styles.tableDiv2}>
                    负责人：
                    <span className={styles.tableSpan}>{formData.submittime}</span>
                  </div>
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>
                  县局法制
                  <br />
                  部门意见
                </td>
                <td>
                  <div className={styles.tableDiv}>{formData.yj2}</div>
                  <div className={styles.tableDiv2}>
                    负责人：
                    <span className={styles.tableSpan}>{formData.submittime}</span>
                  </div>
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>
                  县级公安
                  <br />
                  机关意见
                </td>
                <td>
                  <div className={styles.tableDiv}>{formData.yj3}</div>
                  <div className={styles.tableDiv2}>
                    负责人：
                    <span className={styles.tableSpan}>{formData.submittime}</span>
                  </div>
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>
                  市级公安
                  <br />
                  机关意见
                </td>
                <td>
                  <div className={styles.tableDiv}>{formData.yj4}</div>
                  <div className={styles.tableDiv2}>
                    负责人：
                    <span className={styles.tableSpan}>{formData.submittime}</span>
                  </div>
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td style={{ height: '100px' }}>备注：</td>
                <td>{formData.note}</td>
              </tr>
              <div style={{ width: '664px', marginTop: '10px', marginLeft: '20px' }}>
                <span>办案单位：{formData.transactunit}1111</span>
                <span style={{ paddingLeft: '380px' }}>{formData.writetime}2222</span>
              </div>
            </tbody>
          </table>
        </div>
      </div>
    );
  }
}

export default Index;
