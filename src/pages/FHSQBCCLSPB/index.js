/**
 * Created by yam on 2021-09-13
 * 功能描述：刑事复议/复核申请补充材料审批表   报告书打印预览
 */

import React, { Component, createRef } from 'react';
import { connect } from 'dva';
import { Table } from 'antd';
import moment from 'moment';
import Sign from '@/compoments/seal/Sign';
import Seal from '@/compoments/seal/Seal';
import styles from './index.less';
import { url } from '@/utils/func';

@connect(({ bgs }) => bgs)
class Index extends Component {
  constructor(props) {
    super(props);
    this.ajReasonRef = createRef();
    this.state = {
      ajReasonFontSize: 16,
      isCutPage: false
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token
      }
    }).then(() => {
      this.setCutPage();
    });
  }
  componentDidUpdate() {}
  setCutPage = () => {
    const mainText = document.getElementById('mainText');
    const inscribeTable = document.getElementById('inscribeTable');
    const mainTextHeight = mainText.clientHeight;
    const inscribeTableHeight = inscribeTable.clientHeight + 50;
    if ((mainTextHeight % 985) + inscribeTableHeight > 985) {
      this.setState({
        isCutPage: true
      });
    } else {
      this.setState({
        isCutPage: false
      });
    }
  };
  render() {
    const { isCutPage } = this.state;
    const { bgsData } = this.props;
    const {
      analysiscase = '',
      ms = '',
      reservation04: sswsmc,
      ldps1 = '',
      ldps2 = '',
      cqrq = '',
      cqdw = '',
      cqrxm1 = '',
      cqrqm1 = '',
      cqrxm2 = '',
      cqrqm2 = ''
    } = bgsData;
    const sswsname = url.getHashParam('sswsname') || '刑事复议/复核申请补充材料审批表';
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div id="mainText">
          <table className={styles.table}>
            <tr style={{ height: '120px' }}>
              <td rowSpan={2} className={styles.lable} style={{ width: '15%' }}>
                领导
                <br />
                批示
              </td>
              <td className={styles.psyj} style={{ borderBottom: 0 }}>
                {ldps1}
              </td>
            </tr>
            <tr style={{ height: '40px' }}>
              <td style={{ borderTop: 0, textAlign: 'right', position: 'relative' }} />
            </tr>
            <tr style={{ height: '120px' }}>
              <td rowSpan={2} className={styles.lable}>
                办案
                <br />
                部门
                <br />
                意见
              </td>
              <td className={styles.psyj} style={{ borderBottom: 0 }}>
                {ldps2}
              </td>
            </tr>
            <tr style={{ height: '40px' }}>
              <td style={{ borderTop: 0, textAlign: 'right', position: 'relative' }} />
            </tr>
          </table>
          <div className={styles.wsTitle}>{sswsname}</div>
          <div className={styles.content}>
            <pre className={styles.analysisCaseContent}>{ms || analysiscase}</pre>
          </div>
        </div>
        <div className={isCutPage ? styles.cutPage : ''} />

        <div className={styles.inscribe}>
          <div id="inscribeTable">
            {bgsData.ms.replace(/\s+/g, '').length > 0 && (
              <table className={styles.inscribeTable}>
                <tbody>
                  <tr>
                    <td className={styles.inscribeLabel}>呈请单位：</td>
                    <td colSpan={2}>{cqdw || ''}</td>
                  </tr>
                  <tr style={{ lineHeight: '42pt' }}>
                    <td>呈请人：</td>
                    <td>{cqrqm1 ? <Sign imageBase64={cqrqm1} style={{ position: '' }} /> : <span>{cqrxm1}</span>}</td>
                    <td>{cqrqm2 ? <Sign imageBase64={cqrqm2} style={{ position: '' }} /> : <span>{cqrxm2}</span>}</td>
                  </tr>
                  <tr>
                    <td colSpan={3}>{cqrq || ''}</td>
                  </tr>
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
