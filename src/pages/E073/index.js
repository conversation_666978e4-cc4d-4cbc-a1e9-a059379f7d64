import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { dataConfig, changePageDataConfig } from './reportConfig/E073';
import styles from './index.less';

const SSWSNAME = '变更逮捕措施通知书';
const FILENAME = 'E073';

@connect(({ E073 }) => E073)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'E073/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type !== '') {
      const zwxm = formData.zwxm ? formData.zwxm.length : 0;
      const zeroParagh = formData.superwritid ? formData.superwritid.length : 0;
      const firstParagh = zwxm * 2 + (formData.briefreason ? formData.briefreason.length : 0);
      const secondParagh =
        (formData.itemoflaw ? formData.itemoflaw.length : 0) + (formData.csid ? formData.csid.length : 0);
      const totalLen = zeroParagh + firstParagh + secondParagh;
      const isBrokenPage = totalLen > 134;
      const newDataConfig = isBrokenPage ? changePageDataConfig : dataConfig;
      if (type === 'edit') {
        reportView = (
          <WsReport
            dataConfig={newDataConfig}
            wsData={formData}
            formConfig={formConfig}
            showPage={page}
            fileName={FILENAME}
            sswsname={SSWSNAME}
            isQM
            {...this.props}
          />
        );
      } else if (type === 'print' || type === 'preview') {
        reportView = <WsPrintReport dataConfig={newDataConfig} wsData={formData} sswsname={SSWSNAME} {...this.props} />;
      }
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
