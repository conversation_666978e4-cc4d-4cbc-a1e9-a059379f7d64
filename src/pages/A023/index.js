import React, { Component } from 'react';
import { connect } from 'dva';
import { Spin } from 'antd';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { getDataConfig } from '../A023/reportConfig/A023';
import styles from './index.less';

const SSWSNAME = '行政案件受案联系函';
const FILENAME = 'A023';
const WSCODE = 'A023';

@connect(({ A023 }) => A023)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type, ajbh } = query;

    if (wskey) {
      this.props.dispatch({
        type: 'A023/fetch',
        payload: {
          systemid: wskey,
          wsmc: SSWSNAME,
          token,
          filename: FILENAME,
          type
        }
      });
    } else {
      this.props.dispatch({
        type: 'A023/getinitFunc',
        payload: {
          systemid: wskey,
          wsmc: SSWSNAME,
          token,
          wscode: WSCODE,
          ajbh,
          filename: FILENAME,
          type
        }
      });
    }
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page, ajbh }
    } = location;
    let reportView = '';
    if (!Object.keys(formData).length) {
      reportView = <Spin spinning />;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig(formData)}
          wsData={formData}
          formConfig={formConfig}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          extraData={{ wscode: WSCODE, wsmc: SSWSNAME, state: '1' }}
          showPage={page}
          isQM
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={getDataConfig(formData)} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查 {type}</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
