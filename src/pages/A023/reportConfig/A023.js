import uuidv4 from 'uuid/v4';

export const getDataConfig = () => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 0,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '行政案件\n受案联系函（存根）',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '48px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '18pt'
                },
                fields: []
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: ' 案件名称: <%AJMC%>\n 被侵害人（单位）: <%BYNAME%>\n 被侵害人联系方式: <%XB2%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px 0',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'AJMC', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'BYNAME', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'XB2', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: ' 办案民警: <%TRANSACT%>\n 送达方式: <%DW2%>\n 邮寄送达凭证: <%XXZZ%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  marginLeft: '340px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'TRANSACT', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  marginTop: '150px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '--------------------------------------------------------',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'center',
                  marginTop: '180px',
                  overflow: 'hidden',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: []
              }
            ]
          },
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '警民联系函',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '-50px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                },
                fields: []
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%BYNAME%>：\n' +
                  '    您好！<%AJMC%>一案，我局已于<%LASJ1%>年<%LASJ2%>月<%LASJ3%>日受理，并已开展调查工作。' +
                  '我们将严格履行职责，在对案件进行调查取证的基础上，依法作出处理。在此过程中，我们将充分尊重和' +
                  '保障您（您单位）的合法权益。如果您（您单位）有新的线索、证据，请及时与我们联系。如果民警在办案' +
                  '过程中有违法违纪问题，您（您单位）可以向监督部门反映。\n    警民关系的和谐是我们共同的愿望，公正、' +
                  '文明执法是我们的郑重承诺！真诚地希望您（您单位）能理解和支持我们的工作，并提出意见和建议 。',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  marginTop: '-30px',
                  padding: '20px',
                  lineHeight: 1.8,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'BYNAME', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'AJMC', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'LASJ1', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'LASJ2', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'LASJ3', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: ' 办案民警: <%TRANSACT%>\n 监督部门: <%RECEIVEUNIT%>警务督察大队',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  marginTop: '360px',
                  padding: '20px',
                  lineHeight: 1.8,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'TRANSACT', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'RECEIVEUNIT', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: ' 联系电话：<%TOSOMEBODY%>\n 监督电话：<%BADWLXDH%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  marginTop: '360px',
                  marginLeft: '350px',
                  padding: '20px',
                  lineHeight: 1.8,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'TOSOMEBODY', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'BADWLXDH', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%TRANSACTUNIT1%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  marginTop: '450px',
                  // marginLeft: '350px',
                  padding: '20px',
                  lineHeight: 1.8,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  { key: uuidv4(), textName: 'TRANSACTUNIT1', textValue: '', textType: 'string', elementStyle: {} }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  marginTop: '490px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
