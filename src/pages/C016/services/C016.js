import request from '../../../utils/request';
import { config } from '../../../common/config';
import { io } from "SinoGear/lib/services/SGCore";

export function fetchData({ systemid, wsmc, token, type }) {
  return request(`${config.contextPath}/api/b_asj_ws_news/ssws/${systemid}?sswsname=${wsmc}&type=${type}`, {
    headers: {
      Authorization: token
    }
  });
}

export function updateData({ formData, token }) {
  return request(`${config.contextPath}/api/b_asj_wss/patch`, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/sino-patch'
    },
    body: JSON.stringify(formData),
    method: 'PUT'
  });
}

export function fetchConfig({ filename, token }) {
  return request(`${config.contextPath}/api/ws/b_asj_wschangeareas/filename/${filename}`, {
    headers: {
      Authorization: token
    }
  });
}

// 根据保证人id获取保证人信息
export function getBzrInfoById({ id }) {
  return io.get(`${config.ajblContextPath}/api/b_asj_baszqts/${id}/baszqts`);
}
