import uuidv4 from 'uuid/v4';
import moment from "moment";

/**
 * 取保候审保证书模板
 * */
export const dataConfig = (formData, dispatch) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '一式两份，一份附卷，一份交保证人。',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '取保候审保证书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '34px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    我叫<%XM2%>，性别<%XB2%>，出生日期<%CSRQ2%>，现住<%XXZZ%>，' +
                  '身份证件名称：<%PERFORMBY%>' +
                  '，证件号码：<%NOTE%>，单位及职业：<%DW2%>，<%ZY2%>，联系方式：<%LXFS2%>' +
                  '，与犯罪嫌疑人<%ZWXM%>是<%GXLX%>关系。' +
                  '\n    我自愿作如下保证：\n' +
                  '    监督犯罪嫌疑人在取保期间遵守下列规定：\n' +
                  '    （一）未经执行机关批准不得离开所居住的市、县；\n' +
                  '    （二）住址、工作单位和联系方式发生变动的，在二十四小时以内向执行机关报告；\n' +
                  '    （三）在传讯的时候及时到案；\n' +
                  '    （四）不得以任何形式干扰证人作证；\n' +
                  '    （五）不得毁灭、伪造证据或者串供。\n' +
                  '    监督犯罪嫌疑人遵守以下规定\n' +
                  '    （一）不得进入<%ZXDDXZ%>等场所；\n' +
                  '    （二）不得与<%ZXDXXX%>会见或者通信；\n' +
                  '    （三）不得从事<%ZXDDMC%>等活动；\n' +
                  '    （四）将<%ZDDDDD%>证件交执行机关保存。\n' +
                  '    本人未履行保证义务的，愿承担法律责任。\n' +
                  '    此致',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 1.6,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XM2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'dynamic',
                      params: {
                        configId: 'v_bzr_xm',
                        ajbh: ''
                      }
                    },
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XB2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'static',
                      kind: '01'
                    },
                    elementStyle: { fontSize: '16pt' }
                  },
                  { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                  { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                  {
                    key: uuidv4(),
                    textName: 'PERFORMBY',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: { type: 'static', kind: 'wg_03' },
                    elementStyle: {}
                  },
                  { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                  {
                    key: uuidv4(),
                    textName: 'ZY2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: { type: 'static', kind: '08' },
                    elementStyle: {}
                  },
                  { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  paddingLeft: '22px',
                  lineHeight: 2,
                  left: 0,
                  top: '-20px',
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '保证人：              \n年   月   日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '20px',
                  // lineHeight: 2,
                  left: 0,
                  top: '30px',
                  fontSize: '16pt'
                }
              }
            ]
          }
          // {
          //   key: uuidv4(),
          //   editable: true,
          //   contents: [
          //     {
          //       key: uuidv4(),
          //       type: 'bigText', // 大文本段落
          //       tempStr: '保证人：              \n年   月   日',
          //       elementStyle: {
          //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
          //         width: '100%',
          //         position: 'absolute',
          //         textAlign: 'right',
          //         paddingRight: '20px',
          //         lineHeight: 2,
          //         left: 0,
          //         top: '-60px',
          //         fontSize: '16pt'
          //       }
          //     }
          //   ]
          // }
        ]
      },
      {
        key: uuidv4(),
        footText: '一式两份，一份附卷，一份交保证人。',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '取保候审保证书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '34px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    我叫<%XM2%>，性别<%XB2%>，出生日期<%CSRQ2%>，现住<%XXZZ%>，' +
                  '身份证件名称：<%PERFORMBY%>' +
                  '，证件号码：<%NOTE%>，单位及职业：<%DW2%>，<%ZY2%>，联系方式：<%LXFS2%>' +
                  '，与犯罪嫌疑人<%ZWXM%>是<%GXLX%>关系。' +
                  '\n    我自愿作如下保证：\n' +
                  '    监督犯罪嫌疑人在取保期间遵守下列规定：\n' +
                  '    （一）未经执行机关批准不得离开所居住的市、县；\n' +
                  '    （二）住址、工作单位和联系方式发生变动的，在二十四小时以内向执行机关报告；\n' +
                  '    （三）在传讯的时候及时到案；\n' +
                  '    （四）不得以任何形式干扰证人作证；\n' +
                  '    （五）不得毁灭、伪造证据或者串供。\n' +
                  '    监督犯罪嫌疑人遵守以下规定\n' +
                  '    （一）不得进入<%ZXDDXZ%>等场所；\n' +
                  '    （二）不得与<%ZXDXXX%>会见或者通信；\n' +
                  '    （三）不得从事<%ZXDDMC%>等活动；\n' +
                  '    （四）将<%ZDDDDD%>证件交执行机关保存。\n' +
                  '    本人未履行保证义务的，愿承担法律责任。\n' +
                  '    此致',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 1.6,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XM2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'dynamic',
                      params: {
                        configId: 'v_bzr_xm',
                        ajbh: ''
                      },
                      // onChange: (val,form) => {
                      //   dispatch({
                      //     type: 'C016/getBzrInfoById',
                      //     payload: {
                      //       id: val
                      //     },
                      //     callback: (data) => {
                      //       form.setFieldsValue({
                      //         xb2: data.xb, // 性别
                      //         csrq2: data.csrq2?moment(data.csrq2):null, // 出生日期
                      //         xxzz: data.xxdzms, // 住址
                      //         performby: data.zjzl, // 担保人证件名称
                      //         note: data.zjhm, // 证件号码
                      //         dw2: data.gzdw, // 单位
                      //         zy2: data.xrzhiy, // 职业
                      //         lxfs2: data.lxdh // 联系方式
                      //       });
                      //     }
                      //   });
                      // }
                    },
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XB2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'static',
                      kind: '01'
                    },
                    elementStyle: { fontSize: '16pt' }
                  },
                  { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                  { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                  {
                    key: uuidv4(),
                    textName: 'PERFORMBY',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: { type: 'static', kind: 'wg_03' },
                    elementStyle: {}
                  },
                  { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                  {
                    key: uuidv4(),
                    textName: 'ZY2',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: { type: 'static', kind: '08' },
                    elementStyle: {}
                  },
                  { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                  { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  paddingLeft: '22px',
                  lineHeight: 2,
                  left: 0,
                  top: '-20px',
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '保证人：              \n年   月   日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '20px',
                  // lineHeight: 2,
                  left: 0,
                  top: '30px',
                  fontSize: '16pt'
                }
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
