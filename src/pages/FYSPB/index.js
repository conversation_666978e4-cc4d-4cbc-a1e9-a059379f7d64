import React, { Component } from 'react';
import { connect } from 'dva';
import commonStyles from '../lesses/index.less';

@connect(({ approvalform }) => approvalform)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname: wsname } = query;
    dispatch({
      type: 'approvalform/queryApprovalform',
      payload: {
        wskey,
        token,
        wsname
      }
    });
  }
  render() {
    const { approvalformData } = this.props;
    const approvalformView = approvalformData.systemid ? (
      <div className={commonStyles.normal} style={{ position: 'relative' }}>
        <div className={commonStyles.wsTitle}>呈请国家赔偿刑事复议决定报告书</div>
        <table className={commonStyles.table}>
          <tbody>
            <tr>
              <td colSpan={4}>案件编号</td>
              <td colSpan={2}>{approvalformData.ajbh || ''}</td>
              <td colSpan={2}>案件名称</td>
              <td colSpan={2}>{approvalformData.ajmc || ''}</td>
              <td colSpan={1}>受理时间</td>
              <td colSpan={1}>{approvalformData.cpaturetime || ''}</td>
            </tr>

            <tr>
              <td colSpan={1} rowSpan={3} style={{ borderBottom: 0 }}>
                赔偿请求人
              </td>
              <td colSpan={3}>姓名</td>
              <td colSpan={2}>{approvalformData.zwxm || ''}</td>
              <td colSpan={2}>性别</td>
              <td colSpan={2}>{approvalformData.sex || ''}</td>
              <td colSpan={1}>出生日期</td>
              <td colSpan={1} style={{ minWidth: '180px' }}>
                {approvalformData.birthdayinbegin || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3}>身份证号</td>
              <td colSpan={2}>{approvalformData.zjhm || ''}</td>
              <td colSpan={2}>电话</td>
              <td colSpan={2}>{approvalformData.lxdh || ''}</td>
              <td colSpan={1}>邮编</td>
              <td colSpan={1} style={{ minWidth: '180px' }}>
                {approvalformData.ywm || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3}>所在地</td>
              <td colSpan={8} style={{ minWidth: '180px' }}>
                {approvalformData.xzdz || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={1}>法定代理人</td>
              <td colSpan={3}>姓名</td>
              <td colSpan={2}>{approvalformData.zxdxxx || ''}</td>
              <td colSpan={2}>电话</td>
              <td colSpan={4}>{approvalformData.badwlxdh || ''}</td>
              {/* <td colSpan={1} />
              <td colSpan={1} style={{ minWidth: '180px' }} /> */}
            </tr>

            <tr>
              <td colSpan={1} rowSpan={3}>
                赔偿请求人
              </td>
              <td colSpan={3}>法人或组织名称</td>
              <td colSpan={8}>{approvalformData.receiveby || ''}</td>
              {/* <td colSpan={2}>电话</td>
              <td colSpan={2}>{approvalformData.lxfs2 || ''}</td>
              <td colSpan={1}>邮编</td>
              <td colSpan={1} style={{ minWidth: '180px' }}>
                {approvalformData.ywx || ''}
              </td> */}
            </tr>

            <tr>
              <td colSpan={3}>住所地</td>
              <td colSpan={8} style={{ minWidth: '180px' }}>
                {approvalformData.xxzz || ''}
              </td>
            </tr>

            <tr>
              <td colSpan={3}>法定代表人/主要负责人</td>
              <td colSpan={8}>{approvalformData.sf || ''}</td>
              {/* <td colSpan={2}>电话</td>
              <td colSpan={2}>{approvalformData.tosomebody || ''}</td>
              <td colSpan={1} />
              <td colSpan={1} style={{ minWidth: '180px' }} /> */}
            </tr>

            <tr>
              <td colSpan={1} rowSpan={3}>
                委托代理人
              </td>
              <td colSpan={3}>姓名</td>
              <td colSpan={2}>{approvalformData.xm2 || ''}</td>
              <td colSpan={2}>电话</td>
              <td colSpan={2}>{approvalformData.note || ''}</td>
              <td colSpan={1}>工作单位</td>
              <td colSpan={1} style={{ minWidth: '180px' }}>
                {approvalformData.reservation10 || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3}>赔偿义务机关</td>
              <td colSpan={2}>{approvalformData.dw2 || ''}</td>
              <td colSpan={2}>地址</td>
              <td colSpan={2}>{approvalformData.xxdzms || ''}</td>
              <td colSpan={1} />
              <td colSpan={1} style={{ minWidth: '180px' }} />
            </tr>

            <tr>
              <td colSpan={3}>法定代表人/主要负责人</td>
              <td colSpan={2}>{approvalformData.frdb || ''}</td>
              <td colSpan={2}>职务</td>
              <td colSpan={2}>{approvalformData.zy2 || ''}</td>
              <td colSpan={1} />
              <td colSpan={1} style={{ minWidth: '180px' }} />
            </tr>

            <tr>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                具体复议要求
              </td>
              <td colSpan={12}>{approvalformData.briefreason || ''}</td>
            </tr>

            <tr style={{breakInside: "avoid"}}>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                查明的主要事实
              </td>
              <td colSpan={12}>{approvalformData.analysiscase || ''}</td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                事实的主要证据
              </td>
              <td colSpan={12}>{approvalformData.analysisinlaw || ''}</td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                本机关认为
              </td>
              <td colSpan={12}>{approvalformData.epilog || ''}</td>
            </tr>
            <tr style={{height:'200px',breakInside: "avoid"}}>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                执法问题
              </td>
              <td colSpan={12} />
            </tr>
            <tr style={{height:'200px',breakInside: "avoid"}}>
              <td style={{ borderBottom: 0 }} colSpan={1} rowSpan={1}>
                执法建议
              </td>
              <td colSpan={12} />
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ width: '10%' }}>承办人意见</td>
              <td colSpan={12}>
                <div style={{ paddingTop: '200px' }}>
                  <div style={{ width: '40%', display: 'inline-block', textAlign: 'left', paddingLeft: '10px' }}>
                    承办人:
                  </div>
                  <div style={{ width: '40%', display: 'inline-block', textAlign: 'right', paddingRight: '10px' }}>
                    &nbsp;&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日
                  </div>
                </div>
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ width: '10%' }}>承办单位意见</td>
              <td colSpan={12} style={{ paddingTop: '200px', textAlign: 'left', paddingLeft: '75px'  }}>
                承办人:
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ width: '10%' }}>审核部门意见</td>
              <td colSpan={12} style={{ paddingTop: '200px', textAlign: 'left', paddingLeft: '75px'  }}>
                承办人:
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td style={{ width: '10%' }}>领导审批意见</td>
              <td colSpan={12} style={{ paddingTop: '200px', textAlign: 'left', paddingLeft: '75px'  }}>
                领导:
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{approvalformView}</div>;
  }
}

export default Index;
