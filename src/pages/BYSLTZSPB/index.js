/**
 * Created by yam on 2021-10-14
 * 功能描述： 不予受理听证审批表（听证案件）   报告书打印预览
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Icon } from 'antd';
import moment from 'moment';
import styles from './index.less';
import { url } from '../../utils/func';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const sswsname = url.getHashParam('sswsname') || '不予受理听证审批表';
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div className={styles.unit}>{bgsData.gafj || ''}</div>
        <div className={styles.wsTitle}>{sswsname}</div>
        <div className={styles.header}>
          <div style={{ float: 'left', marginLeft: '8px', fontSize: '14pt', width: '53%' }}>
            填表单位：{bgsData.transactunit1 || ''}
          </div>
          <div style={{ float: 'right', marginRight: '8px', fontSize: '14pt', width: '37%' }}>
            填表日期：{bgsData.submittime || ''}
          </div>
        </div>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td colSpan={1} rowSpan="2" className={styles.lable} style={{ width: '15%' }}>
                申请人
              </td>
              <td className={styles.lable1}>姓名</td>
              <td className={styles.value1} >{bgsData.zwxm || ''}</td>
              <td className={styles.lable1}>性别</td>
              <td className={styles.value1} >{bgsData.sex || ''}</td>
              <td className={styles.lable1}>年龄</td>
              <td className={styles.value1} >{bgsData.age || ''}</td>
              <td className={styles.lable1}>职业</td>
              <td className={styles.value1} >{bgsData.profession || ''}</td>
              <td className={styles.lable1}>住址</td>
              <td colSpan={10} >{bgsData.xzdz || ''}</td>
            </tr>
            <tr>
              <td colSpan={2} className={styles.lable1}>
                法人组织和名称
              </td>
              <td colSpan={2} >{bgsData.dwmc || ''}</td>
              <td className={styles.lable1}>地址</td>
              <td colSpan={3} >{bgsData.xxdzms || ''}</td>
              <td colSpan={1} className={styles.lable1}>
                法定代表
                <br />
                人姓名
              </td>
              <td colSpan={4} >{bgsData.frdb || ''}</td>
              <td colSpan={2} className={styles.lable1}>
                职务
              </td>
              <td colSpan={4} >{bgsData.sf || ''}</td>
            </tr>

            <tr>
              <td className={styles.lable}>被申请人</td>
              <td colSpan={1} className={styles.lable1}>
                名称
              </td>
              <td colSpan={3} className={styles.value1} >{ bgsData.dw2 || ''}</td>
              <td colSpan={1} className={styles.lable1}>
                住址
              </td>
              <td colSpan={8} className={styles.lable} >{bgsData.zxddxz || ''}</td>
              <td colSpan={2} className={styles.lable1}>
                局长
              </td>
              <td colSpan={4} >{bgsData.receiveby || ''}</td>
            </tr>

            <tr>
              <td className={styles.lable}>申请方式</td>
              <td colSpan={1} className={styles.value}>
                书面
              </td>
              <td colSpan={2} className={styles.lable1}>
                申请日期
              </td>
              <td colSpan={5} className={styles.value} >{ bgsData.cpaturetime || ''}</td>
              <td colSpan={3} className={styles.lable}>
                法制部门收到日期
              </td>
              <td colSpan={8} >{ bgsData.receivetime || ''}</td>
            </tr>
            <tr className={styles.rowHeight}>
              <td className={styles.lable}>不予受理理由</td>
              <td colSpan={19} >{ bgsData.transact || ''}</td>
            </tr>
            <tr className={styles.rowHeight}>
              <td className={styles.lable}>承办人意见</td>
              <td colSpan={19} className={styles.qianming}>
                <p>{bgsData.transactclog || ''}</p>
                <div>
                  <span>签名：{bgsData.transactprimarytout || ''}</span>
                  <span>日期：</span>
                </div>
              </td>
            </tr>
            <tr className={styles.rowHeight} >
              <td className={styles.lable}>法制部门负责人意见</td>
              <td colSpan={19} className={styles.qianming}>
                <div>
                  <span>负责人：</span>
                  <span>日期：</span>
                </div>
              </td>
            </tr>
            <tr className={styles.rowHeight}  style={{pageBreakInside:'avoid'}}>
              <td className={styles.lable}>领导批示</td>
              <td colSpan={19} className={styles.qianming}>
                <div>
                  <span>负责人：</span>
                  <span>日期：</span>
                </div>
              </td>
            </tr>
            <tr className={styles.rowHeight2}>
              <td className={styles.lable}>备注</td>
              <td colSpan={19} style={{ border: 0 }} >{ bgsData.note || ''}</td>
            </tr>
          </tbody>
        </table>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;


