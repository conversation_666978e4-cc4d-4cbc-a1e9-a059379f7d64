import React, { Component } from 'react';
import { Form, Input, message, LocaleProvider, DatePicker } from 'antd';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import moment from 'moment';
import { connect } from 'dva';
import { config } from '@/common/config';
import Toolbar from '../../compoments/wenshu/ToolbarQM';
import Seal from '@/compoments/seal/Seal';
import styles from './index.less';

const SSWSNAME = '提讯提解证';
const FILENAME = 'A010';
const WSCODE = 'A021';

@Form.create()
@connect(({ A010 }) => A010)
class Index extends Component {
  state = {
    scale: 100
  };

  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      // 获取文书的信息
      type: 'A010/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        wscode: WSCODE,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {
    const {
      location: { query },
      dispatch,
      form
    } = this.props;
    const { wskey, token, type, sswsname: wsmc } = query;
    if (wskey) {
      dispatch({
        // 预览文书做一个信息回填
        type: 'A010/fetch',
        payload: { systemid: wskey, wsmc, token, type }
      }).then(() => {
        const { formData } = this.props;
        const { writetime, beginperformtime, endperformtime, bank = '' } = formData;
        form.setFieldsValue({
          writetime: !writetime ? '' : moment(writetime),
          beginperformtime: !beginperformtime ? '' : moment(beginperformtime),
          endperformtime: !endperformtime ? '' : moment(endperformtime),
          bank
        });
      });
    }
  }

  handlePrintReport = (DSQM) => {
    // 打印预览
    const {
      location: { query }
    } = this.props;

    const { wskey, token } = query;
    const { wsreportPrintContextPath, wsreportContextPath, qmContextPath, isOpenQM } = config;
    const { fileName, sswsname, printUrl } = this.props;
    if (DSQM && isOpenQM) {
      window.open(
        /* eslint-disable-next-line */
        `${qmContextPath}?url=${wsreportContextPath}/${FILENAME}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}&fileName=${FILENAME}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
        // `http://192.168.1.104:3000/print?url=http://192.168.1.10:8002/A002&type=print&wskey=PCS37201302260000000000184156&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODE3MjUzMX0.axgbBRqBHFNWwSpLvzKvyWp1Y5sTQPR6LwTngnPMQXo`,
      );
    } else {
      window.open(
        `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${FILENAME}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
      );
    }
  };
  handleScaleReport = (type) => {
    // 缩放功能
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  handleSubmit = (e) => {
    // 工具栏（Toolbar）的 保存
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (err) return false;
      const {
        fileName,
        sswsname,
        location: { query }
      } = this.props;
      const { wskey, token, ajbh, type } = query;
      const dealData = {
        writetime: values.writetime ? moment(values.writetime).format('YYYY-MM-DD hh:mm:ss') : '',
        beginperformtime: values.beginperformtime ? moment(values.beginperformtime).format('YYYY-MM-DD hh:mm:ss') : '',
        endperformtime: values.endperformtime ? moment(values.endperformtime).format('YYYY-MM-DD hh:mm:ss') : ''
      };
      if (wskey) {
        const formData = {
          ...values,
          ...dealData,
          systemid: wskey
        };
        this.props
          .dispatch({
            type: `A010/updateData`,
            payload: {
              token,
              formData,
              systemid: wskey,
              filename: fileName,
              wsmc: sswsname
            }
          })
          .then(() => {
            message.info('保存成功');
            window.location.reload();
          });
      } else {
        const { formData: initFormData } = this.props;
        let formData = { ...values, ...dealData, ajbh, wscode: WSCODE, wsmc: SSWSNAME, state: '1' };
        formData = { ...formData, ...initFormData };
        this.props
          .dispatch({
            type: `A010/createData`,
            payload: {
              token,
              formData,
              filename: fileName,
              wsmc: sswsname
            }
          })
          .then((res) => {
            console.info('res', res);
            window.location = `/A010?wskey=${res.systemid}&token=${token}&type=${type}`;
          });
      }
    });
  };

  onSliderChange = (value) => {
    // 拖动缩放
    this.setState({
      scale: value
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { formData, location } = this.props;
    const {
      query: { type }
    } = location;

    const { spdwyz = '' } = formData;
    return (
      <LocaleProvider locale={zh_CN}>
        <div>
          <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
            <Form layout="inline" onSubmit={this.handleSubmit} style={{ marginTop: type === 'edit' ? '60px' : 0 }}>
              <Toolbar
                style={{ display: type === 'edit' ? 'inherit' : 'none' }}
                onSliderChange={this.onSliderChange}
                handleScaleReport={this.handleScaleReport}
                handlePrintReport={this.handlePrintReport}
                handlePreviewReport={this.handlePreviewReport}
                scale={this.state.scale}
                {...this.props}
              />
              {/* <div>{type === 'edit' ? getFieldDecorator('bank')(<Input placeholder="" />) : formData.bank}</div> */}
              <div className={styles.headDiv1}>{formData.gafj}</div>
              <div className={styles.wsTitle}>
                <span>提讯提解证</span>
              </div>
              <div className={styles.headDiv2} style={{ position: 'relative' }}>
                发证日期：
                {type === 'edit'
                  ? getFieldDecorator('writetime')(<DatePicker placeholder="" format="YYYY-MM-DD" />)
                  : moment(formData.writetime).format('YYYY年MM月DD日')}
                <Seal imageBase64={spdwyz} style={{ right: '10px', top: '-55px' }} />
              </div>
              <table className={styles.table}>
                <tbody>
                  <tr className={styles.normalTr}>
                    <td>犯罪嫌疑人</td>
                    <td>{formData.zwxm}</td>
                    <td style={{ width: '60px' }}>性别</td>
                    <td style={{ width: '60px' }}>{formData.sex}</td>
                    <td style={{ width: '100px' }}>出生日期</td>
                    <td>{moment(formData.birthdayinbegin).format('YYYY年MM月DD日')}</td>
                  </tr>
                  <tr className={styles.normalTr}>
                    <td>法定羁押期限</td>
                    <td colSpan={5}>
                      {/* 自{formData.beginperformtime}至{formData.endperformtime} */}自
                      {type === 'edit'
                        ? getFieldDecorator('beginperformtime')(<DatePicker placeholder="" />)
                        : moment(formData.beginperformtime).format('YYYY年MM月DD日')}
                      至
                      {type === 'edit'
                        ? getFieldDecorator('endperformtime')(<DatePicker placeholder="" />)
                        : moment(formData.endperformtime).format('YYYY年MM月DD日')}
                    </td>
                  </tr>
                </tbody>
              </table>
              <table className={styles.secTable}>
                <tbody>
                  <tr className={styles.normalTr}>
                    <td rowSpan={8} style={{ width: '147.5px', padding: '0px', height: '600px' }}>
                      羁押期限
                      <br />
                      变更情况
                    </td>
                    <td style={{ padding: '0px' }}>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>变更原因</td>
                        <td className={styles.tableTd2} />
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>新的起止时间</td>
                        <td className={styles.tableTd2}>
                          自&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日至&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日
                          <div className={styles.tableTdDiv}>填写人</div>
                        </td>
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>变更原因</td>
                        <td className={styles.tableTd2} />
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>新的起止时间</td>
                        <td className={styles.tableTd2}>
                          自&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日至&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日
                          <div className={styles.tableTdDiv}>填写人</div>
                        </td>
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>变更原因</td>
                        <td className={styles.tableTd2} />
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>新的起止时间</td>
                        <td className={styles.tableTd2}>
                          自&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日至&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日
                          <div className={styles.tableTdDiv}>填写人</div>
                        </td>
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1}>变更原因</td>
                        <td className={styles.tableTd2} />
                      </tr>
                      <tr className={styles.normalTr}>
                        <td className={styles.tableTd1} style={{ borderBottom: 'none' }}>
                          新的起止时间
                        </td>
                        <td className={styles.tableTd2} style={{ borderBottom: 'none' }}>
                          自&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日至&ensp;&ensp;&ensp;&ensp;&ensp;年&ensp;&ensp;&ensp;月&ensp;&ensp;&ensp;日
                          <div className={styles.tableTdDiv}>填写人</div>
                        </td>
                      </tr>
                    </td>
                  </tr>
                </tbody>
              </table>
            </Form>
          </div>
          <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
            <table className={styles.table}>
              <tbody>
                <tr className={styles.normalTr}>
                  <td>
                    提讯、提解
                    <br />
                    时间
                  </td>
                  <td>
                    提讯、提解
                    <br />
                    人员
                  </td>
                  <td>
                    收监或回
                    <br />
                    所时间
                  </td>
                  <td>
                    看守所值班
                    <br />
                    民警签名
                  </td>
                  <td>备注</td>
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
                <tr className={styles.normalTr}>
                  <td style={{ height: '100px' }}>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td className={styles.table2Td}>
                    <tr>
                      <td className={styles.tableTrTd1} />
                    </tr>
                    <tr>
                      <td className={styles.tableTrTd2} />
                    </tr>
                  </td>
                  <td>
                    &ensp;&ensp;年&ensp;&ensp;月&ensp;&ensp;日
                    <br />
                    &ensp;&ensp;时&ensp;&ensp;分
                  </td>
                  <td />
                  <td />
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </LocaleProvider>
    );
  }
}

export default Index;
