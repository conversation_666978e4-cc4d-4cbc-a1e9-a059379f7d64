import uuid4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuid4(),
      footText: '',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '$F{GAFJ}',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              },
              fields: [
                [
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              ]
            },
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '监视居住',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '88px',
                // marginLeft: '-50px',
                left: '210px',
                height: '53px',
                // textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '88px',
                left: '410px',
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '决 定',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '84px',
                // marginLeft: '45px',
                left: '350px',
                height: '53px',
                textAlign: 'center',
                fontSize: '12pt'
              }
            },
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '执行通知',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '106px',
                // marginLeft: '45px',
                left: '335px',
                height: '53px',
                textAlign: 'center',
                fontSize: '12pt'
              }
            },
            {
              key: uuid4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '126px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '$F{WSZH}',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '173px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              },
              fields: [
                [
                  {
                    key: uuid4(),
                    textName: 'WSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: { display: 'inherit' },
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '案件名称：<%AJMC%>\n' +
                '案件编号：<%AJBH%>\n' +
                '被监视居住人：<%ZWXM%>  <%SEX%>\n' +
                '出生日期：<%BIRTHDAYINBEGIN%>\n' +
                '住址：<%XZDZ%>\n' +
                '单位及职业：<%WORKIN%> <%PROFESSION%>\n' +
                '监视居住原因：<%BRIEFREASON%>\n' +
                '监视居住地点：<%ZXDDMC%>\n' +
                '指定居所：<%BANK%>\n' +
                '起算时间：<%ZDDDSJ%>\n' +
                '执行机关：<%ASSISTANTUNITINFACT%>\n' +
                '批准人：<%CONFIRMBYPERSON%>\n' +
                '批准时间：<%CONFIRMTIME%>\n' +
                '办案人：<%TRANSACTPRIMARYTOUT%>\n' +
                '办案单位：<%TRANSACTUNIT1%>\n' +
                '填发时间：<%WRITETIME%>\n' +
                '填发人：<%WRITEBY%>\n',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '10px 20px',
                lineHeight: 1.8,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJBH',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WORKIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'PROFESSION',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {},
                  formRule: [
                    {
                      max: 100,
                      message: '该字段最大长度为100'
                    }
                  ]
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'CONFIRMBYPERSON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'CONFIRMTIME',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'TRANSACTPRIMARYTOUT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'TRANSACTUNIT1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WRITETIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WRITEBY',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuid4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '监视居住决定书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'staticText',
              textValue: '(副本)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '163px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    犯罪嫌疑人<%ZWXM%>' +
                '，性别<%SEX%>' +
                '，出生日期<%BIRTHDAYINBEGIN%>' +
                '，住址<%XZDZ%>' +
                '。\n    我局正在侦查<%AJMC%>' +
                '，因<%BRIEFREASON%>' +
                '，根据《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                '条之规定，决定在<%ZXDDMC%>' +
                '对犯罪嫌疑人<%BANK1%>' +
                '，由<%ASSISTANTUNITINFACT%>' +
                '负责执行，监视居住期限从<%ZDDDSJ%>' +
                '起算。\n    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                '     一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                '     二、未经执行机关批准不得会见他人或者通信；\n' +
                '     三、在传讯的时候及时到案；\n' +
                '     四、不得以任何形式干扰证人作证；\n' +
                '     五、不得毁灭、伪造证据或者串供；\n' +
                '     六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                '     如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.3,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {},
                  formRule: [
                    {
                      max: 100,
                      message: '该字段最大长度为100'
                    }
                  ]
                },
                {
                  key: uuid4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ3',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuid4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                right: '50px',
                marginTop: '-20px'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '本决定书已收到。\n    被监视居住人：\n           年   月   日',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 1.5,
                marginTop: '30px',
                left: 0,
                fontSize: '16pt'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                lineHeight: 1.5,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuid4(),
      footText: '此联交被监视居住人',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '监视居住决定书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    犯罪嫌疑人<%ZWXM%>' +
                '，性别<%SEX%>' +
                '，出生日期<%BIRTHDAYINBEGIN%>' +
                '，住址<%XZDZ%>' +
                '。\n    我局正在侦查<%AJMC%>' +
                '，因<%BRIEFREASON%>' +
                '，根据《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                '条之规定，决定在<%ZXDDMC%>' +
                '对犯罪嫌疑人<%BANK1%>' +
                '，由<%ASSISTANTUNITINFACT%>' +
                '负责执行，监视居住期限从<%ZDDDSJ%>' +
                '起算。\n    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                '     一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                '     二、未经执行机关批准不得会见他人或者通信；\n' +
                '     三、在传讯的时候及时到案；\n' +
                '     四、不得以任何形式干扰证人作证；\n' +
                '     五、不得毁灭、伪造证据或者串供；\n' +
                '     六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                '     如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.5,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {},
                  formRule: [
                    {
                      max: 100,
                      message: '该字段最大长度为100'
                    }
                  ]
                },
                {
                  key: uuid4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuid4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                right: '50px',
                marginTop: '-20px'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt',
                marginTop: '20px'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuid4(),
      footText: '此联交执行机关',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '监视居住执行通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%ASSISTANTUNITINFACT%>:\n    ' +
                '因<%BRIEFREASON%>' +
                '我局决定在<%ZXDDMC%>' +
                '对涉嫌<%ZXDXXX%>' +
                '罪的犯罪嫌疑人<%ZWXM%>' +
                '（<%SEX%>' +
                '，<%BIRTHDAYINBEGIN%>' +
                '，住址：<%XZDZ%>' +
                '）<%BANK1%>' +
                '，交由你单位执行，监视居住期限从<%ZDDDSJ%>起算。\n' +
                '    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                '    一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                '    二、未经执行机关批准不得会见他人或者通信；\n' +
                '    三、在传讯的时候及时到案；\n' +
                '    四、不得以任何形式干扰证人作证；\n' +
                '    五、不得毁灭、伪造证据或者串供；\n' +
                '    六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                '    如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。\n' +
                '    属于律师会见需经许可的案件：<%SPECIALDEGREE%>',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.5,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {},
                  formRule: [
                    {
                      max: 100,
                      message: '该字段最大长度为100'
                    }
                  ]
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZXDXXX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ3',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SPECIALDEGREE',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'static',
                    kind: '00',
                    params: {
                      // 这里使用到 动态字典的时候才
                      // 用到
                      configId: ''
                    }
                  },
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuid4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                right: '50px',
                marginTop: '-10px'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    }
  ] // 文书联数
};

export const getChangePageDataConfig = (formData, overflowH) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuid4(),
        footText: '',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '$F{GAFJ}',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                },
                fields: [
                  [
                    {
                      key: uuid4(),
                      textName: 'GAFJ',
                      textValue: '',
                      textType: 'string',
                      elementStyle: {}
                    }
                  ]
                ]
              },
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '监视居住',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  marginLeft: '-50px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  marginLeft: '95px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '决 定',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '84px',
                  marginLeft: '45px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '12pt'
                }
              },
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '执行通知',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '106px',
                  marginLeft: '45px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '12pt'
                }
              },
              {
                key: uuid4(),
                type: 'staticText',
                textValue: '(存根)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '$F{WSZH}',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '163px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                },
                fields: [
                  [
                    {
                      key: uuid4(),
                      textName: 'WSZH',
                      textValue: '',
                      textType: 'string',
                      elementStyle: {}
                    }
                  ]
                ]
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '案件名称：<%AJMC%>\n' +
                  '案件编号：<%AJBH%>\n' +
                  '被监视居住人：<%ZWXM%>  <%SEX%>\n' +
                  '出生日期：<%BIRTHDAYINBEGIN%>\n' +
                  '住址：<%XZDZ%>\n' +
                  '单位及职业：<%WORKIN%> <%PROFESSION%>\n' +
                  '监视居住原因：<%BRIEFREASON%>\n' +
                  '监视居住地点：<%ZXDDMC%>\n' +
                  '指定居所：<%BANK%>\n' +
                  '起算时间：<%ZDDDSJ%>\n' +
                  '执行机关：<%ASSISTANTUNITINFACT%>\n' +
                  '批准人：<%CONFIRMBYPERSON%>\n' +
                  '批准时间：<%CONFIRMTIME%>\n' +
                  '办案人：<%TRANSACTPRIMARYTOUT%>\n' +
                  '办案单位：<%TRANSACTUNIT1%>\n' +
                  '填发时间：<%WRITETIME%>\n' +
                  '填发人：<%WRITEBY%>\n',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '10px 20px',
                  lineHeight: 1.8,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AJBH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'WORKIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'PROFESSION',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {},
                    formRule: [
                      {
                        max: 100,
                        message: '该字段最大长度为100'
                      }
                    ]
                  },
                  {
                    key: uuid4(),
                    textName: 'ZXDDMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'CONFIRMBYPERSON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'CONFIRMTIME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'TRANSACTUNIT1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'WRITEBY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '监视居住决定书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'staticText',
                textValue: '(副本)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '163px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { display: 'inherit' },
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    犯罪嫌疑人<%ZWXM%>' +
                  '，性别<%SEX%>' +
                  '，出生日期<%BIRTHDAYINBEGIN%>' +
                  '，住址<%XZDZ%>' +
                  '。\n    我局正在侦查<%AJMC%>' +
                  '，因<%BRIEFREASON%>' +
                  '，根据《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                  '条之规定，决定在<%ZXDDMC%>' +
                  '对犯罪嫌疑人<%BANK1%>' +
                  '，由<%ASSISTANTUNITINFACT%>' +
                  '负责执行，监视居住期限从<%ZDDDSJ%>' +
                  '日起算。\n    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                  '     一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                  '     二、未经执行机关批准不得会见他人或者通信；\n' +
                  '     三、在传讯的时候及时到案；\n' +
                  '     四、不得以任何形式干扰证人作证；\n' +
                  '     五、不得毁灭、伪造证据或者串供；\n' +
                  '     六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                  '     如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 1.3,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {},
                    formRule: [
                      {
                        max: 100,
                        message: '该字段最大长度为100'
                      }
                    ]
                  },
                  {
                    key: uuid4(),
                    textName: 'ITEMOFLAW',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZXDDMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ3',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联附卷',
        paragraph: [
          {
            key: uuid4(),
            editable: true,
            style: { marginTop: `${20 + overflowH}px`,breakInside: "avoid"},
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  right: '50px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '本决定书已收到。\n    被监视居住人：\n           年   月   日',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  paddingLeft: '22px',
                  lineHeight: 2,
                  marginTop: '30px',
                  left: 0,
                  fontSize: '16pt'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  lineHeight: 2,
                  left: 0,
                  top: '30px',
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '监视居住决定书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { display: 'inherit' },
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    犯罪嫌疑人<%ZWXM%>' +
                  '，性别<%SEX%>' +
                  '，出生日期<%BIRTHDAYINBEGIN%>' +
                  '，住址<%XZDZ%>' +
                  '。\n    我局正在侦查<%AJMC%>' +
                  '，因<%BRIEFREASON%>' +
                  '，根据《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                  '条之规定，决定在<%ZXDDMC%>' +
                  '对犯罪嫌疑人<%BANK1%>' +
                  '，由<%ASSISTANTUNITINFACT%>' +
                  '负责执行，监视居住期限从<%ZDDDSJ%>' +
                  '起算。\n    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                  '     一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                  '     二、未经执行机关批准不得会见他人或者通信；\n' +
                  '     三、在传讯的时候及时到案；\n' +
                  '     四、不得以任何形式干扰证人作证；\n' +
                  '     五、不得毁灭、伪造证据或者串供；\n' +
                  '     六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                  '     如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 1.5,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {},
                    formRule: [
                      {
                        max: 100,
                        message: '该字段最大长度为100'
                      }
                    ]
                  },
                  {
                    key: uuid4(),
                    textName: 'ITEMOFLAW',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZXDDMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联交被监视居住人',
        paragraph: [
          {
            key: uuid4(),
            editable: true,
            style: { marginTop: `${20 + overflowH}px`,breakInside: "avoid"},
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  right: '50px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  lineHeight: 2,
                  top: '40px',
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '监视居住执行通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { display: 'inherit' },
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%ASSISTANTUNITINFACT%>:\n    ' +
                  '因<%BRIEFREASON%>' +
                  '我局决定在<%ZXDDMC%>' +
                  '对涉嫌<%ZXDXXX%>' +
                  '罪的犯罪嫌疑人<%ZWXM%>' +
                  '（<%SEX%>' +
                  '，<%BIRTHDAYINBEGIN%>' +
                  '，住址：<%XZDZ%>' +
                  '）<%BANK1%>' +
                  '，交由你单位执行，监视居住期限从<%ZDDDSJ%>起算。\n' +
                  '    在监视居住期间，被监视居住人应当遵守下列规定：\n' +
                  '    一、未经执行机关批准不得离开执行监视居住的处所；\n' +
                  '    二、未经执行机关批准不得会见他人或者通信；\n' +
                  '    三、在传讯的时候及时到案；\n' +
                  '    四、不得以任何形式干扰证人作证；\n' +
                  '    五、不得毁灭、伪造证据或者串供；\n' +
                  '    六、将护照等出入境证件、身份证件、驾驶证件交执行机关保存。\n' +
                  '    如果被监视居住人违反以上规定，情节严重的，可以予以逮捕；需要予以逮捕的，可以先行拘留。\n' +
                  '    属于律师会见需经许可的案件：<%SPECIALDEGREE%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 1.5,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {},
                    formRule: [
                      {
                        max: 100,
                        message: '该字段最大长度为100'
                      }
                    ]
                  },
                  {
                    key: uuid4(),
                    textName: 'ZXDDMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZXDXXX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZDDDSJ3',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SPECIALDEGREE',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      // 字典
                      type: 'static',
                      kind: '00',
                      params: {
                        // 这里使用到 动态字典的时候才
                        // 用到
                        configId: ''
                      }
                    },
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联交执行机关',
        paragraph: [
          {
            key: uuid4(),
            editable: true,
            style: { marginTop: `${20 + overflowH}px`,breakInside: "avoid"},
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  right: '50px',
                  marginTop: '-10px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  lineHeight: 2,
                  left: 0,
                  top: '30px',
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
