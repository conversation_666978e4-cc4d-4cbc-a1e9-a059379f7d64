import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', //filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '此联存根',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '立 案 决 hah',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '\n' +
                '<%ASSISTANTUNIT%>"："\n' +
                '"\\n    根据《中华人民共和国刑事诉讼法》"<%SUBMITTIME%>\n' +
                '"之规定，请予"($F{WSCODE}.equals("R031")?"冻结"\n' +
                '"犯罪嫌疑人"<%ZWXM%>\n' +
                '"（"<%SEX%>\n' +
                '"，"<%BIRTHDAYINBEGIN%>\n' +
                '"）的下列财产："\n' +
                '"\\n    类型（名称）："<%AJLX%>\n' +
                '"\\n    所在机构："<%ASSISTANTUNIT%>\n' +
                '"\\n    户名或权利人："<%LXFS2%>\n' +
                '"\\n    账号等号码："<%ACCOUNT%>\n' +
                '"\\n    冻结数额（大、小写）："<%MONEY%>($F{MONEY}!=null?"("<%MONEY3%><%RESERVATION09%>")"<%RESERVATION09%>\n' +
                '"\\n    其他："<%NOTE%>\n' +
                '"\\n    冻结时间从"<%BEGINPERFORMTIME1%>\n' +
                '"年"<%BEGINPERFORMTIME2%>\n' +
                '"月"<%BEGINPERFORMTIME3%>\n' +
                '"日起"<%ENDPERFORMTIME1%>\n' +
                '"年"<%ENDPERFORMTIME2%>\n' +
                '"月"<%ENDPERFORMTIME3%>\n' +
                '"日。"',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                { key: 'uuidv4()', textName: 'ASSISTANTUNIT', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'WSCODE', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'SUBMITTIME', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'SEX', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'BIRTHDAYINBEGIN', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'AJLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'ACCOUNT', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'MONEY', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'MONEY3', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'RESERVATION09', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'BEGINPERFORMTIME1', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'BEGINPERFORMTIME2', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'BEGINPERFORMTIME3', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'ENDPERFORMTIME1', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'ENDPERFORMTIME2', textValue: '', textType: 'string', elementStyle: {} },
                { key: 'uuidv4()', textName: 'ENDPERFORMTIME3', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '立 案 决 定 书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '    根据《中华人民共和国刑事诉讼法》第 <%SFZC%> 条之规定，决定对<%AJMC%> 案立案侦查。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'SFZC',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    //字典
                    type: 'static',
                    kind: '02',
                    params: {
                      // 这里使用到 动态字典的时候才用到
                      configId: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   style: { marginTop: '20px', marginLeft: '20px' },
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '案件名称：',
        //       textName: 'ajmc',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '20px',
        //         left: 0,
        //         height: '44px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '案件编号：',
        //       textName: 'ajbh',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '64px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '犯罪嫌疑人：',
        //       textName: 'fzxyr',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '141px',
        //         position: 'absolute',
        //         top: '108px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '出生日期：',
        //       textName: 'csrq',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '124px',
        //         position: 'absolute',
        //         top: '152px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '拘留原因：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '240px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '批准人：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '288px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '批准时间：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '332px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '执行人：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '376px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '办案单位：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '420px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '填发时间：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '464px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'staticText',
        //       textValue: '填发人：',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '120px',
        //         position: 'absolute',
        //         top: '508px',
        //         left: 0,
        //         lineHeight: '44px',
        //         height: '53px',
        //         textAlign: 'left',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '案件编号',
        //       textName: 'ajbh',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '20px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '案件名称',
        //       textName: 'ajmc',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '66px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '犯罪嫌疑人',
        //       textName: 'fzxyr',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '110px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '出日期',
        //       textName: 'csrq',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '154px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '住址',
        //       textName: 'zz',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '198px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '拘留原因',
        //       textName: 'jlyy',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '242px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '批准人',
        //       textName: 'pzr',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '286px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '批准时间',
        //       textName: 'pzsj',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '330px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '执行人',
        //       textName: 'zxr',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '374px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '办案单位',
        //       textName: 'badw',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '374px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '填发时间',
        //       textName: 'tfsj',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '418px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //     {
        //       key: uuidv4(),
        //       type: 'textField',
        //       textType: 'string',
        //       textValue: '填发人',
        //       textName: 'tfr',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '500px',
        //         position: 'absolute',
        //         lineHeight: '44px',
        //         top: '462px',
        //         left: '120px',
        //         height: '44px',
        //         fontSize: '16pt',
        //       },
        //     },
        //   ],
        // },
      ]
    }
  ] //文书联数
};
