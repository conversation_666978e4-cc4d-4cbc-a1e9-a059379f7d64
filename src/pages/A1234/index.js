import React, { Component } from 'react';
// import uuidv4 from 'uuid/v4';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { dataConfig } from './reportConfig/A1234';
import styles from './index.less';

const SSWSNAME = '立案决定书';
const FILENAME = 'A1234';

@connect(({ A1234 }) => A1234)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'A1234/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token: token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport dataConfig={dataConfig} wsData={formData} formConfig={formConfig} showPage={page} {...this.props} />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={dataConfig} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/*<WsReport dataConfig={dataConfig} wsData={formData} />*/}
      </div>
    );
  }
}

export default Index;
