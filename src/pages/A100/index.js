import React, { Component } from 'react';
import { connect } from 'dva';
import { Icon } from 'antd';
import moment from 'moment';
import styles from './index.less';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, wsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const { submitnote, detailreason } = bgsData;
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div className={styles.wsTitle}>受案登记表</div>
        <div className={styles.header}>
          <div className={styles.tt}>{bgsData.transactunit1 || ''}</div>
          <div className={styles.zh}>{bgsData.wszh || ''}</div>
        </div>
        <table className={styles.table}>
          <tbody>
            <tr>
              <td colSpan={3} className={styles.lable} style={{ width: '15%' }}>
                案件来源
              </td>
              <td colSpan={17} style={{ width: '85%' }}>
                <Icon type={detailreason === '01' ? 'check-square' : 'border'} />
                110指令
                <Icon type={detailreason === '02' ? 'check-square' : 'border'} />
                工作中发现
                <Icon type={detailreason === '03' ? 'check-square' : 'border'} />
                报案
                <Icon type={detailreason === '04' ? 'check-square' : 'border'} />
                投案
                <Icon type={detailreason === '05' ? 'check-square' : 'border'} />
                移送
                <Icon type={detailreason === '06' ? 'check-square' : 'border'} />
                扭送
                <Icon type={detailreason === '07' ? 'check-square' : 'border'} />
                其他
              </td>
            </tr>
            <tr>
              <td className={styles.lable} style={{ width: '5%' }} rowSpan={4}>
                报<br />案<br />人
              </td>
              <td colSpan={3} className={styles.lable} style={{ width: '15%' }}>
                姓 名
              </td>
              <td colSpan={4} className={styles.value} style={{ width: '20%' }}>
                {bgsData.xm2 || ''}
              </td>
              <td colSpan={2} className={styles.lable} style={{ width: '10%' }}>
                性 别
              </td>
              <td colSpan={2} className={styles.value} style={{ width: '10%' }}>
                {bgsData.xb2 || ''}
              </td>
              <td colSpan={3} className={styles.lable} style={{ width: '15%' }}>
                出生日期
              </td>
              <td colSpan={5} className={styles.value} style={{ width: '25%' }}>
                {bgsData.csrq2 || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable}>
                证件类型
              </td>
              <td colSpan={8} className={styles.value}>
                {bgsData.cardtype || ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                证件号码
              </td>
              <td colSpan={5} className={styles.value}>
                {bgsData.reservation10 || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable}>
                工作单位
              </td>
              <td colSpan={8} className={styles.value}>
                {bgsData.dw2 || ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                联系电话
              </td>
              <td colSpan={5} className={styles.value}>
                {bgsData.lxfs2 || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable}>
                现住址
              </td>
              <td colSpan={16} className={styles.value}>
                {bgsData.xxzz || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable}>
                移送单位
              </td>
              <td colSpan={4} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.assistantunit || ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                移送人
              </td>
              <td colSpan={3} className={styles.value}>
                {bgsData.performby || ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                联系方式
              </td>
              <td colSpan={4} className={styles.value}>
                {bgsData.badwlxdh || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable}>
                接报民警
              </td>
              <td colSpan={4} className={styles.value}>
                {bgsData.receiveby || ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                接报时间
              </td>
              <td colSpan={3} className={styles.value} style={{ fontSize: '12pt' }}>
                {bgsData.receivetime ? moment(bgsData.receivetime).format('YYYY年MM月DD日hh时mm分') : ''}
              </td>
              <td colSpan={3} className={styles.lable}>
                接报地点
              </td>
              <td colSpan={4} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.zxddxz || ''}
              </td>
            </tr>
            <tr>
              <td
                colSpan={20}
                className={styles.value}
                style={{ height: '180px', verticalAlign: 'top', borderTop: 0, textIndent: '2em' }}
              >
                简要案情：{bgsData.analysiscase || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} className={styles.lable} rowSpan={2}>
                受案意见
              </td>
              <td colSpan={17} style={{ padding: '8px', borderBottom: 0 }}>
                <div>
                  <Icon type={submitnote === '01' ? 'check-square' : 'border'} style={{ fontSize: '20px' }} />
                  属本单位管辖的行政案件，建议及时调查处理
                </div>
                <div>
                  <Icon type={submitnote === '02' ? 'check-square' : 'border'} style={{ fontSize: '20px' }} />
                  属本单位管辖的刑事案件，建议及时立案侦查
                </div>
                <div>
                  <div style={{ display: 'inline-block' }}>
                    <Icon
                      type={submitnote === '03' ? 'check-square' : 'border'}
                      style={{ marginRight: '8px', fontSize: '20px' }}
                    />
                    不属于本单位管辖，建议移送
                    {submitnote === '03' && (
                      <div style={{ width: '180px', fontSize: '10pt' }} className={styles.input}>
                        {bgsData.dwmc || ''}
                      </div>
                    )}
                  </div>
                  {submitnote !== '03' && (
                    <div style={{ width: '180px', borderBottom: '1px solid', display: 'inline-block' }} />
                  )}
                  处理
                </div>
                <div>
                  <Icon type={submitnote === '04' ? 'check-square' : 'border'} style={{ fontSize: '20px' }} />
                  不属于公安机关职责范围，不予调查处理并当场书面告知当事人
                </div>
                <div>
                  <div style={{ display: 'inline-block' }}>
                    <Icon
                      type={submitnote === '05' ? 'check-square' : 'border'}
                      style={{ marginRight: '8px', fontSize: '20px' }}
                    />
                    其他
                    {submitnote === '05' && (
                      <div style={{ width: '430px' }} className={styles.input}>
                        {bgsData.oldbriefreason || ''}
                      </div>
                    )}
                  </div>
                  {submitnote !== '05' && (
                    <div style={{ width: '430px', borderBottom: '1px solid #000', display: 'inline-block' }} />
                  )}
                </div>
              </td>
            </tr>
            <tr>
              <td colSpan={4} className={styles.lable} style={{ border: 0 }}>
                受案民警：
              </td>
              <td colSpan={8} className={styles.value} style={{ border: 0 }}>
                {bgsData.frdb || ''}
              </td>
              <td colSpan={5} className={styles.value} style={{ border: 0 }}>
                {bgsData.zdddsj ? moment(bgsData.zdddsj).format('YYYY年MM月DD日') : ''}
              </td>
            </tr>
            <tr>
              <td colSpan={3} rowSpan={2} className={styles.lable}>
                受案审批
              </td>
              <td
                colSpan={17}
                className={styles.value}
                style={{ height: '60px', verticalAlign: 'top', borderBottom: 0 }}
              >
                {bgsData.itemoflaw || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={6} className={styles.lable} style={{ border: 0 }}>
                受案部门负责人：
              </td>
              <td colSpan={11} style={{ border: 0 }} />
            </tr>
          </tbody>
        </table>
        <div className={styles.footer}>一式两份，一份留存，一份附卷。</div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
