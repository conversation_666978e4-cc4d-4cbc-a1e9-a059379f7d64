import uuidv4 from 'uuid/v4';

// export const dataConfig = {
//   wsmc: '',
//   fileName: '',
//   wsCode: '',
//   wsType: 'narrative', // filed 填充式 ，narrative 叙述式
//   borderLines: 2,
//   borderWidth: '',
//   pages: [
//     {
//       key: uuidv4(),
//       footText: '本决定一式若干份，决定机关留存一份，其余分送被指定的公安机关和其他有关的公安机关',
//       paragraph: [
//         {
//           key: uuidv4(),
//           editable: false,
//           style: {},
//           contents: [
//             {
//               key: uuidv4(),
//               type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
//               textValue: '指定管辖决定书',
//               textName: 'wsmc',
//               elementStyle: {
//                 fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
//                 width: '100%',
//                 position: 'absolute',
//                 top: '88px',
//                 left: 0,
//                 height: '53px',
//                 textAlign: 'center',
//                 fontSize: '22pt'
//               }
//             },
//             {
//               key: uuidv4(),
//               type: 'textField',
//               textType: 'string',
//               textValue: '广西南宁市公安局兴宁分局',
//               textName: 'gafj',
//               elementStyle: {
//                 fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
//                 width: '100%',
//                 position: 'absolute',
//                 top: '40px',
//                 left: 0,
//                 height: '48px',
//                 textAlign: 'center',
//                 fontSize: '16pt',
//                 lineHeight: '48px'
//               }
//             },
//             {
//               key: uuidv4(),
//               type: 'textField',
//               textType: 'string',
//               textValue: '文书字号',
//               textName: 'wszh',
//               elementStyle: {
//                 fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
//                 width: '100%',
//                 position: 'absolute',
//                 top: '143px',
//                 left: 0,
//                 height: '44px',
//                 textAlign: 'right',
//                 fontSize: '16pt',
//                 lineHeight: '44px',
//                 paddingRight: '22px'
//               }
//             }
//           ]
//         },
//         {
//           key: uuidv4(),
//           editable: true,
//           contents: [
//             {
//               key: uuidv4(),
//               type: 'bigText', // 大文本段落
//               tempStr:
//                 '    经对<%AJMC%>案件的管辖问题进行审查，根据《公安机关办理刑事案件程序规定》第二十二条' +
//                 '之规定，决定由<%RESERVATION09%>管辖。请<%ASSISTANTUNITINFACT%>在<%DELAYLENGTHOFTERM%>日' +
//                 '内将与案件有关的证据材料移送该公安机关。',
//
//               elementStyle: {
//                 fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
//                 width: '100%',
//                 position: 'absolute',
//                 textAlign: 'left',
//                 padding: '20px',
//                 lineHeight: 2,
//                 left: 0,
//                 fontSize: '16pt'
//               },
//               fields: [
//                 {
//                   key: uuidv4(),
//                   textName: 'AJMC',
//                   textValue: '',
//                   textType: 'string',
//                   elementStyle: {}
//                 },
//                 {
//                   key: uuidv4(),
//                   textName: 'RESERVATION09',
//                   textValue: '',
//                   textType: 'string',
//                   elementStyle: {}
//                 },
//                 {
//                   key: uuidv4(),
//                   textName: 'ASSISTANTUNITINFACT',
//                   textValue: '',
//                   textType: 'string',
//                   elementStyle: {}
//                 },
//                 {
//                   key: uuidv4(),
//                   textName: 'DELAYLENGTHOFTERM',
//                   textValue: '',
//                   textType: 'string',
//                   elementStyle: {}
//                 }
//               ]
//             }
//           ]
//         },
//         {
//           key: uuidv4(),
//           editable: true,
//           style: {breakInside: "avoid"},
//           contents: [
//             {
//               key: uuidv4(),
//               type: 'img',
//               textType: 'img',
//               textValue: '印章',
//               textName: 'spdwyz',
//               elementStyle: {
//                 fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
//                 position: 'absolute',
//                 marginTop: '-20px',
//                 right: '50px'
//               }
//             },
//             {
//               key: uuidv4(),
//               type: 'bigText', // 大文本段落
//               tempStr: '<%gafj_1%>\n<%WRITETIME%>',
//               elementStyle: {
//                 fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
//                 width: '100%',
//                 position: 'absolute',
//                 textAlign: 'right',
//                 padding: '20px',
//                 lineHeight: 2,
//                 left: 0,
//                 fontSize: '16pt'
//               },
//               fields: [
//                 {
//                   key: uuidv4(),
//                   textName: 'gafj_1',
//                   textValue: '',
//                   textType: 'string',
//                   elementStyle: {}
//                 },
//                 {
//                   key: uuidv4(),
//                   textName: 'WRITETIME',
//                   textValue: '',
//                   textType: 'date',
//                   elementStyle: {}
//                 }
//               ]
//             }
//           ]
//         }
//       ]
//     }
//   ] // 文书联数
// };

export const getDataConfig = (formData,type) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '本决定一式若干份，决定机关留存一份，其余分送被指定的公安机关和其他有关的公安机关',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '指定管辖决定书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    经对<%AJMC%>案件的管辖问题进行审查，根据《公安机关办理刑事案件程序规定》第二十二条' +
                  '之规定，决定由<%RESERVATION09%>管辖。请<%ASSISTANTUNITINFACT%>在<%DELAYLENGTHOFTERM%>日' +
                  '内将与案件有关的证据材料移送该公安机关。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DELAYLENGTHOFTERM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '-20px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n' + `${ type === "print" ? '<%writetimenyr%>' : '<%WRITETIME%>'}`,
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      }
    ] // 文书联数
  }
}
