.normal {
}

.pageContainer {
  font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 华文仿宋, 仿宋, serif;
  font-size: 16pt;
  width: 664px;
  height: 993px;
  page-break-after: always;
  page-break-before: always;
  margin: 0 auto;
}
.wsContext {
  position: relative;
  width: 100%;
  float: right;
}
.borderComponent {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
}
.doubleBorderComponent {
  height: 953px;
  border: 3px solid #000;
  .minBorder {
    margin: 5px;
    height: 938px;
    border: 1px solid #000;
  }
}
.singleBorderComponent {
  height: 993px;
  border: 2px solid #000;
  display: none;
}

.footText {
  height: 40px;
  line-height: 40px;
  font-size: 12pt;
}

.wsBodyContent {
  padding: 8px;
  position: absolute;
  z-index: 100;
  width: 100%;
  height: 100%;
}
