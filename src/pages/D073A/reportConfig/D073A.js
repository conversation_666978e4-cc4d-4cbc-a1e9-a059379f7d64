import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '撤销案件决定书(省级)',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                ' 案件名称:  <%AJMC%>\n' +
                ' 案件编号:  <%AJBH%>\n' +
                ' 原案件犯罪嫌疑人：  <%ZWXM%>  <%SEX%>\n' +
                ' 出生日期:  <%BIRTHDAYINBEGIN%>\n' +
                ' 住    址:  <%XZDZ%>\n' +
                ' 单位及职业:  <%WORKIN%>  <%PROFESSION%>\n' +
                ' 撤销案件原因：  <%CXYY%>\n' +
                ' 批 准 人:  <%CONFIRMBYPERSON%>\n' +
                ' 批准时间:  <%CONFIRMTIME%>\n' +
                ' 办 案 人:  <%TRANSACTPRIMARYTOUT%>\n' +
                ' 办案单位:  <%TRANSACTUNIT1%>\n' +
                ' 填发日期:  <%WRITETIME%>\n' +
                ' 填 发 人:  <%WRITEBY%>',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'AJBH',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WORKIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'PROFESSION',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CXYY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: 'JZ_07'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYPERSON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMTIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTPRIMARYTOUT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTUNIT1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITETIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITEBY',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '撤销案件决定书(省级)',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(副本)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    我局办理的<%AJMC%>案，因<%CXYY%>，根据《中华人民共和国刑事诉讼法》第<%TIAO%>条之规定，决定撤销此案。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CXYY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: 'JZ_07'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-25px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '本决定书已收到\n原案件犯罪嫌疑人：\n        年  月  日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                top: '70px',
                left: 0,
                fontSize: '16pt'
              },
              fields: []
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '本决定书已收到\n    原案件被害人：\n        年  月  日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                marginLeft: '210px',
                marginTop: '-199px',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: []
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '本决定书已收到\n    移送机关：\n        年  月  日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                marginLeft: '410px',
                marginTop: '-231px',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: []
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交原案件犯罪嫌疑人（原案件嫌疑人死亡的交其家属）',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '撤销案件决定书(省级)',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    我局办理的<%AJMC1%>案，因<%CXYY%>，根据《中华人民共和国刑事诉讼法》第<%TIAO%>条之规定，决定撤销此案。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CXYY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: 'JZ_07'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-25px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交原案件被害人或者其近亲属、法定代理人',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '撤销案件决定书(省级)',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    我局办理的<%AJMC1%>案，因<%CXYY%>，根据《中华人民共和国刑事诉讼法》第<%TIAO%>条之规定，决定撤销此案。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CXYY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: 'JZ_07'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-25px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交移送机关',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '撤销案件决定书(省级)',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    我局办理的<%AJMC1%>案，因<%CXYY%>，根据《中华人民共和国刑事诉讼法》第<%TIAO%>条之规定，决定撤销此案。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CXYY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: 'JZ_07'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-25px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
