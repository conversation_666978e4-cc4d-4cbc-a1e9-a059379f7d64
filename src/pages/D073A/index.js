import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { dataConfig } from './reportConfig/D073A';
import styles from './index.less';

const SSWSNAME = '撤销案件决定书';
const FILENAME = 'D073A';

@connect(({ D073A }) => D073A)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'D073A/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={dataConfig}
          wsData={formData}
          formConfig={formConfig}
          showPage={page}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = <WsPrintReport dataConfig={dataConfig} wsData={formData} sswsname={SSWSNAME} {...this.props} />;
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
