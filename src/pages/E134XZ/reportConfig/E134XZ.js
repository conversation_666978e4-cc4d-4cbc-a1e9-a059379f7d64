import uuidv4 from 'uuid/v4';

export const getDataConfig = (formData) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '鉴定聘请书(行政)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'staticText',
                textValue: '(存根)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  ' 案件名称:  <%AJMC%>\n' +
                  ' 案件编号:  <%AJBH%>\n' +
                  ' 犯罪嫌疑人:  <%ZWXM%>\n' +
                  ' 出生日期:  <%BIRTHDAYINBEGIN%>\n' +
                  ' 鉴定内容:  <%LAWNAME%>\n' +
                  ' 被聘请人:  <%RESERVATION09%>\n' +
                  ' 单位及职务:  <%DW2%>\n' +
                  ' 鉴定意见提交时间:  <%ZDDDSJ%>\n' +
                  ' 批 准 人:  <%CONFIRMBYPERSON%>\n' +
                  ' 批准时间:  <%CONFIRMTIME%>\n' +
                  ' 办 案 人:  <%TRANSACTPRIMARYTOUT%>\n' +
                  ' 办案单位:  <%TRANSACTUNIT%>\n' +
                  ' 填发时间:  <%WRITETIME%>\n' +
                  ' 填 发 人:  <%WRITEBY%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'AJBH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DW2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMBYPERSON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMTIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTUNIT',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'static',
                      kind: '06'
                    },
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITEBY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuidv4(),
        footText: '此联附卷',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '鉴定聘请书(行政)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%RESERVATION09%>：\n' +
                  `    为了查明<%AJMC%>一案，根据《公安机关办理行政案件程序规定》${
                    new Date('2019/1/1').getTime() > new Date(formData.submittime).getTime()
                      ? '第七十二条'
                      : '第八十七条'
                  }之规定，` +
                  '特聘请你（单位）对<%LAWNAME%>进行鉴定。请于 <%ZDDDSJ%>前将鉴定情况' +
                  '和意见书面送交我（分）局。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'SUBMITTIME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '-20px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '本聘请书已收到。\n        接收人：\n               年    月    日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: []
              }
            ]
          }
        ]
      },
      {
        key: uuidv4(),
        footText: '此联交被聘请人',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '鉴定聘请书(行政)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%RESERVATION09%>：\n' +
                  `    为了查明<%AJMC%>一案，根据《公安机关办理行政案件程序规定》${
                    new Date('2019/1/1').getTime() > new Date(formData.submittime).getTime()
                      ? '第七十二条'
                      : '第八十七条'
                  }之规定，` +
                  '特聘请你（单位）对<%LAWNAME%>进行鉴定。请于<%ZDDDSJ%>前将鉴' +
                  '定情况和意见书面送交我（分）局。',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'SUBMITTIME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '-20px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '本聘请书已收到。\n        接收人：\n               年    月    日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: []
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
