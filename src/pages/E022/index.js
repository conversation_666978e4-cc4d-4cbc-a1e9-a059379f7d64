import React, { Component } from 'react';
// import {Form} from 'antd';
import { connect } from 'dva';
import { config } from '@/common/config';
import Toolbar from '../../compoments/wenshu/Toolbar';
import styles from './index.less';

const SSWSNAME = '会见犯罪嫌疑人申请表';
const FILENAME = 'E022';

// @Form.create()
@connect(({ wtstjd }) => wtstjd)
class Index extends Component {
  state = {
    scale: 100
  };

  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'wtstjd/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  handlePrintReport = () => {
    const {
      fileName,
      location: { query }
    } = this.props;

    const { wskey, token, type } = query;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    window.open(
      `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
      '_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
      // `http://192.168.1.104:3000/print?url=http://192.168.1.10:8002/A002&type=print&wskey=PCS37201302260000000000184156&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODE3MjUzMX0.axgbBRqBHFNWwSpLvzKvyWp1Y5sTQPR6LwTngnPMQXo`,
    );
  };
  // handlePrintReport = () => {
  //   const {
  //     fileName,
  //     location: { query }
  //   } = this.props;
  //
  //   const { wskey, token, type } = query;
  //   const { wsreportPrintContextPath, wsreportContextPath } = config;
  //   window.open(
  //     `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
  //     '_blank',
  //     'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
  //   );
  // };
  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  render() {
    // const {getFieldDecorator} = this.props.form;
    const { formData, location } = this.props;
    const {
      query: { type }
    } = location;
    return (
      <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
        <Toolbar
          style={{ display: type === 'edit' ? 'inherit' : 'none' }}
          onSliderChange={this.onSliderChange}
          handleScaleReport={this.handleScaleReport}
          handlePrintReport={this.handlePrintReport}
          handlePreviewReport={this.handlePreviewReport}
          scale={this.state.scale}
          {...this.props}
        />
        <div className={styles.wsTitle}>
          <span>会见犯罪嫌疑人申请表</span>
        </div>
        <table className={styles.table}>
          <tbody>
            <tr className={styles.normalTr}>
              <td>申请人</td>
              <td>{formData.xm2}</td>
              <td>性别</td>
              <td>{formData.xb2}</td>
              <td>出生日期</td>
              <td>{formData.csrq2}</td>
            </tr>
            <tr className={styles.normalTr}>
              <td>单 位</td>
              <td colSpan={2}>{formData.dw2}</td>
              <td>
                律师执业
                <br />
                证编号
              </td>
              <td colSpan={2}>{formData.lxfs2}</td>
            </tr>
            <tr className={styles.normalTr}>
              <td>犯罪嫌疑人</td>
              <td>{formData.zwxm}</td>
              <td>性别</td>
              <td>{formData.sex}</td>
              <td>出生日期</td>
              <td>{formData.birthdayinbegin}</td>
            </tr>
            <tr className={styles.normalTr}>
              <td>涉嫌罪名</td>
              <td colSpan={2}>{formData.briefreason}</td>
              <td>拘留/逮捕/监视居住时间</td>
              <td colSpan={2}>{formData.zdddsj}</td>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr>
              <td>
                <div style={{ height: '130px', margin: '20px' }}>
                  &nbsp;&nbsp;我受{formData.wtr}委托，为犯罪嫌疑人提供辩护。根据《中华人民共和国刑事诉讼法》
                  第三十七条第{formData.itemoflaw}款之规定，特申请会见犯罪嫌疑人。
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr className={styles.normalTr}>
              <td style={{ width: '110px', height: '80px' }}>申请人签名</td>
              <td>
                <span />
                <span style={{ marginLeft: '120px' }}>{formData.writetime}</span>
              </td>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr>
              <td style={{ width: '110px', textAlign: 'center' }}>
                侦<br />查<br />机<br />关<br />意<br />见
              </td>
              <td style={{ padding: '0' }}>
                <tr style={{ height: '100px', borderBottom: '1px solid #000' }}>
                  办案人意见：
                  <div style={{ width: '553px', paddingLeft: '20px' }}>{formData.submitnote}</div>
                  <div style={{ paddingLeft: '450px' }}>{formData.submittime}</div>
                </tr>
                <tr style={{ height: '120px', borderBottom: '1px solid #000' }}>
                  办案单位意见：
                  <div style={{ width: '553px', paddingLeft: '20px' }}>{formData.ldps2}</div>
                  <div style={{ paddingLeft: '450px' }}>{formData.ldps2}</div>
                </tr>
                <tr style={{ height: '130px' }}>
                  领导批示
                  <div style={{ width: '553px', paddingLeft: '20px' }}>{formData.ldps1}</div>
                  <div style={{ paddingLeft: '450px' }}>{formData.ldps1}</div>
                </tr>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }
}

export default Index;
