import React, { Component } from 'react';
import { connect } from 'dva';
import { countFontSize, digitUppercase, drawLine } from '@/pages/list/utils/func';
import commonStyles from '../lesses/index.less';
import styles from './index.less';

class Index extends Component {
  constructor() {
    super();
    this.state = {
      rowNum: 8
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'list/queryList',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    })
      .then(() => {
        const { rowNum } = this.state;
        const { list } = this.props;
        const { wpclqdxmList = [] } = list.listData;
        const y = (rowNum - wpclqdxmList.length) * 50;
        return y;
      })
      .then((y) => {
        drawLine(0, 0, 664, y, 'lineCanvas');
        drawLine(0, 0, 99, 50, 'wpgg');
        drawLine(0, 0, 165, 50, 'wptz');
      });
  }
  buildBlankBox = () => {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [] } = listData;
    const blankBox = [];
    if (rowNum > wpclqdxmList.length){
      for (let i = 0; i < rowNum - wpclqdxmList.length; i++) {
        blankBox.push(
          <tr>
            <td />
            <td />
            <td />
            <td />
            <td />
            <td />
          </tr>
        );
      }
    }
    return blankBox;
  };
  render() {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [] } = listData;
    const canvasHeight = (rowNum - wpclqdxmList.length) * 50;
    const xzzDh = `${listData.xzdz || ''}，联系电话：${listData.lxfs || ''}`;
    const xzzDhInputFontSize = countFontSize(xzzDh, 896, 25, 19, 'px');
    const number = parseInt(275 / xzzDhInputFontSize.substring(0, xzzDhInputFontSize.length - 2));
    const fristXzzDh = xzzDh.substring(0, number);
    const secondXzzdh = xzzDh.length > number ? xzzDh.substring(number, xzzDh.length) : '';
    const listView = listData.systemid ? (
      <div className={commonStyles.normal}>
        <table className={commonStyles.table}>
          <tbody>
            <tr>
              <td colSpan={6}>
                <div className={commonStyles.titleDept}>{listData.gamc}</div>
                <div className={commonStyles.wsTitle}>登记物品清单</div>
                <div style={{ textAlign: 'left', padding: '0 5px', lineHeight: '35px' }}>
                  <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                  <span>物品持有人</span>
                  <span
                    className={styles.input}
                    style={{ width: '150px', fontSize: countFontSize(listData.dxmc, 150, 25, 19, 'px') }}
                  >
                    {listData.dxmc || <span>&nbsp;</span>}
                  </span>
                  <span>（性别</span>
                  <span
                    className={styles.input}
                    style={{ width: '80px', fontSize: countFontSize(listData.xb, 80, 25, 19, 'px') }}
                  >
                    {listData.xb || <span>&nbsp;</span>}
                  </span>
                  <span>年龄</span>
                  <span
                    className={styles.input}
                    style={{ width: '140px', fontSize: countFontSize(listData.csrq1, 140, 25, 19, 'px') }}
                  >
                    {listData.csrq1 || <span>&nbsp;</span>}
                  </span>
                  <span>单位法定代表人</span>
                  <span
                    className={styles.input}
                    style={{ width: '150px', fontSize: countFontSize(listData.reservation01, 150, 25, 19, 'px') }}
                  >
                    {listData.reservation01 || <span>&nbsp;</span>}
                  </span>
                  <span>现住址及联系方式</span>
                  <span className={styles.input} style={{ width: '275px', fontSize: xzzDhInputFontSize }}>
                    {fristXzzDh || <span>&nbsp;</span>}
                  </span>
                  <span
                    className={styles.input}
                    style={{ width: '640px', fontSize: xzzDhInputFontSize, textAlign: 'left' }}
                  >
                    {secondXzzdh || <span>&nbsp;</span>}
                  </span>
                  <span>)</span>
                  <span>根据《公安机关办理行政案件程序规定》第八十八条第三款之规定，下列物品与</span>
                  <span className={styles.input} style={{ width: '350px' }}>
                    {listData.ajmc || <span>&nbsp;</span>}
                  </span>
                  <span> 案件有关，需要作为证据，现依法予以登记。</span>
                </div>
              </td>
            </tr>
            <tr>
              <th style={{ width: '10%' }}>编号</th>
              <th style={{ width: '25%' }}>名称</th>
              <th style={{ width: '15%' }}>规格</th>
              <th style={{ width: '10%' }}>数量</th>
              <th style={{ width: '25%' }}>特征</th>
              <th style={{ width: '15%' }}>备注</th>
            </tr>
            {wpclqdxmList.map((item, index) => (
              <tr>
                <td>{index + 1}</td>
                <td style={{ fontSize: countFontSize(item.wpmc, 165, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wpmc || ''}
                </td>
                <td
                  style={{
                    fontSize: countFontSize(item.wpgg, 99, 50, 19, 'px'),
                    lineHeight: '1',
                    textAlign: 'left',
                    verticalAlign: 'top'
                  }}
                >
                  {item.wpgg || <canvas className="wpgg" width={99} height={50} style={{ position: 'absolute' }} />}
                </td>
                <td
                  style={{
                    fontSize: countFontSize(digitUppercase(item.wpsl || '', item.jldw || ''), 66, 50, 19, 'px'),
                    lineHeight: '1'
                  }}
                >
                  {digitUppercase(item.wpsl || '', item.jldw || '')}
                </td>
                <td
                  style={{
                    fontSize: countFontSize(item.wptz, 165, 50, 19, 'px'),
                    lineHeight: '1',
                    textAlign: 'left',
                    verticalAlign: 'top'
                  }}
                >
                  {item.wptz || <canvas className="wptz" width={165} height={50} style={{ position: 'absolute' }} />}
                </td>
                <td style={{ fontSize: countFontSize(item.bz, 99, 50, 19, 'px'), lineHeight: '1' }}>{item.bz || ''}</td>
              </tr>
            ))}
            {this.buildBlankBox()}
            <tr>
              <td colSpan={6} className={styles.footerTd}>
                <div className={styles.footerDiv} style={{ borderRight: '0.5px solid #000000' }}>
                  <div style={{ height: '80px' }}>物品持有人</div>
                  <div style={{ marginLeft: '50%' }}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
                <div className={styles.footerDiv}>
                  <div style={{ height: '80px' }}>承办人</div>
                  <div style={{ marginLeft: '50%' }}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
          <canvas
            className="lineCanvas"
            width={664}
            height={canvasHeight}
            style={{ position: 'absolute', bottom: 120 }}
          />
        </table>
        <div className={commonStyles.footer}>附卷</div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ list }) => ({
  list
}))(Index);
