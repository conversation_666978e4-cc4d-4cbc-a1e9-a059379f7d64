import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '提请批准延长侦查羁押期限意见书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: { display: 'inherit' },
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '案件名称： <%AJMC%>\n' +
                '案件编号： <%AJBH%>\n' +
                '犯罪嫌疑人： <%ZWXM%>  <%SEX%>\n' +
                '出生日期： <%BIRTHDAYINBEGIN%>\n' +
                '住址： <%XZDZ%>\n' +
                '单位及职业： <%WORKIN%>  <%PROFESSION%>\n' +
                '逮捕时间： <%CONFIRMTIME%>\n' +
                '延长原因： <%BRIEFREASON%>\n' +
                '提请延长期限： <%DELAYLENGTHOFTERM%><%CELLOFDELAYLENGTH%>\n' +
                '送往单位： <%CONFIRMBYUNIT%>\n' +
                '批准人： <%CONFIRMBYPERSON%>\n' +
                '批准时间： <%RETURNWRITDATE%>\n' +
                '办案人： <%TRANSACTPRIMARYTOUT%>\n' +
                '办案单位： <%TRANSACTUNIT1%>\n' +
                '填发时间： <%WRITETIME%>\n' +
                '填发人： <%WRITEBY%>',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'AJBH',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WORKIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'PROFESSION',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMTIME',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'DELAYLENGTHOFTERM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CELLOFDELAYLENGTH',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'static',
                    kind: '97'
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYUNIT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYPERSON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTPRIMARYTOUT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTUNIT1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITETIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'RETURNWRITDATE',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITEBY',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '提请批准延长侦查羁押期限意见书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(副本)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: { display: 'inherit' },
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%CONFIRMBYUNIT%>：\n' +
                '    你院于<%RETURNWRITDATE%>以<%ZXDDXZ%>' +
                '[<%ZDDDDD%>' +
                ']<%ZXDDMC%>' +
                '号决定书批准逮捕的犯罪嫌疑人<%ZWXM%>' +
                '已于<%ZDDDSJ%>被执行逮捕，因<%DETAILREASON%>' +
                '，羁押期限届满不能侦查终结，根据 《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                '条之规定，特提请批准对其延长羁押期限<%DELAYLENGTHOFTERM%>' +
                '个月。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYUNIT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'RETURNWRITDATE',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZXDDXZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDDD',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'DETAILREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'DELAYLENGTHOFTERM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-25px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: `<%gafj_1%>\n<%writetimenyr%>`,
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                marginTop: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: `本意见书已收到。
      检察院收件人：
          年   月   日`,

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                marginTop: '20px',
                left: 0,
                fontSize: '16pt'
              }
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交检察院',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '提请批准延长侦查羁押期限意见书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: { display: 'inherit' },
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%CONFIRMBYUNIT%>：\n' +
                '    你院于<%RETURNWRITDATE%>以<%ZXDDXZ%>' +
                '[<%ZDDDDD%>' +
                ']<%ZXDDMC%>' +
                '号决定书批准逮捕的犯罪嫌疑人<%ZWXM%>' +
                '已于<%ZDDDSJ%>被执行逮捕，因<%DETAILREASON%>' +
                '，羁押期限届满不能侦查终结，根据 《中华人民共和国刑事诉讼法》第<%ITEMOFLAW%>' +
                '条之规定，特提请批准对其延长羁押期限<%DELAYLENGTHOFTERM%>' +
                '个月。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYUNIT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'RETURNWRITDATE',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZXDDXZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDDD',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ3',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'DETAILREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'DELAYLENGTHOFTERM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-30px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%writetimenyr%>',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                marginTop: '68px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
