import request from '../../../utils/request';
import { config } from '../../../common/config';

const Token =
  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODg1NTgxOX0.EWdNZsy0_PGZbEWzo6AT_1gJeVz_oX8oGZXLUJqRfVs';

const PCSkey = 'PCS37201302260000000000184156';
export function fetchData({ systemid, wsmc, token }) {
  return request(`${config.contextPath}/api/b_asj_ws_news/ssws/${systemid || PCSkey}?sswsname=${wsmc}`, {
    headers: {
      Authorization: token || Token
    }
  });
}

export function updateData({ formData, token }) {
  return request(`${config.contextPath}/api/b_asj_wss/patch`, {
    headers: {
      Authorization: token || Token,
      'Content-Type': 'application/sino-patch'
    },
    body: JSON.stringify(formData),
    method: 'PUT'
  });
}

export function fetchConfig({ filename, token }) {
  return request(`${config.contextPath}/api/ws/b_asj_wschangeareas/filename/${filename}`, {
    headers: {
      Authorization: token || Token
    }
  });
}
