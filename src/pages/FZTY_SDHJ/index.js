import React, { Component } from 'react';
// import {Form} from 'antd';
import { connect } from 'dva';
import Toolbar from '../../compoments/wenshu/Toolbar';
import styles from './index.less';
import { config } from '@/common/config';

const SSWSNAME = '送达回执';
const FILENAME = 'FZTY_SDHJ';

// @Form.create()
@connect(({ FZTY_SDHJ }) => FZTY_SDHJ)
class Index extends Component {
  state = {
    scale: 100
  };

  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'FZTY_SDHJ/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token: token,
        filename: FILENAME,
        type
      }
    });
  }

  handlePrintReport = () => {
    const {
      fileName,
      location: { query }
    } = this.props;

    const { wskey, token, type } = query;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    window.open(
      `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`
   ,'_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
    );
  };
  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  render() {
    // const {getFieldDecorator} = this.props.form;
    const { formData, location } = this.props;
    const {
      query: { type }
    } = location;
    return (
      <div>
        <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
          <Toolbar
            style={{ display: type === 'edit' ? 'inherit' : 'none' }}
            onSliderChange={this.onSliderChange}
            handleScaleReport={this.handleScaleReport}
            handlePrintReport={this.handlePrintReport}
            handlePreviewReport={this.handlePreviewReport}
            scale={this.state.scale}
          />
          <div className={styles.headDiv1}>{formData.gafj}</div>
          <div className={styles.wsTitle}>
            <span>送达回执</span>
          </div>
          <div className={styles.headDiv2}>
            <span>{formData.wszh}</span>
          </div>
          <table className={styles.table}>
            <tbody>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>文书名称、编号</td>
                <td>
                  《{formData.reservation04}》{formData.wszh1}
                  {formData.reservation05 == null ? '' : ','}《{formData.reservation05}》{formData.wszh2}
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 时 间</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>受 送 达 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 地 点</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 方 式</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>代 收 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>代 收 原 因</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>与受送达人关系</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>受送达人拒收原因</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>见 证 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>备 注</td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <div className={styles.footer}>
            <span>存根</span>
          </div>
        </div>
        <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
          <Toolbar
            style={{ display: type === 'edit' ? 'inherit' : 'none' }}
            onSliderChange={this.onSliderChange}
            handleScaleReport={this.handleScaleReport}
            handlePrintReport={this.handlePrintReport}
            handlePreviewReport={this.handlePreviewReport}
            scale={this.state.scale}
          />
          <div className={styles.headDiv1}>{formData.gafj}</div>
          <div className={styles.wsTitle}>
            <span>送达回执</span>
          </div>
          <div className={styles.headDiv2}>
            <span>{formData.wszh}</span>
          </div>
          <table className={styles.table}>
            <tbody>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>文书名称、编号</td>
                <td>
                  《{formData.reservation04}》{formData.wszh1}
                  {formData.reservation05 == null ? '' : ','}《{formData.reservation05}》{formData.wszh2}
                </td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 时 间</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>受 送 达 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 地 点</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 方 式</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>代 收 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>代 收 原 因</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>与受送达人关系</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>受送达人拒收原因</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>见 证 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>送 达 人</td>
                <td></td>
              </tr>
              <tr className={styles.normalTr}>
                <td className={styles.tableTd}>备 注</td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <div className={styles.footer}>
            <span>随根</span>
          </div>
        </div>
      </div>
    );
  }
}

export default Index;
