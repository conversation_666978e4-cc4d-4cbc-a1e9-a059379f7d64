.normal {
  width: 664px;
  height: 993px;
  box-sizing: border-box;
  page-break-after: always;
  page-break-before: always;
  margin: 0 auto;
}

.titleZh{
  //font-size: 12pt;
  font-family: 'STFangsong, <PERSON>Song, 华文仿宋, 仿宋', serif;
  display: flex;
  justify-content: space-between;
}

.wsTitle {
  height: 64px;
  font-size: 16pt;
  font-family: 'STF<PERSON>song, FangSong, 华文仿宋, 仿宋', serif;
  text-align: center;
  line-height: 64px;
}
.table {
  font-size: 12pt;
  font-family: 'STFangsong, FangSong, 华文仿宋, 仿宋', serif;
  //border:1px solid #000;
  width: 100%;
  //height: 100%;
  border-collapse: collapse;
  table-layout: fixed;;
  td {
    border: 1px solid #000;
    color:#000;
  }

  .normalTr {
    height: 48px;
    text-align: center;
  }
}

.secTable {
  font-size: 12pt;
  font-family: 'ST<PERSON><PERSON>song, <PERSON>Song, 华文仿宋, 仿宋', serif;
  //border:1px solid #000;
  width: 100%;
  //height: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  td {
    border: 1px solid #000;
    border-top: none;
    color:#000;
  }

  .normalTr {
    height: 48px;
    text-align: center;
  }
}

.headDiv1 {
  text-align: center;
  font-size: 14pt;
  font-family: 'STFangsong, FangSong, 华文仿宋, 仿宋', serif;
}
.headDiv2 {
  font-family: 'STFangsong, FangSong, 华文仿宋, 仿宋', serif;
  margin-left: 18px;
  margin-bottom: 5px;
}
.tableTd1 {
  width: 120px;
  border-left: none !important;
  height: 80px;
}
.tableTd2 {
  width: 396px;
  border-right: none !important;
}
.tableTdDiv {
  margin-top: 15px;
}
.table2Td {
  padding: 0px;
}
.tableTrTd1 {
  height: 50px;
  width: 132.8px;
  border: none !important;
}
.tableTrTd2 {
  height: 50px;
  width: 132.8px;
  border: none !important;
  border-top: 1px solid #000000 !important;
}
