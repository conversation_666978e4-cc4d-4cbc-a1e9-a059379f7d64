import React, { Component } from 'react';
import { connect } from 'dva';
import Seal from '../../compoments/seal/Seal';
import Sign from '../../compoments/seal/Sign';
import commonStyles from '../lesses/index.less';
import styles from './index.less';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname: wsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const approvalformView = bgsData.systemid ? (
      <div className={commonStyles.normal}>
        <div className={commonStyles.wsTitle}>公安部电信诈骗案件侦办平台呈请冻结审批表</div>
        <div
          className={styles.titleZh}
          style={{ width: '100%', paddingBottom: '25px', paddingRight: '5px', height: '18px' }}
        >
          <div>填表单位： {bgsData.transactunit1}</div>
          <div>填表日期 {bgsData.writetime}</div>

        </div>
        <table className={commonStyles.table} style={{ fontSize: '12pt' }}>
          <tbody>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                案件名称
              </td>
              <td width="80%" colSpan={8}>
                {bgsData.ajmc || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                案件编号
              </td>
              <td width="80%" colSpan={8}>
                {bgsData.ajbh || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                法律文书号
              </td>
              <td width="80%" colSpan={8}>
                {bgsData.dw2 || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                冻结类别
              </td>
              <td width="80%" colSpan={8}>
                {bgsData.qbhslx || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                冻结账号
              </td>
              <td width="80%" colSpan={8}>
                {bgsData.detailreason || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="20%" colSpan={2}>
                承办人
              </td>
              <td width="30%" colSpan={3}>
                {bgsData.transactprimarytout || ''}
              </td>
              <td width="20%" colSpan={2}>
                联系方式
              </td>
              <td width="30%" colSpan={3}>
                {bgsData.lxdh || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="10%" colSpan={1}>
                简要案情
              </td>
              <td width="90%" colSpan={9} style={{ height: '300px', verticalAlign: 'top', textAlign: 'left' }}>
                {bgsData.analysiscase || ''}
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="10%" colSpan={1}>
                申请人
              </td>
              <td width="40%" colSpan={4} style={{ position:"relative", height: '100px', verticalAlign: 'top', textAlign: 'left' }} >
                <div style={{position:"absolute",right:'10px',top:'40px'}}>{bgsData.cqrqm1 ? <Sign imageBase64={bgsData.cqrqm1} style={{ position: '' }} /> : <span>{bgsData.cqrxm1}</span>}</div>
                <div style={{position:"absolute",right:'100px',top:'40px'}}>{bgsData.cqrqm2 ? <Sign imageBase64={bgsData.cqrqm2} style={{ position: '' }} /> : <span>{bgsData.cqrxm2}</span>}</div>
                <div style={{position:"absolute",right:'10px',top:'70px'}}>{bgsData.submittimenyr || ''}</div>
              </td>
              <td width="10%" colSpan={1}>
                刑侦部门负责人
              </td>
              <td width="40%" colSpan={4} style={{ position:"relative", height: '100px', verticalAlign: 'top', textAlign: 'left' }} >
                <div>
                  {bgsData.ldps2 || ''}
                </div>
                <div style={{position:"absolute",right:'80px',top:'50px'}}>
                  {bgsData.dwldqm ? <Sign imageBase64={bgsData.dwldqm} style={{ right: 140, bottom: 0 }} /> : <span>{bgsData.dwldxm}</span>}
                  <Seal imageBase64={bgsData.cqdwyz} style={{ right: 10, bottom: 1 }} />
                </div>
                <div style={{position:"absolute",right:'10px',top:'70px'}}>{bgsData.ldpssj2 || ''}</div>
              </td>
            </tr>
            <tr style={{breakInside: "avoid"}}>
              <td width="10%" colSpan={1}>
                区县级以上公安机关意见
              </td>
              <td width="90%" colSpan={9} style={{position:"relative", height: '150px', verticalAlign: 'top', textAlign: 'left' }} >
                <div>{bgsData.ldps1 || ''}</div>
                <div style={{position:"absolute",right:'80px',top:'100px'}}>
                  {bgsData.spldqm ? <Sign imageBase64={bgsData.spldqm} style={{ right: 140, bottom: 0 }} /> : <span>{bgsData.spldxm}</span>}
                  <Seal imageBase64={bgsData.spdwyz} style={{ right: 10, bottom: 1 }} />
                </div>
                <div style={{position:"absolute",right:'10px',top:'130px'}}>{bgsData.ldpssj1 || ''}</div>
              </td>
            </tr>
          </tbody>
        </table>
        <div style={{pageBreakBefore:'always'}}>
          <div className={commonStyles.normal}>
            <div className={commonStyles.wsTitle}>公安部电信诈骗案件侦办平台规范使用责任书</div>
            <pre style={{ fontSize: '16pt' }}>
              {'    为切实加强公安部电信诈骗案件侦办平台用户的安全使用和管\n' +
              '理，确保国家秘密和警务工作秘密的绝对安全，严防出现因越权访\n' +
              '问、违规操作等造成公安敏感信息泄露、侵犯公民隐私的行为发生，\n' +
              '严防使用该平台查询非电信网络诈骗案件，平台用户必须严格落实公\n' +
              '安信息共享查询应用“七不准”管理规定，并承诺做到：\n' +
              '    一、严格遵守国家保密法律、法规和有关规章制度，履行保密义\n' +
              '务，保守党和国家的秘密、保守公安警务工作秘密。\n' +
              '    二、不将“公安部电信诈骗案件侦办平台”等公安信息共享查询\n' +
              '应用系统授权数字证书转借他人使用。\n' +
              '    三、不违规申报和审批“公安部电信诈骗案件侦办平台”等公安\n' +
              '信息共享查询应用系统授权用户。\n' +
              '    四、不非法扫描、入侵“公安部电信诈骗案件侦办平台”等公安\n' +
              '信息共享查询应用系统，扰乱系统正常运行。\n' +
              '    五、不公开宣传、与无关人员谈论“公安部电信诈骗案件侦办平\n' +
              '台”等公安信息共享查询应用系统及其相关内容。\n' +
              '    六、不泄露、传播或出售通过“公安部电信诈骗案件侦办平台”\n' +
              '等公安信息共享查询应用系统获取的信息。\n' +
              '    七、不对违规使用“公安部电信诈骗案件侦办平台”等公安信息\n' +
              '共享查询应用系统行为隐瞒不报、包庇袒护。\n' +
              '    八、非因工作原因，不使用“公安部电信诈骗案件侦办平台”等\n' +
              '公安信息共享查询应用系统，不使用通过“公安部电信诈骗案件侦办\n' +
              '平台”等公安信息共享查询应用系统获取的信息。\n' +
              '    如若违反上述承诺，自愿承担党纪、政纪责任和法律后果。\n' +
              '\n' +
              '\n' +
              '                                         责任人：\n' +
              '                                        年   月   日'}
            </pre>
          </div>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{approvalformView}</div>;
  }
}
export default Index;
