/**
 * Created by yam on 2021-09-14
 * 功能描述：呈请听证报告书（听证案件）   报告书打印预览
 */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Icon } from 'antd';
import moment from 'moment';
import styles from './index.less';
import { url } from '../../utils/func';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const sswsname = url.getHashParam('sswsname') || '听证报告书';
    console.log(bgsData,'bgsData')
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div className={styles.unit}>{bgsData.gafj || ''}</div>
        <div className={styles.wsTitle}>{sswsname}</div>
        <div className={styles.zihao}>{bgsData.wszh || ''}</div>
        <div  className={styles.body}>
          <p>案由：{bgsData.reservation07 || ''}</p>
          <p>时间：{bgsData.beginhappentime || ''}<span  style={{padding:'0  30px'}}>至</span>{bgsData.endhappentime || ''}</p>
          <p>地点：{bgsData.zddddd || ''}</p>
          <p>举行方式：{bgsData.bank || ''}</p>
          <p>听证主持人：{bgsData.zwxm || ''}</p>
          <p>听证员：{bgsData.dwmc || ''}</p>
          <p>记录员：{bgsData.dw2 || ''}</p>
          <p>违法嫌疑人：{bgsData.xm2 || ''}</p>
          <p>法定代表人：{bgsData.frdb || ''}</p>
          <p>委托代理人：{bgsData.tosomebody || ''}</p>
          <p>本案其他利害关系人：{bgsData.oldbriefreason || ''}</p>
          <p>本案其他利害关系人的代理人：{bgsData.receiveby || ''}</p>
          <p>本案办案人民警察：{bgsData.zxdxxx || ''}</p>
          <p>听证会基本情况：{bgsData.submitnote || ''}</p>
          <p>案件事实：{bgsData.transact || ''}</p>
          <p>处理意见和建议：</p>
        </div>
        <div className={styles.body}>
          <p  className={styles.qiangming}><span>听证主持人（签名）：{bgsData.zwxm || ''}</span></p>
          <div className={styles.date}><div><span></span>年<span></span>月<span></span>日</div></div>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;



