/**
 * 询 问 笔 录-（不按规定登记住宿旅客信息）
 * 证人
 *
 * */

import React, { Component } from 'react';
import { connect } from 'dva';
import styles from './index.less';
import { getLineArr } from '@/utils/func';

class Index extends Component {
  constructor() {
    super();
    this.state = {};
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    // debugger
    const { wskey, token, systemCode } = query;
    // const systemid = url.getHashParam('systemid');
    // const token = url.getHashParam('token');
    dispatch({
      type: 'Bagddjzslkofzr/fetch',
      payload: {
        systemid: wskey,
        token,
        systemCode
      }
    });
  }
  getLine = (data) => {
    const dd = getLineArr(data).map((item, index) => {
      if (index === 0) {
        return <div key={index} className={styles.blackLineFirst} />;
      } else {
        return <div key={index} className={styles.blackLineArr} />;
      }
    });
    return <div className={styles.linePosition}>{dd}</div>;
  };
  // 自定义问题格式处理
  zdy = (obj) => {
    const arr = [];
    // eslint-disable-next-line guard-for-in
    for (const key in obj) {
      if (key.indexOf('question') > -1) {
        const indexArr = key.split('question');
        arr[indexArr[1] - 1] = { ...arr[indexArr[1] - 1], question: obj[key] };
      }
      if (key.indexOf('answer') > -1) {
        const indexArr = key.split('answer');
        arr[indexArr[1] - 1] = { ...arr[indexArr[1] - 1], answer: obj[key] };
      }
    }
    return arr;
  };
  renderZdywd = (zdyAnswer) => {
    return (
      <div>
        {zdyAnswer &&
          zdyAnswer.map((item1, index) => {
            return (
              <div>
                {/* <div className={styles.item}> */}
                {/*  <span>{`自定义问题${index + 1}：`}</span> */}
                {/* </div> */}
                <div>
                  <div className={styles.item}>
                    <span>问：</span>
                    <span>{item1.question || ''}</span>
                    {this.getLine(item1.question || '')}
                  </div>
                </div>
                <div>
                  <div className={styles.item}>
                    <span>答：</span>
                    <span>{item1.answer || ''}</span>
                    {this.getLine(item1.answer || '')}
                  </div>
                </div>
              </div>
            );
          })}
      </div>
    );
  };
  render() {
    const { formData } = this.props;
    const listData = formData.content ? JSON.parse(formData.content) : {};
    const zdyAnswer = listData && this.zdy(listData);
    const listView = formData.systemid ? (
      <div className={styles.body}>
        <div className={styles.normal}>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.wsTitle} style={{ display: 'inline-block', padding: '0 0 10px' }}>
              询 问 笔 录
            </div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.wsTitle} style={{ display: 'inline-block' }}>
              （不按规定登记住宿旅客信息案件）
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>时间：</span>
              <span className={styles.date}>
                <span className={styles.blackLine}>
                  {listData.kssjCn || '          年     月     日     时     分'}
                </span>
                <span style={{ padding: '0px 10px' }}>至</span>
                <span className={styles.blackLine}>
                  {listData.jssjCn || '          年     月     日     时     分'}
                </span>
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>地点：</span>
              <span className={styles.blackLine}>
                {listData.dd || '                                                          '}
              </span>
            </div>
          </div>
          <div>
            <div className={`${styles.item} ${styles.imgContent1}`}>
              <span>询问人：</span>
              <span className={styles.blackLine}>
                {/* {listData.xwry1Cn || '                                             '} */}
                <span className={styles.imgSeat}>
                  <img className={styles.imgPostion} src={listData.bamjSign1 || ''} />
                </span>
              </span>
              <span style={{ padding: '0px 10px' }}>、</span>
              <span className={styles.blackLine}>
                {/* {listData.xwry2Cn || '                                             '} */}
                <span className={styles.imgSeat}>
                  <img className={styles.imgPostion} src={listData.bamjSign2 || ''} />
                </span>
              </span>
              <span>工作单位：</span>
              <span className={styles.blackLine}>
                {listData.xwrgzdwCn || '                                                                      '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>记录人：</span>
              <span className={styles.blackLine}>
                {listData.jlrCn || '                                                '}
              </span>
              <span>工作单位：</span>
              <span className={styles.blackLine}>
                {listData.jlrgzdwCn || '                                                '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>被询问人：</span>
              <span className={styles.blackLine}>{listData.xm || '                                 '}</span>
              <span>性别：</span>
              <span className={styles.blackLine}>{listData.xbCn || '                  '}</span>
              <span>年龄：</span>
              <span className={styles.blackLine}>{listData.nl || '                  '}</span>
              <span>出生日期：</span>
              <span className={styles.blackLine}>
                {listData.csrqCn || '                                          '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>身份证种类及号码：</span>
              <span className={styles.blackLine}>{listData.zjzlCn || '                                   '}</span>
              <span className={styles.blackLine}>{listData.zjhm || '                               '}</span>
            </div>
          </div>

          <div>
            <div className={styles.item}>
              <div className={styles.checkboxImg}>
                {listData.sfrddb ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>是</span>
              <div className={styles.checkboxImg}>
                {!listData.sfrddb ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>否 人大代表</span>
              <span className={styles.fenge} />

              <div className={styles.checkboxImg}>
                {listData.sfzxwy ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>是</span>
              <div className={styles.checkboxImg}>
                {!listData.sfzxwy ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>否 政协委员</span>
              <span className={styles.fenge} />

              <div className={styles.checkboxImg}>
                {listData.sfzgdy ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>是</span>

              <div className={styles.checkboxImg}>
                {!listData.sfzgdy ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>否 中共党员</span>
              <span className={styles.fenge} />

              <div className={styles.checkboxImg}>
                {listData.sfgjgzry ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>是</span>

              <div className={styles.checkboxImg}>
                {!listData.sfgjgzry ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>否 国家工作人员</span>
              <span className={styles.fenge} />

              <div className={styles.checkboxImg}>
                {listData.ywwffzqk ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>有</span>
              <div className={styles.checkboxImg}>
                {!listData.ywwffzqk ? (
                  <img src={require('../../assets/box_check.png')} alt="" />
                ) : (
                  <img src={require('../../assets/box_no_check.png')} alt="" />
                )}
              </div>
              <span>无 违法犯罪前科</span>
            </div>
          </div>

          <div>
            <div className={styles.item}>
              <span>现住址：</span>
              <span className={styles.blackLine}>
                {listData.xzz || '                                                    '}
              </span>
              <span>联系方式：</span>
              <span className={styles.blackLine}>
                {listData.lxdh || '                                                    '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>户籍所在地：</span>
              <span className={styles.blackLine}>
                {listData.hjszd ||
                  '                                                                                                          '}
              </span>
            </div>
          </div>
          {/*    <div>
            <div className={styles.item}>
              <span>（口头传唤∕被扭送∕自动投案的被询问/讯问人</span>
              <span className={styles.blackLine}>
                {listData.xwkssjCn || '          年       月       日      时     分'}
              </span>
              <span>到达，</span>
              <span className={styles.blackLine}>
                {listData.xwjssjCn || '          年       月       日      时     分'}
              </span>
              <span>离开，本人签名</span>
              <span className={styles.blackLine}>{listData.bxwrxm || '                              '}</span>
              <span>)。</span>
            </div>
          </div> */}
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                {`你好，我们是${listData.xwrgzdwCn}的民警（出示工作证件），现依法对你进行询问，请你如实回答我们的问题并协助调查，你有权拒绝回答与案件无关的问题。本次询问通过全程录音录像的方式进行，你是否清楚？`}
              </span>
              {this.getLine(
                `你好，我们是${listData.xwrgzdwCn}的民警（出示工作证件），现依法对你进行询问，请你如实回答我们的问题并协助调查，你有权拒绝回答与案件无关的问题。本次询问通过全程录音录像的方式进行，你是否清楚？`
              )}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.sfqc === '0' ? '清楚了。' : listData.sfqc === '1' ? '不清楚。' : ''}</span>
              {this.getLine(listData.sfqc === '0' ? '清楚了。' : listData.sfqc === '1' ? '不清楚。' : '')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>说一下你的基本情况，包括姓名、出生日期、身份证码、 户籍所在地和现住址、文化程度等。</span>
              {this.getLine('说一下你的基本情况，包括姓名、出生日期、身份证码、 户籍所在地和现住址、文化程度等。')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.jbqk || ''}</span>
              {this.getLine(listData.jbqk || '')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你是否申请回避？</span>
              {this.getLine('你是否申请回避？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.sfhb === '0' ? '申请' : listData.sfhb === '1' ? '不申请' : ''}</span>
              {this.getLine(listData.sfhb === '0' ? '申请' : listData.sfhb === '1' ? '不申请' : '')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你把入住的情况讲一下？</span>
              {this.getLine('你把入住的情况讲一下？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.rzqk || ''}</span>
              {this.getLine(listData.rzqk || '')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>工作人员是否有登记你的信息？</span>
              {this.getLine('工作人员是否有登记你的信息？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.sfdjxx || ''}</span>
              {this.getLine(listData.sfdjxx || '')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>工作人员为何不登记你的信息？</span>
              {this.getLine('工作人员为何不登记你的信息？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>{listData.whbdjxx || ''}</span>
              {this.getLine(listData.whbdjxx || '')}
            </div>
          </div>
          {this.renderZdywd(zdyAnswer)}
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你还有什么要补充的吗？</span>
              {this.getLine('你还有什么要补充的吗？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.bcss === '0' ? '有。' : listData.bcss === '1' ? '没有。' : ''}
                {listData.bcqk || ''}
              </span>
              {this.getLine(
                listData.bcss === '0' ? '有。' : listData.bcss === '1' ? '没有。' : `${listData.bcqk}` || ''
              )}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>以上笔录是否与你所说的一致？</span>
              {this.getLine('以上笔录是否与你所说的一致？')}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfss === '0'
                  ? '以上笔录我看过，与我所说的一致。'
                  : listData.sfss === '1'
                    ? '以上笔录已经念给我听了，与我所说的一致。'
                    : listData.sfss === '2'
                      ? '其他。'
                      : ''}
                {listData.sgtxqk || ''}
              </span>
              {this.getLine(
                listData.sfss === '0'
                  ? '以上笔录我看过，与我所说的一致。'
                  : listData.sfss === '1'
                    ? '以上笔录已经念给我听了，与我所说的一致。'
                    : listData.sfss === '2'
                      ? '其他。'
                      : `${listData.sgtxqk}` || ''
              )}
            </div>
          </div>
          <div style={{ marginTop: '20px' }}>
            <div>
              <span style={{ position: 'relative', right: '-370px' }}>被询问人签名:</span>
              <span className={styles.imgSeat} style={{ position: 'relative', right: '-400px' }}>
                <img
                  className={styles.imgPostion}
                  // style={{ maxWidth: '80px', height: '27px', position: 'relative', right: '-400px', verticalAlign: 'sub' }}
                  src={listData.bxwrSign || ''}
                />
              </span>
            </div>
            {listData?.substitute === '1' && (
              <div>
                <div style={{ position: 'relative', right: '-370px', width: '200px' }}>
                  当事人系文盲无法签名，由民警代签名。
                </div>
              </div>
            )}
            <div>
              <span style={{ position: 'relative', right: '-370px' }}>日期:{listData.rqCn || ''}</span>
            </div>
          </div>
        </div>
      </div>
    ) : (
      <div className={styles.loading}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ Bagddjzslkofzr }) => ({
  ...Bagddjzslkofzr
}))(Index);
