import uuidv4 from 'uuid/v4';

export const getDataConfig = (formData) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 1,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '此联存根',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '行政处罚决定书(派出所)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  ' 案   由:  <%BRIEFREASON%>\n' +
                  ' 被处罚人:  <%ZWXM%>\n' +
                  ' 性别:  <%SEX%>\n' +
                  ' 出生日期:  <%BIRTHDAYINBEGIN%>\n' +
                  ' 身份证件种类及号码:  <%CARDTYPE%>  <%ZJHM%>\n' +
                  ' 现 住 址:  <%XZDZ%>\n' +
                  ' 工作单位:  <%WORKIN%>\n' +
                  ' 法定代表人:  <%FRDB%>\n' +
                  ' 行政处罚决定:  <%XXZZ%>\n' +
                  ' 办案单位:  <%TRANSACTUNIT1%>\n' +
                  ' 承 办 人:  <%TRANSACTPRIMARYTOUT%>\n' +
                  ' 批 准 人:  <%CONFIRMBYPERSON%>\n' +
                  ' 填 发 人:  <%WRITEBY%>\n' +
                  ' 填发日期:  <%WRITETIME%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CARDTYPE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WORKIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTUNIT1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMBYPERSON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITEBY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuidv4(),
        pageStyle: {
          height: '917px'
        },
        footText: '一式三份，被处罚人和执行单位各一份，一份附卷。治安案件有被侵害人的，复印送达被侵害人。',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '行政处罚决定书(派出所)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    违法行为人<%XZCFDX%>\n' +
                  '    <%DETAILREASON%>。\n' +
                  '    以上事实有<%POLITICALPARTY%>等证据证实。\n' +
                  '    据<%LAWNAME%>之规定，现决定<%XXZZ%>。\n' +
                  '    <%DW2%>。' +
                  `${
                    formData.money === '0'
                      ? ''
                      : '    (逾期不交纳罚款的，每日按罚款数额的百分之三加处罚款，罚款的数额不超过罚款本数。)\n'
                  }` +
                  '    如不服本决定，' +
                  '可以在收到本决定书之日起六十日内向<%RESERVATION10%>申请行政复议或者在六个月内依法向' +
                  '<%RESERVATION09%>提起行政诉讼 。\n' +
                  '    附：<%ZXDXXX%>清单共<%ZY2%>份',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XZCFDX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DETAILREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'POLITICALPARTY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DW2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'MONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION10',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZXDXXX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZY2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  // top: '540px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '行政处罚决定书已向我宣告并送达。\n被处罚人:\n                年    月    日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  // width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  // marginLeft: '270px',
                  // top: '30px',
                  marginTop: '90px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [

            ]
          }
        ]
      },
      {
        key: uuidv4(),
        pageStyle: {
          height: '917px'
        },
        footText: '一式三份，被处罚人和执行单位各一份，一份附卷。治安案件有被侵害人的，复印送达被侵害人。',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '行政处罚决定书(派出所)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    违法行为人<%XZCFDX%>\n' +
                  '    <%DETAILREASON%>。\n' +
                  '    以上事实有<%POLITICALPARTY%>等证据证实。\n' +
                  '    根据<%LAWNAME%>之规定，现决定<%XXZZ%>。\n' +
                  '    <%DW2%>。' +
                  `${
                    formData.money === '0'
                      ? ''
                      : '    (逾期不交纳罚款的，每日按罚款数额的百分之三加处罚款，罚款的数额不超过罚款本数。)\n'
                  }` +
                  '    如不服本决定，' +
                  '可以在收到本决定书之日起六十日内向<%RESERVATION10%>申请行政复议或者在六个月内依法向' +
                  '<%RESERVATION09%>提起行政诉讼 。\n' +
                  '    附：<%ZXDXXX%>清单共<%ZY2%>份',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XZCFDX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DETAILREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'POLITICALPARTY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DW2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'MONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION10',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZXDXXX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZY2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  // top: '540px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '行政处罚决定书已向我宣告并送达。\n被处罚人:\n                年    月    日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  // width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  // marginLeft: '270px',
                  // top: '30px',
                  marginTop: '90px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [

            ]
          }
        ]
      },
      {
        key: uuidv4(),
        pageStyle: {
          height: '917px'
        },
        footText: '一式三份，被处罚人和执行单位各一份，一份附卷。治安案件有被侵害人的，复印送达被侵害人。',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '行政处罚决定书(派出所)',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    违法行为人<%XZCFDX%>\n' +
                  '    <%DETAILREASON%>。\n' +
                  '    以上事实有<%POLITICALPARTY%>等证据证实。\n' +
                  '    根据<%LAWNAME%>之规定，现决定<%XXZZ%>。\n' +
                  '    <%DW2%>。' +
                  `${
                    formData.money === '0'
                      ? ''
                      : '    (逾期不交纳罚款的，每日按罚款数额的百分之三加处罚款，罚款的数额不超过罚款本数。)\n'
                  }` +
                  '    如不服本决定，' +
                  '可以在收到本决定书之日起六十日内向<%RESERVATION10%>申请行政复议或者在六个月内依法向' +
                  '<%RESERVATION09%>提起行政诉讼 。\n' +
                  '    附：<%ZXDXXX%>清单共<%ZY2%>份',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XZCFDX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DETAILREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'POLITICALPARTY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'LAWNAME',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'DW2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'MONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION10',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION09',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZXDXXX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZY2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  // top: '540px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%gafj_1%>\n<%writetimenyr%>',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '行政处罚决定书已向我宣告并送达。\n被处罚人:\n                年    月    日',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  // width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  // marginLeft: '270px',
                  // top: '30px',
                  marginTop: '90px',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [

            ]
          }
        ]
      },
    ] // 文书联数
  };
};

