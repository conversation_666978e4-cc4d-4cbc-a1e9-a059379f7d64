import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { dataConfig } from './reportConfig/B008';
import styles from './index.less';

const SSWSNAME = '安排律师会见非涉密案件在押犯罪嫌疑人通知书';
const FILENAME = 'B008';

@connect(({ B008 }) => B008)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'B008/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={dataConfig(formData)}
          wsData={formData}
          formConfig={formConfig}
          fileName={FILENAME}
          showPage={page}
          isQM
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={dataConfig(formData)} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
