import uuidv4 from 'uuid/v4';

/**
 * 取保候审保证书模板
 * */
export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText:
        '本告诫书一式五份：一份存档，一份交家庭暴力加害人，一份交家庭暴力受害人，两份抄\n' +
        '送当事人住所地或经常居住地居（村）民委员会、妇联组织',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '家庭暴力告诫书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '34px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '家庭暴力加害人姓名<%XM2%>\n' +
                '性别<%XB2%> 年龄<%XB2%> 出生日期<%CSRQ2%> 身份证种类及号码<%PERFORMBY%>\n' +
                '现住<%XXZZ%>\n' +
                '现查明<%XXZZ%>\n' +
                ',以上事实有\n' +
                '等证据证实\n' +
                '根据《中华人民共和国反家庭暴力法》第十六条之规定，决定对<%XXZZ%>给予告诫，请及时纠正不法行为，严谨对家庭成员再次实施家庭暴力行为，如有违法犯罪行为，公安机关将依法处理。\n',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.6,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'dynamic',
                    params: {
                      configId: 'v_bzr_xm',
                      ajbh: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XB2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: '01'
                  },
                  elementStyle: { fontSize: '16pt' }
                },
                { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                {
                  key: uuidv4(),
                  textName: 'PERFORMBY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: 'wg_03' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: '08' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '20px',
                // lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '被告诫人（签名、捺印）\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                left: 0,
                top: '120px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
          ]
        },
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'bigText', // 大文本段落
        //       tempStr: '保证人：              \n年   月   日',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '100%',
        //         position: 'absolute',
        //         textAlign: 'right',
        //         paddingRight: '20px',
        //         lineHeight: 2,
        //         left: 0,
        //         top: '-60px',
        //         fontSize: '16pt'
        //       }
        //     }
        //   ]
        // }
      ]
    },
    {
      key: uuidv4(),
      footText:
        '本告诫书一式五份：一份存档，一份交家庭暴力加害人，一份交家庭暴力受害人，两份抄\n' +
        '送当事人住所地或经常居住地居（村）民委员会、妇联组织',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '家庭暴力告诫书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '34px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '家庭暴力加害人姓名<%XM2%>\n' +
                '性别<%XB2%> 年龄<%XB2%> 出生日期<%CSRQ2%> 身份证种类及号码<%PERFORMBY%>\n' +
                '现住<%XXZZ%>\n' +
                '现查明<%XXZZ%>\n' +
                ',以上事实有\n' +
                '等证据证实\n' +
                '根据《中华人民共和国反家庭暴力法》第十六条之规定，决定对<%XXZZ%>给予告诫，请及时纠正不法行为，严谨对家庭成员再次实施家庭暴力行为，如有违法犯罪行为，公安机关将依法处理。\n',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.6,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'dynamic',
                    params: {
                      configId: 'v_bzr_xm',
                      ajbh: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XB2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: '01'
                  },
                  elementStyle: { fontSize: '16pt' }
                },
                { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                {
                  key: uuidv4(),
                  textName: 'PERFORMBY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: 'wg_03' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: '08' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '20px',
                // lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '被告诫人（签名、捺印）\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                left: 0,
                top: '120px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
          ]
        },
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'bigText', // 大文本段落
        //       tempStr: '保证人：              \n年   月   日',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '100%',
        //         position: 'absolute',
        //         textAlign: 'right',
        //         paddingRight: '20px',
        //         lineHeight: 2,
        //         left: 0,
        //         top: '-60px',
        //         fontSize: '16pt'
        //       }
        //     }
        //   ]
        // }
      ]
    },
    {
      key: uuidv4(),
      footText:
        '本告诫书一式五份：一份存档，一份交家庭暴力加害人，一份交家庭暴力受害人，两份抄\n' +
        '送当事人住所地或经常居住地居（村）民委员会、妇联组织',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '家庭暴力告诫书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '34px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '家庭暴力加害人姓名<%XM2%>\n' +
                '性别<%XB2%> 年龄<%XB2%> 出生日期<%CSRQ2%> 身份证种类及号码<%PERFORMBY%>\n' +
                '现住<%XXZZ%>\n' +
                '现查明<%XXZZ%>\n' +
                ',以上事实有\n' +
                '等证据证实\n' +
                '根据《中华人民共和国反家庭暴力法》第十六条之规定，决定对<%XXZZ%>给予告诫，请及时纠正不法行为，严谨对家庭成员再次实施家庭暴力行为，如有违法犯罪行为，公安机关将依法处理。\n',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.6,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'dynamic',
                    params: {
                      configId: 'v_bzr_xm',
                      ajbh: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XB2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: '01'
                  },
                  elementStyle: { fontSize: '16pt' }
                },
                { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                {
                  key: uuidv4(),
                  textName: 'PERFORMBY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: 'wg_03' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: '08' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '20px',
                // lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '被告诫人（签名、捺印）\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                left: 0,
                top: '120px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
          ]
        },
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'bigText', // 大文本段落
        //       tempStr: '保证人：              \n年   月   日',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '100%',
        //         position: 'absolute',
        //         textAlign: 'right',
        //         paddingRight: '20px',
        //         lineHeight: 2,
        //         left: 0,
        //         top: '-60px',
        //         fontSize: '16pt'
        //       }
        //     }
        //   ]
        // }
      ]
    },
    {
      key: uuidv4(),
      footText:
        '本告诫书一式五份：一份存档，一份交家庭暴力加害人，一份交家庭暴力受害人，两份抄\n' +
        '送当事人住所地或经常居住地居（村）民委员会、妇联组织',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '家庭暴力告诫书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '34px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '家庭暴力加害人姓名<%XM2%>\n' +
                '性别<%XB2%> 年龄<%XB2%> 出生日期<%CSRQ2%> 身份证种类及号码<%PERFORMBY%>\n' +
                '现住<%XXZZ%>\n' +
                '现查明<%XXZZ%>\n' +
                ',以上事实有\n' +
                '等证据证实\n' +
                '根据《中华人民共和国反家庭暴力法》第十六条之规定，决定对<%XXZZ%>给予告诫，请及时纠正不法行为，严谨对家庭成员再次实施家庭暴力行为，如有违法犯罪行为，公安机关将依法处理。\n',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.6,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'dynamic',
                    params: {
                      configId: 'v_bzr_xm',
                      ajbh: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XB2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: '01'
                  },
                  elementStyle: { fontSize: '16pt' }
                },
                { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                {
                  key: uuidv4(),
                  textName: 'PERFORMBY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: 'wg_03' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: '08' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '20px',
                // lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '被告诫人（签名、捺印）\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                left: 0,
                top: '120px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
          ]
        },
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'bigText', // 大文本段落
        //       tempStr: '保证人：              \n年   月   日',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '100%',
        //         position: 'absolute',
        //         textAlign: 'right',
        //         paddingRight: '20px',
        //         lineHeight: 2,
        //         left: 0,
        //         top: '-60px',
        //         fontSize: '16pt'
        //       }
        //     }
        //   ]
        // }
      ]
    },
    {
      key: uuidv4(),
      footText:
        '本告诫书一式五份：一份存档，一份交家庭暴力加害人，一份交家庭暴力受害人，两份抄\n' +
        '送当事人住所地或经常居住地居（村）民委员会、妇联组织',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '家庭暴力告诫书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '34px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '家庭暴力加害人姓名<%XM2%>\n' +
                '性别<%XB2%> 年龄<%XB2%> 出生日期<%CSRQ2%> 身份证种类及号码<%PERFORMBY%>\n' +
                '现住<%XXZZ%>\n' +
                '现查明<%XXZZ%>\n' +
                ',以上事实有\n' +
                '等证据证实\n' +
                '根据《中华人民共和国反家庭暴力法》第十六条之规定，决定对<%XXZZ%>给予告诫，请及时纠正不法行为，严谨对家庭成员再次实施家庭暴力行为，如有违法犯罪行为，公安机关将依法处理。\n',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.6,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'dynamic',
                    params: {
                      configId: 'v_bzr_xm',
                      ajbh: ''
                    }
                  },
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XB2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    type: 'static',
                    kind: '01'
                  },
                  elementStyle: { fontSize: '16pt' }
                },
                { key: uuidv4(), textName: 'CSRQ2', textValue: '', textType: 'date', elementStyle: {} },
                { key: uuidv4(), textName: 'XXZZ', textValue: '', textType: 'string', elementStyle: {} },
                {
                  key: uuidv4(),
                  textName: 'PERFORMBY',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: 'wg_03' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'NOTE', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'DW2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: { type: 'static', kind: '08' },
                  elementStyle: {}
                },
                { key: uuidv4(), textName: 'LXFS2', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZWXM', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'GXLX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDXZ', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDXXX', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZXDDMC', textValue: '', textType: 'string', elementStyle: {} },
                { key: uuidv4(), textName: 'ZDDDDD', textValue: '', textType: 'string', elementStyle: {} }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '20px',
                // lineHeight: 2,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '被告诫人（签名、捺印）\n年   月   日',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 2,
                left: 0,
                top: '120px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
          ]
        },
        // {
        //   key: uuidv4(),
        //   editable: true,
        //   contents: [
        //     {
        //       key: uuidv4(),
        //       type: 'bigText', // 大文本段落
        //       tempStr: '保证人：              \n年   月   日',
        //       elementStyle: {
        //         fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
        //         width: '100%',
        //         position: 'absolute',
        //         textAlign: 'right',
        //         paddingRight: '20px',
        //         lineHeight: 2,
        //         left: 0,
        //         top: '-60px',
        //         fontSize: '16pt'
        //       }
        //     }
        //   ]
        // }
      ]
    },
  ] // 文书联数
};
