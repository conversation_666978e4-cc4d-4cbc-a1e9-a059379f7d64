import uuidv4 from 'uuid/v4';

export const dataConfig = (formData) => {
  const { departmentcode = '' , openWstype = ''} = formData;
  const dept = departmentcode.substring(0, 4);
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '逮捕通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'staticText',
                textValue: '(存根)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '案件名称： <%AJMC%>\n' +
                  '案件编号： <%AJBH%>\n' +
                  '被逮捕人： <%ZWXM%>  <%SEX%>\n' +
                  '出生日期： <%BIRTHDAYINBEGIN%>\n' +
                  '逮捕原因： <%OLDBRIEFREASON%>\n' +
                  '逮捕时间： <%ZDDDSJ%>\n' +
                  '羁押处所： <%ASSISTANTUNITINFACT%>\n' +
                  '家属姓名： <%XM2%>\n' +
                  '地址： <%XXZZ%>\n' +
                  '办案人： <%TRANSACTPRIMARYTOUT%>\n' +
                  '办案单位： <%TRANSACTUNIT%>\n' +
                  '填发时间： <%WRITETIME%>\n' +
                  '填发人： <%WRITEBY%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'AJBH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'OLDBRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    customField: true,
                    dateFormat: 'YYYY年MM月DD日HH时',
                    showTime: 'HH',
                    elementStyle: { width: '230px' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'rangedict',
                    dictConfig: {
                      type: 'dynamic',
                      params: {
                        configId: 'ssjycs',
                        code: ''
                      },
                      mixinModel: [
                        { label: '本市局羁押场所', defaultActive: true, type: 'dict', scope: dept },
                        { label: '全区羁押场所', defaultActive: false, type: 'dict', scope: '' }
                      ]
                    },
                    elementStyle: { fontSize: '16pt' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'XM2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTUNIT',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      type: 'static',
                      kind: '06'
                    },
                    elementStyle: {
                      fontSize: '16pt'
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITEBY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuidv4(),
        footText: '此联附卷',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '逮捕通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'staticText',
                textValue: '(副本)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%XM2%>：\n' +
                  '    经<%CONFIRMBYUNIT%>' +
                  `${formData.confirmbyunit && formData.confirmbyunit.lastIndexOf('检察院') !== -1 ? '批准' : '决定'}` +
                  `${(openWstype === 'print'|| openWstype === 'preview') && !formData.zdddsj ?'我局于    年  月  日':'，我局于<%ZDDDSJ%>'}` +
                  '对<%OLDBRIEFREASON%>' +
                  '罪的<%ZWXM%>' +
                  '执行逮捕，现羁押在<%ASSISTANTUNITINFACT%>' +
                  '。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XM2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMBYUNIT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      minWidth: '200px',
                      width: 'auto'
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    customField: true,
                    dateFormat: 'YYYY年MM月DD日HH时',
                    showTime: 'HH',
                    elementStyle: { width: '230px' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'OLDBRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'rangedict',
                    dictConfig: {
                      type: 'dynamic',
                      params: {
                        configId: 'ssjycs',
                        code: ''
                      },
                      mixinModel: [
                        { label: '本市局羁押场所', defaultActive: true, type: 'dict', scope: dept },
                        { label: '全区羁押场所', defaultActive: false, type: 'dict', scope: '' }
                      ]
                    },
                    elementStyle: { fontSize: '16pt' }
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  // top: '30px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: `<%gafj_1%>\n<%writetimenyr%>`,
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  textAlign: 'right',
                  paddingRight: '22px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '本通知书已收到。\n' +
                  '    被逮捕人家属：\n' +
                  '如在逮捕后24小时内无法通知的，注明原因：_______________\n' +
                  '_______________________________________________________\n' +
                  '_______________________________________________________\n' +
                  '    办案人：\n',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  paddingLeft: '22px',
                  marginTop: '50px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '年   月   日   时\n\n\n\n年   月   日   时\n',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  // width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  marginLeft: '350px',
                  marginTop: '90px',
                  paddingLeft: '22px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                }
              },
            ]
          },
        ]
      },
      {
        key: uuidv4(),
        footText: '此联交被捕人家属',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '逮捕通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'staticText',
                textValue: '(副本)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%XM2%>：\n' +
                  '    经<%CONFIRMBYUNIT%>' +
                  `${formData.confirmbyunit && formData.confirmbyunit.lastIndexOf('检察院') !== -1 ? '批准' : '决定'}` +
                  `${(openWstype === 'print'|| openWstype === 'preview') && !formData.zdddsj ?'我局于    年  月  日':'，我局于<%ZDDDSJ%>'}` +
                  // '，我局于<%ZDDDSJ%>' +
                  '对<%OLDBRIEFREASON%>' +
                  '罪的<%ZWXM%>' +
                  '执行逮捕，现羁押在<%ASSISTANTUNITINFACT%>' +
                  '。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'XM2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMBYUNIT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      minWidth: '200px',
                      width: 'auto'
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDSJ',
                    textValue: '',
                    textType: 'date',
                    customField: true,
                    dateFormat: 'YYYY年MM月DD日HH时',
                    showTime: 'HH',
                    elementStyle: { width: '230px' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'OLDBRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ASSISTANTUNITINFACT',
                    textValue: '',
                    textType: 'rangedict',
                    dictConfig: {
                      type: 'dynamic',
                      params: {
                        configId: 'ssjycs',
                        code: ''
                      },
                      mixinModel: [
                        { label: '本市局羁押场所', defaultActive: true, type: 'dict', scope: dept },
                        { label: '全区羁押场所', defaultActive: false, type: 'dict', scope: '' }
                      ]
                    },
                    elementStyle: { fontSize: '16pt' }
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {breakInside: "avoid"},
            contents: [
              {
                key: uuidv4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '10px',
                  right: '50px'
                }
              },
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: `<%gafj_1%>\n<%writetimenyr%>`,
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  marginTop: '48px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'gafj_1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'writetimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '注：看守所地址: <%NOTE%> ',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  paddingLeft: '22px',
                  marginTop: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'NOTE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
