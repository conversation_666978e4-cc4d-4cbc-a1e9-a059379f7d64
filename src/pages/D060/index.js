import React, { Component } from 'react';
import { connect } from 'dva';
import Seal from '../../compoments/seal/Seal';
import { countFontSize, drawLine, digitUppercase } from '../utils/func';
import commonStyles from '../lesses/index.less';
import styles from './index.less';

class Index extends Component {
  constructor() {
    super();
    this.state = {
      rowNum: 14
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'list/queryList',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    }).then(() => {
      const { rowNum } = this.state;
      const { list } = this.props;
      const { wpclqdxmList = [] } = list.listData;
      const y = (rowNum - wpclqdxmList.length) * 50;
      drawLine(0, 0, 664, y, 'lineCanvas');
      drawLine(0, 0, 185, 50, 'wptz');
      drawLine(0, 0, 112, 50, 'wply');
      drawLine(0, 0, 112, 50, 'clqk');
    });
  }
  buildBlankBox = () => {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [] } = listData;
    const blankBox = [];
    for (let i = 0; i < rowNum - wpclqdxmList.length; i++) {  // eslint-disable-line
      blankBox.push(
        <tr>
          <td />
          <td />
          <td />
          <td />
          <td />
          <td />
        </tr>
      );
    }
    return blankBox;
  };
  render() {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { wpclqdxmList = [], gamc = '', spdwyz = '' } = listData;
    const canvasHeight = (rowNum - wpclqdxmList.length) * 50;
    const listView = listData.systemid ? (
      <div className={commonStyles.normal} style={{ position: 'relative' }}>
        <Seal imageBase64={spdwyz} style={{ left: 10, top: -30 }} />
        <div className={commonStyles.wsTitle} style={{ margin: '0px auto' }}>
          {gamc}处理物品、文件清单
        </div>
        <table className={commonStyles.table}>
          <tbody>
            <tr>
              <th style={{ width: '7%' }}>编号</th>
              <th style={{ width: '21%' }}>名称</th>
              <th style={{ width: '10%' }}>数量</th>
              <th style={{ width: '28%' }}>特征</th>
              <th style={{ width: '17%' }}>来源</th>
              <th style={{ width: '17%' }}>处理情况</th>
            </tr>
            {wpclqdxmList.map((item, index) => (
              <tr>
                <td>{index + 1}</td>
                <td style={{ fontSize: countFontSize(item.wpmc, 140, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wpmc || ''}
                </td>
                <td
                  style={{
                    fontSize: countFontSize(digitUppercase(item.wpsl || '', item.jldw || ''), 67, 50, 19, 'px'),
                    lineHeight: '1'
                  }}
                >
                  {digitUppercase(item.wpsl || '', item.jldw || '')}
                </td>
                <td style={{ fontSize: countFontSize(item.wptz, 185, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wptz || <canvas className="wptz" width={185} height={50} />}
                </td>
                <td style={{ fontSize: countFontSize(item.wply, 112, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.wply || <canvas className="wply" width={112} height={50} />}
                </td>
                <td style={{ fontSize: countFontSize(item.clqk, 112, 50, 19, 'px'), lineHeight: '1' }}>
                  {item.clqk || <canvas className="clqk" width={112} height={50} />}
                </td>
              </tr>
            ))}
            {this.buildBlankBox()}
            <tr>
              <td colSpan={6} className={styles.footerTd}>
                <div className={styles.footerDiv}>
                  <div style={{ height: '100px' }}>办案单位</div>
                </div>
                <div className={styles.footerDiv}>
                  <div style={{ height: '100px' }}>办 案 人</div>
                  <div style={{ textAlign: 'right', marginRight: '20px', height: '32px' }}>
                    <div className={styles.footerInput} />年
                    <div className={styles.footerInput} />月
                    <div className={styles.footerInput} />日
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
          <canvas
            className="lineCanvas"
            width={664}
            height={canvasHeight}
            style={{ position: 'absolute', bottom: 132 }}
          />
        </table>
        <div className={commonStyles.footer}>本清单附卷。</div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ list }) => ({
  list
}))(Index);
