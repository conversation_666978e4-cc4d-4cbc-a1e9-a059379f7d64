import React, { Component } from 'react';
import { connect } from 'dva';
import Seal from '../../compoments/seal/Seal';
import { countFontSize, drawLine } from '../list/utils/func';
import styles from './index.less';

class Index extends Component {
  constructor() {
    super();
    this.state = {
      rowNum: 22
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'list/queryList',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    })
      .then(() => {
        const { rowNum } = this.state;
        const { list } = this.props;
        const { zjckcxqdList = [] } = list.listData;
        const y = zjckcxqdList.length % 22 ? (rowNum - zjckcxqdList.length % 22) * 40 : 0;
        return y;
      })
      .then((y) => {
        drawLine(0, 0, 664, y, 'lineCanvas');
      });
  }
  buildBlankBox = (pageLists) => {
    const { rowNum } = this.state;
    const blankBox = [];
    for (let i = 0; i < rowNum - pageLists.length; i++) {    // eslint-disable-line
      blankBox.push(
        <tr>
          <td colSpan={2} />
          <td colSpan={4} />
          <td colSpan={8} />
          <td colSpan={6} />
        </tr>
      );
    }
    return blankBox;
  };
  render() {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { zjckcxqdList = [] } = listData;
    let title = '';
    if (listData.ywlxcode) {
      if (listData.ywlxcode === '1') {
        title = '查询清单';
      } else if (listData.ywlxcode === '2') {
        title = '动态查询清单';
      } else if (listData.ywlxcode === '3') {
        title = '继续动态查询清单';
      } else if (listData.ywlxcode === '4') {
        title = '解除动态查询清单';
      } else if (listData.ywlxcode === '5') {
        title = '冻结清单';
      } else if (listData.ywlxcode === '6') {
        title = '继续冻结清单';
      } else if (listData.ywlxcode === '7') {
        title = '解除冻结清单';
      } else if (listData.ywlxcode === '8') {
        title = '紧急止付清单';
      } else {
        title = '解除止付清单';
      }
    }
    const perPageLists = [];
    const pageNum = zjckcxqdList.length % 22 === 0 ? zjckcxqdList.length / 22 : parseInt(zjckcxqdList.length / 22) + 1; // eslint-disable-line
    for (let i = 0; i < pageNum; i++) { //eslint-disable-line
      perPageLists.push(zjckcxqdList.slice(i * 22, (i + 1) * 22));
    }
    const lastPageNotBlankRow = perPageLists.length > 0 ? perPageLists[pageNum - 1].length : 0;
    const canvasHeight = (rowNum - lastPageNotBlankRow) * 40;
    const listView = listData.systemid ? (
      perPageLists.map((pageLists, pageIndex) => (
        <div className={styles.normal}>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.wsTitle} style={{ display: 'inline-block' }}>
              {title}
            </div>
            <Seal imageBase64={listData.spdwyz} style={{ right: 10, top: 10 }} />
          </div>
          <table className={styles.table} style={{ fontSize: '12pt' }}>
            <tbody>
              <tr style={{ height: '30px', lineHeight: '1' }}>
                <th colSpan={2} style={{ width: '10%' }}>
                  序号
                </th>
                <th colSpan={4} style={{ width: '20%' }}>
                  姓名/单位名称
                </th>
                <th colSpan={8} style={{ width: '40%' }}>
                  调查目标 （身份证号、组织机构代码或账户、卡号）{' '}
                </th>
                <th colSpan={6} style={{ width: '30%' }}>
                  所查银行
                </th>
              </tr>
              {pageLists.map((item, index) => (
                <tr>
                  <td colSpan={2}>{pageIndex * this.state.rowNum + index + 1}</td>
                  <td colSpan={4} style={{ fontSize: countFontSize(item.ztmc, 99, 40, 19, 'px'), lineHeight: '1' }}>
                    {item.ztmc || ''}
                  </td>
                  <td
                    colSpan={8}
                    style={{
                      fontSize: countFontSize(item.dcmb, 265, 40, 19, 'px'),
                      lineHeight: '1',
                      textAlign: 'left'
                    }}
                  >
                    {item.dcmb}
                  </td>
                  <td colSpan={6} style={{ fontSize: countFontSize(item.yhmc, 68, 40, 19, 'px'), lineHeight: '1' }}>
                    {item.yhmc}
                  </td>
                </tr>
              ))}
              {this.buildBlankBox(pageLists)}
            </tbody>
            {pageNum === pageIndex + 1 && (
              <canvas
                className="lineCanvas"
                width={664}
                height={canvasHeight}
                style={{ position: 'absolute', bottom: 2 }}
              />
            )}
          </table>
        </div>
      ))
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ list }) => ({
  list
}))(Index);
