import { genQueryList } from '../services/list';

export default {
  namespace: 'list',
  state: {
    listData: {},
    formConfig: []
  },
  effects: {
    *queryList({ payload }, { call, put }) {
      const res = yield call(genQueryList, payload);
      yield put({
        type: 'queryListEnd',
        payload: res
      });
    }
  },
  reducers: {
    queryListEnd(state, { payload }) {
      return {
        ...state,
        listData: payload
      };
    }
  }
};
