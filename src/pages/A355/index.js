import React, { Component } from 'react';
import { connect } from 'dva';
import { Icon } from 'antd';
import moment from 'moment';
import Seal from '../../compoments/seal/Seal';
import Sign from '../../compoments/seal/Sign';
import styles from './index.less';

@connect(({ bgs }) => bgs)
class Index extends Component {
  state = {
    fontSize: 18 // 14pt等于18px
  };
  componentWillMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  componentDidUpdate() {
    this.countJyaqSizeFont();
  }
  countJyaqSizeFont = () => {
    const jyaqTdHeight = document.getElementById('jyaq') ? document.getElementById('jyaq').clientHeight : '199';
    if (jyaqTdHeight > 199) {
      this.setState(
        {
          fontSize: this.state.fontSize - 1
        },
        () => {
          this.countJyaqSizeFont();
        }
      );
    }
  };
  render() {
    const { bgsData } = this.props;
    const { fontSize } = this.state;
    const {
      cqdwyz = '',
      dwldqm = '',
      dwldxm = '',
      spdwyz = '',
      sqldqm = ''
    } = bgsData;
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div className={styles.wsTitle} style={{ fontSize: '16pt' }}>
          广西公安机关办理跨区域涉众型经济犯罪案件审批(报备)表
        </div>
        <table className={styles.table}>
          <tbody style={{fontSize:'16px'}}>
            <tr>
              <td colSpan={2} className={styles.lable}>
                案件编号
              </td>
              <td colSpan={5} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.ajbh || ''}
              </td>
              <td colSpan={2} className={styles.lable} >
                案件名称
              </td>
              <td colSpan={5} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.ajmc || ''}
              </td>
              <td colSpan={2} className={styles.lable}>
                受理单位
              </td>
              <td colSpan={4} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.receiveunit_cn || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={2} className={styles.lable}>
                受理时间
              </td>
              <td colSpan={5} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.receivetime || ''}
              </td>
              <td colSpan={2} className={styles.lable}>
                立案单位
              </td>
              <td colSpan={5} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.confirmbyunit_cn || ''}
              </td>
              <td colSpan={2} className={styles.lable}>
                立案时间
              </td>
              <td colSpan={4} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.confirmtime || ''}
              </td>
            </tr>
            <tr>
              <td colSpan={5} className={styles.lable}>
                跨区域范围
                <br />
                (跨市、跨省)
              </td>
              <td colSpan={8} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.zxddmc || ''}
              </td>
              <td colSpan={2} className={styles.lable}>
                案件涉及地
              </td>
              <td colSpan={5} className={styles.value} style={{ fontSize: '10pt' }}>
                {bgsData.zxddxz || ''}
              </td>
            </tr>
            <tr style={{height:'110px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={1}>
                简要案情
              </td>
              <td colSpan={17} style={{ padding: '8px', borderBottom: 0 }}>
                {bgsData.analysiscase || ''}
              </td>
            </tr>
            <tr style={{height:'110px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={1}>
                申报事项(立案侦查、采取强制措施、涉案资产处置)
              </td>
              <td colSpan={17} style={{ padding: '8px', borderBottom: 0 }}>
                {bgsData.submitnote || ''}
              </td>
            </tr>
            <tr style={{height:'110px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={1}>
                申请事项的主要内容
              </td>
              <td colSpan={17} style={{ padding: '8px', borderBottom: 0 }}>
                {bgsData.detailreason || ''}
              </td>
            </tr>
            <tr style={{height:'110px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={1}>
                备注
              </td>
              <td colSpan={17} style={{ padding: '8px', borderBottom: 0 }}>
                {bgsData.note || ''}
              </td>
            </tr>
            <tr style={{height:'55px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={2}>
                承办单位意见
              </td>
              <td colSpan={12} style={{ padding: '8px', borderBottom: 0,borderRight:'none',textAlign:'left' }}>
                {bgsData.ldps2 || ''}
              </td>
              <td colSpan={5} style={{ padding: '8px', border: 0 }}>
                {cqdwyz ? <Sign imageBase64={cqdwyz} style={{ left: 10, bottom: 0 }} /> : <span>{cqdwyz}</span>}
              </td>
            </tr>
            <tr>
              <td colSpan={6} className={styles.lable} style={{ border: 0,textAlign:'left' }}>
                负责人：
              </td>
              <td colSpan={6} style={{ border: 0, position: 'relative' }}>
                {dwldqm ? <Sign imageBase64={dwldqm} style={{ left: 10, bottom: 0 }} /> : <span>{dwldxm}</span>}
              </td>
              <td colSpan={5} style={{ border: 0 }}>
                {bgsData.ldpssj2}
              </td>
            </tr>
            <tr style={{height:'55px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={2}>
                审核部门审核意见
              </td>
              <td colSpan={12} style={{ padding: '8px', borderBottom: 0,borderRight:'none',textAlign:'left' }}>
                {bgsData.ldps2 || ''}
              </td>
              <td colSpan={5} style={{ padding: '8px', border: 0 ,borderTop:'1px solid #000'}}>
                {dwldqm ? <Sign imageBase64={dwldqm} style={{ left: 10, bottom: 0 }} /> : <span>{dwldxm}</span>}
              </td>
            </tr>
            <tr>
              <td colSpan={6} className={styles.lable} style={{ border: 0,textAlign:'left' }}>
                负责人：
              </td>
              <td colSpan={6} style={{ border: 0, position: 'relative' }}>
                {dwldqm ? <Sign imageBase64={dwldqm} style={{ left: 10, bottom: 0 }} /> : <span>{dwldxm}</span>}
              </td>
              <td colSpan={5} style={{ border: 0 }}>
                {bgsData.ldpssj2}
              </td>
            </tr>
            <tr style={{height:'55px'}}>
              <td colSpan={3} className={styles.lable} rowSpan={2}>
                领导审批意见
              </td>
              <td colSpan={12} style={{ padding: '8px', borderBottom: 0,borderRight:'none',textAlign:'left' }}>
                {bgsData.ldps1 || ''}
              </td>
              <td colSpan={5} style={{ padding: '8px', border: 0 ,borderTop:'1px solid #000'}}>
                {spdwyz ? <Sign imageBase64={spdwyz} style={{ left: 10, bottom: 0 }} /> : <span>{spdwyz}</span>}
              </td>
            </tr>
            <tr>
              <td colSpan={6} className={styles.lable} style={{ border: 0,textAlign:'left' }}>
                领导：
              </td>
              <td colSpan={6} style={{ border: 0, position: 'relative' }}>
                {sqldqm ? <Sign imageBase64={sqldqm} style={{ left: 10, bottom: 0 }} /> : <span>{sqldqm}</span>}
              </td>
              <td colSpan={5} style={{ border: 0 }}>
                {bgsData.ldpssj1}
              </td>
            </tr>
          </tbody>
        </table>
        {/*<div className={styles.footer}>一式两份，一份留存，一份附卷。</div>*/}
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
