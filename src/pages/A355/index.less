.normal {
  margin: 0 auto;
  width: 664px;
  font-size: 16pt;
  .wsTitle {
    height: 64px;
    font-size: 22pt;
    font-family: FZXiaoBiaoSong-B05S, serif;
    text-align: center;
    line-height: 64px;
  }
  .table {
    border: 1px solid #000000;
    font-size: 16pt;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    line-height: 1;
    .lable {
      text-align: center;
    }
    .value {
      font-size: 14pt;
    }
    .input {
      display: inline-block;
      border-bottom: 1px solid;
      font-size: 14pt;
    }
    tr {
      height: 44px;
    }
    td {
      border: 1px solid #000;
    }
  }

  .header,
  .footer {
    font-size: 16pt;
    font-family: <PERSON><PERSON><PERSON><PERSON>g, <PERSON>Song, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
  }

  .header {
    position: relative;
    height: 52pt;
    line-height: 1.2;

    .tt {
      float: left;
      font-size: 14pt;
      max-width: 50%;
      position: absolute;
      bottom: 0;
    }

    .zh {
      float: right;
      font-size: 14pt;
      max-width: 50%;
      text-align: right;
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
}
