.normal {
  font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 华文仿宋, 仿宋, serif;
  width: 664px;
  height: 993px;
  page-break-after: always;
  page-break-before: always;
  margin: 0 auto;
}

.wsTtile {
  text-align: center;
  font-size: 18pt;
  font-family: 黑体, <PERSON><PERSON>, serif;
  width: 100%;
}
.bi<PERSON><PERSON> {
  font-family: 宋体, <PERSON><PERSON>, serif;
  text-align: right;
  margin-right: 16px;
  font-size: 12pt;
}

.pageContainer {
  font-family: ST<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, 华文仿宋, 仿宋, serif;
  font-size: 16pt;
  width: 664px;
  height: 993px;
  page-break-after: always;
  page-break-before: always;
  margin: 0 auto;
}
.wsContext {
  position: relative;
  width: 100%;
  float: right;
}
.borderComponent {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
}
.doubleBorderComponent {
  height: 953px;
  border: 3px solid #000;
  .minBorder {
    margin: 5px;
    height: 938px;
    border: 1px solid #000;
  }
}
.singleBorderComponent {
  height: 993px;
  border: 2px solid #000;
  display: none;
}

.footText {
  height: 40px;
  line-height: 40px;
  font-size: 12pt;
}

.wsBodyContent {
  padding: 8px;
  position: relative;
  margin-top: 32px;
  height: 260px;
  border-bottom: 2px dashed #bbbbbb;
}
.leftItem {
  position: absolute;
  left: 16px;
  top: 16px;
  font-size: 12pt;
}
.rightItem {
  position: absolute;
  left: 330px;
  top: 16px;
  font-size: 12pt;
}

.date {
  top: 200px;
  width: 100%;
  text-align: right;
  position: absolute;
  font-size: 12pt;
}
.wsconent2 {
  margin-top: 16px;
  font-size: 12pt;
  line-height: 24pt;
}
.signName {
  margin-top: 64px;
  position: relative;
}
.dw {
  font-size: 12pt;
  margin-top: 24px;
}
