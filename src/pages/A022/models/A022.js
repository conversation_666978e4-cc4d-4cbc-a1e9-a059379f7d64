import * as nodePrintSerive from '../services/A022';

export default {
  namespace: 'A022',
  state: {
    formData: {},
    formConfig: []
  },

  reducers: {
    save(state, { payload: { formData, formConfig } }) {
      return { ...state, formData, formConfig };
    }
  },
  effects: {
    *fetch({ payload }, { call, put, all }) {
      const result = yield all([call(nodePrintSerive.fetchData, payload), call(nodePrintSerive.fetchConfig, payload)]);
      yield put({
        type: 'save',
        payload: {
          formData: result[0],
          formConfig: result[1]
        }
      });
    },
    *createData({ payload }, { call, put, all }) {
      // eslint-disable-next-line no-unused-vars
      return yield call(nodePrintSerive.createData, payload);
    },
    *updateData({ payload }, { call, put, all }) {
      // eslint-disable-next-line no-unused-vars
      const result = yield call(nodePrintSerive.updateData, payload);
      yield put({
        type: 'fetch',
        payload
      });
    },
    *getinitFunc({ payload }, { call, put, all }) {
      // eslint-disable-next-line no-unused-vars
      const result = yield all([
        call(nodePrintSerive.getinitFunc, payload),
        call(nodePrintSerive.fetchConfig, payload)
      ]);

      yield put({
        type: 'save',
        payload: {
          formData: {
            ajbh: result[0].ajbh,
            ajmc: result[0].ajmc,
            transactprimarytout: result[0].transactprimarytout,
            byname: result[0].byname,
            dw2: result[0].dw2,
            xb2: result[0].xb2,
            xxzz: result[0].xxzz,
            lasj1: result[0].lasj1,
            lasj2: result[0].lasj2,
            lasj3: result[0].lasj3,
            tosomebody: result[0].tosomebody,
            receiveunit: result[0].receiveunit,
            badwlxdh: result[0].badwlxdh,
            bh: result[0].bh
          },
          formConfig: result[1]
        }
      });
    }
  }
};
