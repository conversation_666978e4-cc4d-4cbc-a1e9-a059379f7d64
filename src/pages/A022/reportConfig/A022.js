import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'filed', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '',
      elementStyle: {
        width: '750px'
      },
      paragraph: [
        {
          key: uuidv4(),
          editable: true,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '警民联系函',
              textName: '',
              elementStyle: {
                fontFamily: '黑体,Heiti,serif',
                width: '589.3628px',
                position: 'absolute',
                top: '412.0202px',
                left: '0.00039999999999906777px',
                height: '45.3356px',
                textAlign: 'center',
                fontSize: '25pt',
                lineHeight: '45.3356px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: 'Static text',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '0px',
                position: 'absolute',
                top: '578.6952px',
                left: '9.3342px',
                height: '0px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '0px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '刑事案件\n立案联系函（存根）',
              textName: '',
              elementStyle: {
                fontFamily: '黑体,Heiti,serif',
                width: '589.3628px',
                position: 'absolute',
                top: '81.33699999999999px',
                left: '37.3356px',
                height: '80.00399999999999px',
                textAlign: 'center',
                fontSize: '18pt',
                lineHeight: '80.00399999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '年',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '365.352px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '月',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '436.0222px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '日',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '506.6924px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '年',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '349.3504px',
                left: '441.3558px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '月',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '349.3504px',
                left: '512.026px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '日',
              textName: '',
              elementStyle: {
                fontFamily: '新宋体,Heiti,serif',
                width: '22.6678px',
                position: 'absolute',
                top: '349.3504px',
                left: '582.6962px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '办案民警：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '80.00399999999999px',
                position: 'absolute',
                top: '168.00799999999998px',
                left: '352.018px',
                height: '30.6682px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '邮寄送达凭证：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '112.00559999999999px',
                position: 'absolute',
                top: '288.014px',
                left: '352.018px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '案件名称：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '80.00399999999999px',
                position: 'absolute',
                top: '168.00799999999998px',
                left: '-8px',
                height: '30.6682px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '办案民警：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '80.00399999999999px',
                position: 'absolute',
                top: '790.7058px',
                left: '-8px',
                height: '30.6682px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '监督部门：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '80.00399999999999px',
                position: 'absolute',
                top: '848.0419999999999px',
                left: '-8px',
                height: '30.6682px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '被害人（单位）：',
              textName: '',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '128.00639999999999px',
                position: 'absolute',
                top: '226.67759999999998px',
                left: '-8px',
                height: '30.6682px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName:
                '"    您好！"+(ajmc!=null?$f{ajmc}:"        ")+" 一案，我局已于"+($f{lasj1}!=null?$f{lasj1}:"    ")+"年"+($f{lasj2}!=null?$f{lasj2}:"    ")+"月"+($f{lasj3}!=null?$f{lasj3}:"    ")+"日立案侦查。我们将按照法律的有关规定积极开展工作，多方面收集相关证据，尽快查清案件事实，依法进行处理。"+"\\n"+"    如果您有新的线索、证据或对我们的工作有好的意见和建议，请及时与办案民警联系沟通；如果您发现办案民警有违法违纪问题，可以向监督部门反映。"',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '601.3634px',
                position: 'absolute',
                top: '514.692px',
                left: '-8px',
                height: '254.6794px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '254.6794px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(byname!=null?$f{byname}:"")+"："',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '576.0287999999999px',
                position: 'absolute',
                top: '458.68919999999997px',
                left: '-8px',
                height: '37.3352px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '37.3352px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(ajmc!=null?$f{ajmc}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '272.0136px',
                position: 'absolute',
                top: '168.00799999999998px',
                left: '72.00399999999999px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(transactprimarytout!=null?$f{transactprimarytout}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '256.01279999999997px',
                position: 'absolute',
                top: '168.00799999999998px',
                left: '432.022px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(byname!=null?$f{byname}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '224.01119999999997px',
                position: 'absolute',
                top: '226.67759999999998px',
                left: '120.00639999999999px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '"送达方式："+(dw2!=null?$f{dw2}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '336.0168px',
                position: 'absolute',
                top: '226.67759999999998px',
                left: '352.018px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '"编号："+(bh!=null?$f{bh}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '213.344px',
                position: 'absolute',
                top: '40.001599999999996px',
                left: '468.0238px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(xxzz!=null?$f{xxzz}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '224.01119999999997px',
                position: 'absolute',
                top: '288.014px',
                left: '464.0236px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '"被害人联系方式："+(xb2!=null?$f{xb2}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '352.01759999999996px',
                position: 'absolute',
                top: '288.014px',
                left: '-8px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(transactprimarytout!=null?$f{transactprimarytout}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '272.0136px',
                position: 'absolute',
                top: '790.7058px',
                left: '72.00399999999999px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '"联系电话："+(tosomebody!=null?$f{tosomebody}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '336.0168px',
                position: 'absolute',
                top: '790.7058px',
                left: '352.018px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(receiveunit!=null?$f{receiveunit}:"")+"警务督察大队"',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '272.0136px',
                position: 'absolute',
                top: '848.0419999999999px',
                left: '72.00399999999999px',
                height: '56.00279999999999px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '56.00279999999999px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '"监督电话："+(badwlxdh!=null?$f{badwlxdh}:"")',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '336.0168px',
                position: 'absolute',
                top: '848.0419999999999px',
                left: '352.018px',
                height: '30.6682px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '30.6682px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'transactunit!=null?$f{transactunit}:""',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '400.02px',
                position: 'absolute',
                top: '908.045px',
                left: '286.6814px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime1',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '66.67px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '300.0154px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime2',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '50.6692px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '386.6864px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime3',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '50.6692px',
                position: 'absolute',
                top: '956.0473999999999px',
                left: '457.35659999999996px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime1',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '66.67px',
                position: 'absolute',
                top: '349.3504px',
                left: '376.01919999999996px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime2',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '50.6692px',
                position: 'absolute',
                top: '349.3504px',
                left: '462.69019999999995px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writetime3',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '50.6692px',
                position: 'absolute',
                top: '349.3504px',
                left: '533.3603999999999px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '29.334799999999998px'
              }
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
