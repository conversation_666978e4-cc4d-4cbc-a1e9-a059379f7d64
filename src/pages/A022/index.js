import React, { Component } from 'react';
import { connect } from 'dva';
import { Form, message } from 'antd';
import { config } from '@/common/config';
import Toolbar from '@/compoments/wenshu/ToolbarQM';
import WsInput from '@/compoments/wenshu/WsInput';
import { fieldConfigCheck } from '@/compoments/wenshu/utils/CommonUtil';
import styles from './index.less';

const SSWSNAME = '刑事案件立案联系函';
const FILENAME = 'A022';
const WSCODE = 'A022';

@connect(({ A022 }) => A022)
@Form.create({
  mapPropsToFields(props) {
    const formsvalue = {};
    const { formData } = props;
    const keys = Object.keys(formData);
    for (const key of keys) {
      formsvalue[key] = Form.createFormField({
        value: formData[key]
      });
    }
    return formsvalue;
  }
})
class Index extends Component {
  state = {
    scale: 100
  };
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type, ajbh } = query;
    if (wskey) {
      this.props.dispatch({
        type: 'A022/fetch',
        payload: {
          systemid: wskey,
          wsmc: SSWSNAME,
          token,
          filename: FILENAME,
          type
        }
      });
    } else {
      this.props.dispatch({
        type: 'A022/getinitFunc',
        payload: {
          systemid: wskey,
          wsmc: SSWSNAME,
          token,
          wscode: WSCODE,
          ajbh,
          filename: FILENAME,
          type
        }
      });
    }
  }

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };
  handleSubmit = (e) => {
    e.preventDefault();
    console.info('handleSubmit');
    this.props.form.validateFields((err, values) => {
      const { formConfig } = this.props;
      const postFormData = {};
      for (const key of Object.keys(values)) {
        const checkResult = fieldConfigCheck(formConfig, key);
        if (checkResult.isEditable) {
          postFormData[key] = values[key];
        }
      }

      const {
        fileName,
        sswsname,
        location: { query }
      } = this.props;
      const { wskey, token, ajbh, type } = query;

      if (wskey) {
        const formData = { ...postFormData, systemid: wskey };
        this.props
          .dispatch({
            type: `A022/updateData`,
            payload: {
              token,
              formData,
              systemid: wskey,
              filename: fileName,
              wsmc: sswsname
            }
          })
          .then(() => {
            message.info('保存成功');
            window.location.reload();
          });
      } else {
        const { formData: initFormData } = this.props;
        let formData = { ...postFormData, ajbh, wscode: WSCODE, wsmc: SSWSNAME, state: '1' };
        formData = { ...formData, ...initFormData };
        this.props
          .dispatch({
            type: `A022/createData`,
            payload: {
              token,
              formData,
              filename: fileName,
              wsmc: sswsname
            }
          })
          .then((res) => {
            console.info('res', res);
            window.location = `/A022?wskey=${res.systemid}&token=${token}&type=${type}`;
          });
      }
    });
  };
  handlePrintReport = (DSQM) => {
    const {
      location: { query }
    } = this.props;

    const { wskey, token } = query;
    const { wsreportPrintContextPath, wsreportContextPath, qmContextPath, isOpenQM } = config;
    const { fileName, sswsname, printUrl } = this.props;
    if (DSQM && isOpenQM) {
      window.open(
        /* eslint-disable-next-line */
        `${qmContextPath}?url=${wsreportContextPath}/${FILENAME}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}&fileName=${FILENAME}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
        // `http://192.168.1.104:3000/print?url=http://192.168.1.10:8002/A002&type=print&wskey=PCS37201302260000000000184156&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoi5a-S5Lqt5rCR6K2mIiwidXNlcmlkIjoiaHRtaiIsImpoIjoiaHRtajExIiwiZGVwdCI6IjM3MDcwMzU4MDAwMCIsIndvcmtkZXB0IjoiMzcwNzAzNTgwMDAwIiwiZHdtYyI6Iua9jeWdiuW4guWFrOWuieWxgOWvkuS6reWIhuWxgOWkruWtkOa0vuWHuuaJgCIsImlzcyI6IiIsImF1ZCI6IiIsImV4cCI6MTU2ODE3MjUzMX0.axgbBRqBHFNWwSpLvzKvyWp1Y5sTQPR6LwTngnPMQXo`,
      );
    } else {
      window.open(
        `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${FILENAME}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
        '_blank',
        'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
      );
    }
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { formConfig, formData } = this.props;
    const {
      location: { query }
    } = this.props;
    const { type, wskey } = query;
    const keys = Object.keys(formData);
    const checkResult = {};
    for (const key of keys) {
      checkResult[key] = fieldConfigCheck(formConfig, key);
    }

    return (
      <div className={styles.normal}>
        <Form layout="inline" onSubmit={this.handleSubmit} style={{ marginTop: type === 'edit' ? '60px' : 0 }}>
          <Toolbar
            onSliderChange={this.onSliderChange}
            handleScaleReport={this.handleScaleReport}
            handlePrintReport={this.handlePrintReport}
            handlePreviewReport={this.handlePreviewReport}
            scale={this.state.scale}
            style={{ display: type === 'edit' ? 'inherit' : 'none' }}
            {...this.props}
          />
          <div className={styles.bianhao}>
            编号:
            <WsInput pageType="narrative" editable={type === 'edit' && checkResult.bh && checkResult.ajmc.bh} />
          </div>
          <div className={styles.wsTtile}>刑事案件</div>
          <div className={styles.wsTtile}>立案联系函存根</div>
          <div className={styles.wsBodyContent}>
            <div className={styles.leftItem}>
              案件名称:
              {getFieldDecorator('ajmc')(
                <WsInput
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.ajmc && checkResult.ajmc.isEditable}
                />
              )}
            </div>
            <div className={styles.rightItem}>
              办案民警:
              {getFieldDecorator('transactprimarytout')(
                <WsInput
                  pageType="narrative"
                  editable={
                    type === 'edit' && checkResult.transactprimarytout && checkResult.transactprimarytout.isEditable
                  }
                />
              )}
            </div>
            <div className={styles.leftItem} style={{ top: '80px' }}>
              被害人（单位）:
              {getFieldDecorator('byname')(
                <WsInput
                  placeholder="被害人（单位）"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.byname && checkResult.byname.isEditable}
                />
              )}
            </div>
            <div className={styles.rightItem} style={{ top: '80px' }}>
              送达方式:
              {getFieldDecorator('dw2')(
                <WsInput
                  placeholder="送达方式"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.dw2 && checkResult.dw2.isEditable}
                />
              )}
            </div>
            <div className={styles.leftItem} style={{ top: '160px' }}>
              被害人联系方式:
              {getFieldDecorator('xb2')(
                <WsInput
                  placeholder="被害人联系方式"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.xb2 && checkResult.xb2.isEditable}
                />
              )}
            </div>
            <div className={styles.rightItem} style={{ top: '160px' }}>
              邮寄送达凭证:
              {getFieldDecorator('xxzz')(
                <WsInput
                  placeholder="邮寄送达凭证"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.xxzz && checkResult.xxzz.isEditable}
                />
              )}
            </div>
            <div className={styles.date}>
              <span>年</span>
              <span style={{ marginLeft: '64px' }}>月</span>
              <span style={{ marginLeft: '64px' }}>日</span>
            </div>
          </div>
          <div className={styles.border} />
          <div className={styles.wsTtile} style={{ marginTop: '16px' }}>
            警民联系函
          </div>
          <div className={styles.dw}>
            {getFieldDecorator('byname')(
              <WsInput
                placeholder="被害人（单位）"
                pageType="narrative"
                editable={type === 'edit' && checkResult.byname && checkResult.byname.isEditable}
              />
            )}
            :
          </div>
          <div className={styles.wsconent2}>
            &nbsp;&nbsp;&nbsp;&nbsp;您好!一案，我局已于${formData.lasj1 || ''}年${formData.lasj2 || ''}月$
            {formData.lasj3 || ''}
            日立案侦查。我们将按照法律的有关规定积极开展工作，多方面收集相关证据，尽快查清案件事实，依法进行处理。
            <br />
            &nbsp;&nbsp;&nbsp;&nbsp;如果您有新的线索、证据或对我们的工作有好的意见和建议，请及时与办案民警联系沟通；如果您发现办案民警有违法违纪问题，可以向监督部门反映。
          </div>
          <div className={styles.signName}>
            <div className={styles.leftItem}>
              办案民警:
              {getFieldDecorator('transactprimarytout')(
                <WsInput
                  pageType="narrative"
                  editable={
                    type === 'edit' && checkResult.transactprimarytout && checkResult.transactprimarytout.isEditable
                  }
                />
              )}
            </div>
            <div className={styles.rightItem}>
              联系电话:
              {getFieldDecorator('tosomebody')(
                <WsInput
                  placeholder="联系电话"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.tosomebody && checkResult.tosomebody.isEditable}
                />
              )}
            </div>
            <div className={styles.leftItem} style={{ top: '80px' }}>
              监督部门:
              {getFieldDecorator('receiveunit')(
                <WsInput
                  placeholder="监督部门"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.receiveunit && checkResult.receiveunit.isEditable}
                />
              )}{' '}
              警务监察大队
            </div>
            <div className={styles.rightItem} style={{ top: '80px' }}>
              监督电话:
              {getFieldDecorator('badwlxdh')(
                <WsInput
                  placeholder="监督电话"
                  pageType="narrative"
                  editable={type === 'edit' && checkResult.badwlxdh && checkResult.badwlxdh.isEditable}
                />
              )}
            </div>
            <div className={styles.date} style={{ top: '132px' }}>
              <span>年</span>
              <span style={{ marginLeft: '64px' }}>月</span>
              <span style={{ marginLeft: '64px' }}>日</span>
            </div>
          </div>
        </Form>
      </div>
    );
  }
}

export default Index;
