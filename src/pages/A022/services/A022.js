import { io } from 'SinoGear';
import request from '../../../utils/request';
import { config } from '../../../common/config';

export function fetchData({ systemid, wsmc, token, type }) {
  return request(`${config.contextPath}/api/b_asj_ws_news/ssws/${systemid}?sswsname=${wsmc}&type=${type}`, {
    headers: {
      Authorization: token
    }
  });
}

export function createData({ formData, token }) {
  return io.post(`${config.contextPath}/api/b_asj_ws_news`, formData, {
    headers: {
      Authorization: token
    }
  });
  // return request(`${config.contextPath}/api/b_asj_ws_news`, {
  //   headers: {
  //     Authorization: token
  //   },
  //   body: JSON.stringify(formData),
  //   method: 'POST'
  // });
}

export function getinitFunc({ wscode, token, ajbh }) {
  return request(`${config.contextPath}/api/b_asj_ws_news/init?wscode=${wscode}&ajbh=${ajbh}`, {
    headers: {
      Authorization: token
    }
  });
}

export function updateData({ formData, token }) {
  return request(`${config.contextPath}/api/b_asj_wss/patch`, {
    headers: {
      Authorization: token,
      'Content-Type': 'application/sino-patch'
    },
    body: JSON.stringify(formData),
    method: 'PUT'
  });
}

export function fetchConfig({ filename, token }) {
  return request(`${config.contextPath}/api/ws/b_asj_wschangeareas/filename/${filename}`, {
    headers: {
      Authorization: token
    }
  });
}
