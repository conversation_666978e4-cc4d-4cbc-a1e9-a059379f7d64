.normal {
  width: 664px;
  margin: 0 auto;
  font-size: 16pt;

  .wsTitle {
    height: 64px;
    font-size: 22pt;
    font-family: FZXiaoBiaoSong-B05S, serif;
    text-align: center;
    line-height: 64px;
  }

  .cutPage {
    page-break-after: always;
  }

  .table {
    border: 1px solid #000000;
    font-family: <PERSON><PERSON><PERSON><PERSON>g, FangSong, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    width: 100%;

    .lable {
      text-align: center;
    }

    .value {
      border: 0;
    }

    .psyj {
      text-align: left;
      vertical-align: top;
      text-indent: 2em;
      padding: 5px;
    }

    td {
      border: 1px solid #000;
    }
  }

  .content {
    font-family: STFangsong, FangSong, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    line-height: 1.2;
    word-break: break-all;
  }
}

.analysisCaseContent{
  font-size: 16pt;
  font-family: ST<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
  width: 624px;
  overflow:auto;
  white-space: pre-line;
  white-space: pre;
  white-space: pre-wrap;
  margin: 0 auto;
  text-align: justify;
}

.inscribe {
  position: relative;
  padding-top: 50px;
  .inscribeTable {
    font-family: ST<PERSON>angsong, <PERSON><PERSON>ong, 仿宋, 华文仿宋, Heiti, serif;
    line-height: 1.2;
    width: 60%;
    position: absolute;
    right: 0;

    .inscribeLabel {
      width: 110px;
    }

    td {
      vertical-align: top;
      padding: 0;
    }
  }
}
