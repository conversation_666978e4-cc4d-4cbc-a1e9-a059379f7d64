import React, { Component } from 'react';
// import {Form} from 'antd';
import { connect } from 'dva';
import { config } from '@/common/config';
import Toolbar from '../../compoments/wenshu/Toolbar';
import styles from './index.less';

const SSWSNAME = '涉密案件聘请律师申请表';
const FILENAME = 'E019';

// @Form.create()
@connect(({ E019 }) => E019)
class Index extends Component {
  state = {
    scale: 100
  };

  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'E019/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  handlePreviewReport = () => {
    const {
      fileName,
      location: { query }
    } = this.props;

    const { wskey, token } = query;
    const { wsreportContextPath } = config;
    window.open(
      `${wsreportContextPath}/${fileName}?type=preview&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
      '_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
    );
  };

  handlePrintReport = () => {
    const {
      fileName,
      location: { query }
    } = this.props;

    const { wskey, token, type } = query;
    const { wsreportPrintContextPath, wsreportContextPath } = config;
    window.open(
      // eslint-disable-next-line
      `${wsreportPrintContextPath}/print?url=${wsreportContextPath}/${fileName}&type=print&wskey=${wskey}&token=${token}&sswsname=${SSWSNAME}`,
      '_blank',
      'fullscreen=yes,toolbar=no,menubar=no,location=no,scroll=no,status=no,titlebar=no,top=0, left=0'
    );
  };
  handleScaleReport = (type) => {
    if (type === 'plus') {
      this.setState({
        scale: this.state.scale === 100 ? this.state.scale : this.state.scale + 10 // 最大为1000%
      });
    } else {
      this.setState({
        scale: this.state.scale === 20 ? this.state.scale : this.state.scale - 10 // 最小为20%
      });
    }
  };

  onSliderChange = (value) => {
    this.setState({
      scale: value
    });
  };

  render() {
    const { formData, location } = this.props;
    const {
      query: { type }
    } = location;
    return (
      <div className={styles.normal} style={{ marginTop: type === 'edit' ? '60px' : '0px' }}>
        <Toolbar
          style={{ display: type === 'edit' ? 'inherit' : 'none' }}
          onSliderChange={this.onSliderChange}
          handleScaleReport={this.handleScaleReport}
          handlePrintReport={this.handlePrintReport}
          handlePreviewReport={this.handlePreviewReport}
          scale={this.state.scale}
          location={location}
        />
        <div className={styles.wsTitle}>
          <span>涉密案件聘请律师申请表</span>
        </div>
        <table className={styles.table}>
          <tbody>
            <tr className={styles.normalTr}>
              <td>申请人</td>
              <td>{formData.xm2}</td>
              <td>性别</td>
              <td>{formData.xb2}</td>
              <td>年龄</td>
              <td>{formData.age}</td>
              <td>
                与犯罪嫌
                <br />
                疑人关系
              </td>
              <td>{formData.gxlx}</td>
            </tr>
            <tr className={styles.normalTr}>
              <td>住址</td>
              <td colSpan="3">{formData.xxzz}</td>
              <td>单位</td>
              <td colSpan="3">{formData.dw2}</td>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr className={styles.normalTr}>
              {/* <div className={styles.table2}> */}
              {/*  根据《中华人民共和国刑事诉讼法》第九十六条第一款之规定， */}
              {/*  <br /> */}
              {/*  特请{formData.reservation10} */}
              {/*  律师事务所{formData.reservation08}律师为犯罪嫌疑人 */}
              {/*  <br /> */}
              {/*  {formData.zxdxjy}提供法律咨询、代理申诉、控告、申请取保候审。 */}
              {/* </div> */}
              <div className={styles.table2} style={{ textIndent: '2em', textAlign: 'left', padding: '5px' }}>
                {formData.oldbriefreason}
              </div>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr className={styles.normalTr}>
              <td style={{ width: '120px' }}>申请人签名</td>
              <td>{formData.writetime}</td>
            </tr>
          </tbody>
        </table>
        <table className={styles.secTable}>
          <tbody>
            <tr>
              <td rowSpan="3" className={styles.normalTr} style={{ height: '400px', width: '80px' }}>
                侦<br />查<br />机<br />关<br />意<br />见
              </td>
              <td>
                <tr>
                  <div className={styles.table4div}>
                    办案人意见：
                    <div className={styles.table4div2}>{formData.submitnote}</div>
                    <div className={styles.table4div3}>{formData.ldpssj1}</div>
                  </div>
                </tr>
                <tr>
                  <div className={styles.table4div}>
                    办案单位意见：：
                    <div className={styles.table4div2}>{formData.ldps2}</div>
                    <div className={styles.table4div3}>{formData.ldpssj2}</div>
                  </div>
                </tr>
                <tr>
                  <div className={styles.table4div1}>
                    办案人意见：
                    <div className={styles.table4div2}>{formData.ldps1}</div>
                    <div className={styles.table4div3}>{formData.ldpssj3}</div>
                  </div>
                </tr>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }
}

export default Index;
