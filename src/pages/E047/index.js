import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { getDataConfig } from './reportConfig/E047';
import styles from './index.less';

const SSWSNAME = '对保证人罚款/没收保证金复核决定书';
const FILENAME = 'E047';

@connect(({ E047 }) => E047)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'E047/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;

    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig(formData)}
          wsData={formData}
          formConfig={formConfig}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          showPage={page}
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = <WsPrintReport dataConfig={getDataConfig(formData)} wsData={formData} sswsname={SSWSNAME} {...this.props} />;
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
