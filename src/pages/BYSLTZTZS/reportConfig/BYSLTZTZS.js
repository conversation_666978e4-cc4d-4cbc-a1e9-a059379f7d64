import uuidv4 from 'uuid/v4';
import moment from 'moment';

export const getDataConfig = (formData) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '此联附卷',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '不予受理听证通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '潍坊市公安局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  ' <%ZWXM%>：\n' +
                  '   <%DWMC%>因<%AJMC%>一案，提出举行听证要求，本机关经审查认为：<%ZDDDDD%>\n' +
                  '   根据《中华人民共和国行政处罚法》第四十二条之规定，决定不予受理。\n'+
                  '   特此通知。     \n',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'AJMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZXDDMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZDDDDD',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            style: {
              marginTop: '40px',
              breakInside: "avoid"
            },
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '<%submittimenyr%>',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  // top: '540px',
                  left: 0,
                  height: '48px',
                  textAlign: 'right',
                  paddingRight: '22px',
                  fontSize: '16pt',
                  lineHeight: '48px'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'submittimenyr',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '   年  月  日     \n',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                }
              }
            ]
          }
        ]
      },
    ] // 文书联数
  };
};
