import React, { Component } from 'react';
import { connect } from 'dva';
import { Spin } from 'antd';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { getDataConfig } from '../BYSLTZTZS/reportConfig/BYSLTZTZS';
import styles from './index.less';

const SSWSNAME = '不予受理听证通知书';
const FILENAME = 'BYSLTZTZS';

@connect(({ BYSLTZTZS }) => BYSLTZTZS)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type } = query;

    this.props.dispatch({
      type: 'BYSLTZTZS/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;

    let reportView = '';
    if (!formData.systemid) {
      reportView = <Spin spinning />;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig(formData)}
          wsData={formData}
          formConfig={formConfig}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          showPage={page}
          isQM
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={getDataConfig(formData)} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查 {type}</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
