import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'filed', // filed 填充式 ，narrative 叙述式
  borderLines: 1,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      elementStyle: {
        width: '325px'
      },
      paragraph: [
        {
          key: uuidv4(),
          editable: false,

          contents: [
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传  唤  证',
              textName: '',
              elementStyle: {
                fontFamily: '方正小标宋简体,Heiti,serif',
                width: '300.015px',
                position: 'absolute',
                top: '88.00399999999999px',
                left: '2.6671999999999993px',
                height: '42.6688px',
                textAlign: 'center',
                fontSize: '22pt',
                lineHeight: '42.6688px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '案件名称',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '250.6788px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '办 案 人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '693.3675999999999px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '批准时间',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '654.699px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '批 准 人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '610.6967999999999px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传唤原因',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '492.02419999999995px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '单位及职业',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '70.6702px',
                position: 'absolute',
                top: '450.68879999999996px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '案件编号',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '57.3362px',
                position: 'absolute',
                top: '289.3474px',
                left: '9.3342px',
                height: '33.335px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '33.335px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '犯罪嫌疑人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '74.6704px',
                position: 'absolute',
                top: '333.34959999999995px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '300.015px',
                position: 'absolute',
                top: '130.6728px',
                left: '2.6671999999999993px',
                height: '42.6688px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '42.6688px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '办案单位',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '734.703px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '填发时间',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '772.0382px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '填 发 人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '814.707px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '住    址',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '369.35139999999996px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '指定时间',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '530.6927999999999px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '出生日期',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '64.00319999999999px',
                position: 'absolute',
                top: '410.68679999999995px',
                left: '4.000599999999999px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '指定地点',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '61.3364px',
                position: 'absolute',
                top: '570.6948px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '10pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'gafj',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '300.015px',
                position: 'absolute',
                top: '45.3352px',
                left: '2.6671999999999993px',
                height: '34.6684px',
                textAlign: 'center',
                fontSize: '14pt',
                border: 'none',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'wszh',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '273.347px',
                position: 'absolute',
                top: '188.009px',
                left: '32.001999999999995px',
                height: '33.335px',
                border: 'none',
                textAlign: 'right',
                fontSize: '11pt',
                lineHeight: '33.335px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'ajmc',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '242.67839999999998px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'ajbh',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '284.0138px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xzdz_11',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '360.01759999999996px',
                left: '64.00359999999999px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: '(workin!=null?$f{workin}:"")+($f{profession}!=null?$f{profession}:"")',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '229.3448px',
                position: 'absolute',
                top: '441.35499999999996px',
                left: '73.33739999999999px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'oldbriefreason',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '482.69039999999995px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'confirmbyperson',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '605.3632px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'confirmtime',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '646.6985999999999px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'transactprimarytout',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '688.034px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'transactunit1',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '728.036px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'date',
              textValue: '',
              textName: 'writetime',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '768.0379999999999px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'writeby',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '806.7066px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zdddsj7',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '522.6924px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'sex_1',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '60.00299999999999px',
                position: 'absolute',
                top: '322.6824px',
                left: '238.67899999999997px',
                height: '37.3352px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '37.3352px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zwxm',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '152.0076px',
                position: 'absolute',
                top: '326.6826px',
                left: '77.3376px',
                height: '33.335px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '33.335px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'date',
              textValue: '',
              textName: 'birthdayinbegin',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '240.01199999999997px',
                position: 'absolute',
                top: '401.35299999999995px',
                left: '72.00399999999999px',
                height: '37.3352px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '37.3352px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xxdzms',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '238.6786px',
                position: 'absolute',
                top: '565.3611999999999px',
                left: '66.6704px',
                height: '40.001999999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '40.001999999999995px'
              }
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联附卷',
      elementStyle: {
        width: '591px'
      },
      paragraph: [
        {
          key: uuidv4(),
          editable: false,

          contents: [
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '本证已于       年  月  日收到。',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '280.014px',
                position: 'absolute',
                top: '694.7009999999999px',
                left: '25.335px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传 唤 证',
              textName: '',
              elementStyle: {
                fontFamily: '方正小标宋简体,Heiti,serif',
                width: '550.6941999999999px',
                position: 'absolute',
                top: '98.6712px',
                left: '12.000999999999998px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '22pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '根据《中华人民共和国刑事诉讼法》第一百一十七条之规定，兹',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '525.3596px',
                position: 'absolute',
                top: '313.3486px',
                left: '42.6692px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '接受讯问。无正当理由拒不接受传唤的，可以依法拘传。',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '489.3578px',
                position: 'absolute',
                top: '488.02399999999994px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传唤涉嫌',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '93.338px',
                position: 'absolute',
                top: '353.3506px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '罪的犯罪嫌疑人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '136.0068px',
                position: 'absolute',
                top: '352.0172px',
                left: '318.683px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '被传唤人到达时间     年  月  日  时。',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '302.6818px',
                position: 'absolute',
                top: '748.0369999999999px',
                left: '25.335px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传唤结束时间     年  月  日  时。',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '302.6818px',
                position: 'absolute',
                top: '792.0391999999999px',
                left: '25.335px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '被传唤人:',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '96.00479999999999px',
                position: 'absolute',
                top: '720.0355999999999px',
                left: '108.0058px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '被传唤人:',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '89.3378px',
                position: 'absolute',
                top: '768.0379999999999px',
                left: '108.0058px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '被传唤人:',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '89.3378px',
                position: 'absolute',
                top: '813.3735999999999px',
                left: '108.0058px',
                height: '22.6678px',
                textAlign: 'left',
                fontSize: '12pt',
                lineHeight: '22.6678px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '，出生日期',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '116.0058px',
                position: 'absolute',
                top: '400.01959999999997px',
                left: '141.3408px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '住址',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '66.67px',
                position: 'absolute',
                top: '400.01959999999997px',
                left: '428.0218px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '，',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '14.667399999999999px',
                position: 'absolute',
                top: '400.01959999999997px',
                left: '410.6876px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '（性别',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '58.669599999999996px',
                position: 'absolute',
                top: '398.6862px',
                left: '5.334px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '于',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '34.6684px',
                position: 'absolute',
                top: '440.0216px',
                left: '169.3422px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '到',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '34.6684px',
                position: 'absolute',
                top: '444.0218px',
                left: '349.3512px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '（副  本）',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '300.015px',
                position: 'absolute',
                top: '142.6734px',
                left: '140.0074px',
                height: '34.6684px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'gafj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '550.6941999999999px',
                position: 'absolute',
                top: '64.0028px',
                left: '12.000999999999998px',
                height: '34.6684px',
                textAlign: 'center',
                fontSize: '16pt',
                border: 'none',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'wszh',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '494.6914px',
                position: 'absolute',
                top: '177.34179999999998px',
                left: '78.67099999999999px',
                height: '33.335px',
                textAlign: 'right',
                fontSize: '16pt',
                border: 'none',
                lineHeight: '33.335px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zdddsj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '144.00719999999998px',
                position: 'absolute',
                top: '430.6878px',
                left: '204.01059999999998px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xxdzms',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '180.009px',
                position: 'absolute',
                top: '430.6878px',
                left: '382.6862px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'oldbriefreason',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '220.011px',
                position: 'absolute',
                top: '344.0168px',
                left: '98.672px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zwxm',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '108.0054px',
                position: 'absolute',
                top: '344.0168px',
                left: '454.6898px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'date',
              textValue: '',
              textName: 'birthdayinbegin',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '150.67419999999998px',
                position: 'absolute',
                top: '393.3526px',
                left: '256.0132px',
                height: '37.3352px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '37.3352px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'sex',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '74.6704px',
                position: 'absolute',
                top: '390.6858px',
                left: '65.33699999999999px',
                height: '40.001999999999995px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xzdz',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '68.0034px',
                position: 'absolute',
                top: '388.01899999999995px',
                left: '494.69179999999994px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xzdz_1',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '164.0082px',
                position: 'absolute',
                top: '429.3544px',
                left: '5.334px',
                height: '44.002199999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'gafj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '280.014px',
                position: 'absolute',
                top: '588.029px',
                left: '282.6812px',
                height: '34.6684px',
                textAlign: 'right',
                fontSize: '14pt',
                border: 'none',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'date',
              textValue: '',
              textName: 'writetime',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '255px',
                position: 'absolute',
                top: '622.6974px',
                left: '307.3416px',
                height: '34.6684px',
                textAlign: 'right',
                fontSize: '14pt',
                lineHeight: '34.6684px'
              }
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交被传唤人',
      elementStyle: {
        width: '591px'
      },
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          contents: [
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传 唤 证',
              textName: '',
              elementStyle: {
                fontFamily: '方正小标宋简体,Heiti,serif',
                width: '550.6941999999999px',
                position: 'absolute',
                top: '101.338px',
                left: '12.000999999999998px',
                height: '46.669px',
                textAlign: 'center',
                fontSize: '22pt',
                lineHeight: '46.669px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '于',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '34.6684px',
                position: 'absolute',
                top: '437.35479999999995px',
                left: '176.0092px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '，',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '14.667399999999999px',
                position: 'absolute',
                top: '397.3528px',
                left: '417.35459999999995px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '罪的犯罪嫌疑人',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '136.0068px',
                position: 'absolute',
                top: '349.3504px',
                left: '325.34999999999997px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '，出生日期',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '116.0058px',
                position: 'absolute',
                top: '397.3528px',
                left: '148.0078px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '住址',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '66.67px',
                position: 'absolute',
                top: '397.3528px',
                left: '434.68879999999996px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '（性别',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '58.669599999999996px',
                position: 'absolute',
                top: '396.01939999999996px',
                left: '12.000999999999998px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '根据《中华人民共和国刑事诉讼法》第一百一十七条之规定，兹',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '525.3596px',
                position: 'absolute',
                top: '310.6818px',
                left: '49.3362px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '接受讯问。无正当理由拒不接受传唤的，可以依法拘传。',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '489.3578px',
                position: 'absolute',
                top: '485.3572px',
                left: '12.000999999999998px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '到',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '34.6684px',
                position: 'absolute',
                top: '441.35499999999996px',
                left: '356.0182px',
                height: '29.334799999999998px',
                textAlign: 'center',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '传唤涉嫌',
              textName: '',
              elementStyle: {
                fontFamily: '仿宋,Heiti,serif',
                width: '94.67139999999999px',
                position: 'absolute',
                top: '350.68379999999996px',
                left: '12.000999999999998px',
                height: '29.334799999999998px',
                textAlign: 'left',
                fontSize: '14pt',
                lineHeight: '29.334799999999998px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'gafj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '550.6941999999999px',
                position: 'absolute',
                top: '66.6696px',
                left: '12.000999999999998px',
                height: '34.6684px',
                textAlign: 'center',
                fontSize: '16pt',
                border: 'none',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'wszh',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '557.3611999999999px',
                position: 'absolute',
                top: '166.6746px',
                left: '12.000999999999998px',
                height: '33.335px',
                textAlign: 'right',
                fontSize: '16pt',
                border: 'none',
                lineHeight: '33.335px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xzdz',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '68.0034px',
                position: 'absolute',
                top: '385.3522px',
                left: '501.3588px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zwxm',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '108.0054px',
                position: 'absolute',
                top: '341.34999999999997px',
                left: '461.35679999999996px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xzdz_1',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '164.0082px',
                position: 'absolute',
                top: '426.6876px',
                left: '12.000999999999998px',
                height: '44.002199999999995px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'zdddsj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '144.00719999999998px',
                position: 'absolute',
                top: '428.02099999999996px',
                left: '210.67759999999998px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'date',
              textValue: '',
              textName: 'birthdayinbegin',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '150.67419999999998px',
                position: 'absolute',
                top: '390.6858px',
                left: '262.68019999999996px',
                height: '37.3352px',
                textAlign: 'left',
                fontSize: '11pt',
                lineHeight: '37.3352px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'xxdzms',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '180.009px',
                position: 'absolute',
                top: '428.02099999999996px',
                left: '389.35319999999996px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'oldbriefreason',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '220.011px',
                position: 'absolute',
                top: '341.34999999999997px',
                left: '105.339px',
                height: '44.002199999999995px',
                textAlign: 'center',
                fontSize: '11pt',
                lineHeight: '44.002199999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'sex',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '74.6704px',
                position: 'absolute',
                top: '388.01899999999995px',
                left: '72.00399999999999px',
                height: '40.001999999999995px',
                textAlign: 'center',
                fontSize: '12pt',
                lineHeight: '40.001999999999995px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName: 'gafj',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '280.014px',
                position: 'absolute',
                top: '645.3652px',
                left: '272.014px',
                height: '34.6684px',
                textAlign: 'right',
                fontSize: '14pt',
                border: 'none',
                lineHeight: '34.6684px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '',
              textName:
                '(writetime1==null?"    ":$f{writetime1})+"年"+($f{writetime2}==null?"    ":$f{writetime2})+"月"+($f{writetime3}==null?"    ":$f{writetime3})+"日"',
              elementStyle: {
                fontFamily: '宋体,Heiti,serif',
                width: '405.3536px',
                position: 'absolute',
                top: '680.0336px',
                left: '146.6744px',
                height: '34.6684px',
                textAlign: 'right',
                fontSize: '14pt',
                lineHeight: '34.6684px'
              }
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
