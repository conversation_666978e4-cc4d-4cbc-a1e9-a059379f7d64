import * as nodePrintSerive from '../services/E174';

export default {
  namespace: 'E174',
  state: {
    formData: {},
    formConfig: []
  },

  reducers: {
    save(state, { payload: { formData, formConfig } }) {
      return { ...state, formData, formConfig };
    }
  },
  effects: {
    *fetch({ payload }, { call, put, all }) {
      const result = yield all([call(nodePrintSerive.fetchData, payload), call(nodePrintSerive.fetchConfig, payload)]);
      yield put({
        type: 'save',
        payload: {
          formData: result[0],
          formConfig: result[1]
        }
      });
    },
    *updateData({ payload }, { call, put }) {
      // eslint-disable-next-line no-unused-vars
      yield call(nodePrintSerive.updateData, payload);
      yield put({
        type: 'fetch',
        payload
      });
    }
  }
};
