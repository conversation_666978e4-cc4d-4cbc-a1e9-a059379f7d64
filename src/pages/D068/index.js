import React, { Component } from 'react';
import { connect } from 'dva';
import { countFontSize, drawLine } from '../list/utils/func';
import styles from './index.less';
import commonStyles from '@/pages/approvalform/lesses/index.less';

class Index extends Component {
  constructor() {
    super();
    this.state = {
      rowNum: 19
    };
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'list/queryList',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    })
      .then(() => {
        const { rowNum } = this.state;
        const { list } = this.props;
        const { zjckcxqdList = [] } = list.listData;
        const y = (rowNum - zjckcxqdList.length) * 40;
        return y;
      })
      .then((y) => {
        drawLine(0, 0, 664, y, 'lineCanvas');
      });
  }
  buildBlankBox = () => {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { zjckcxqdList = [] } = listData;
    const blankBox = [];
    for (let i = 0; i < rowNum - zjckcxqdList.length; i++) {
      blankBox.push(
        <tr>
          <td colSpan={2} />
          <td colSpan={4} />
          <td colSpan={6} />
          <td colSpan={8} />
        </tr>
      );
    }
    return blankBox;
  };
  render() {
    const { rowNum } = this.state;
    const { list } = this.props;
    const { listData } = list;
    const { zjckcxqdList = [] } = listData;
    const canvasHeight = (rowNum - zjckcxqdList.length) * 40;
    const listView = listData.systemid ? (
      <div className={styles.normal}>
        <div style={{ textAlign: 'center' }}>
          <div className={styles.wsTitle} style={{ display: 'inline-block' }}>
            相 关 账 户 列 表
          </div>
          <div style={{ display: 'inline-block', float: 'right' }}>
            {listData.spdwyz ? (
              <img
                src={listData.spdwyz}
                width={109}
                height={69}
                style={{ position: 'absolute', top: '0', right: '2px' }}
              />
            ) : (
              ''
            )}
          </div>
        </div>
        <table className={styles.table} style={{ fontSize: '12pt' }}>
          <tbody>
            <tr style={{ height: '30px', lineHeight: '1' }}>
              <th colSpan={2} style={{ width: '10%' }}>
                序号
              </th>
              <th colSpan={4} style={{ width: '20%' }}>
                账（卡）号
              </th>
              <th colSpan={6} style={{ width: '30%' }}>
                所属银行
              </th>
              <th colSpan={8} style={{ width: '40%' }}>
                涉案账（卡）号与犯罪活动有何关联
              </th>
            </tr>
            {zjckcxqdList.map((item, index) => (
              <tr>
                <td colSpan={2}>{index + 1}</td>
                <td colSpan={4} style={{ fontSize: countFontSize(item.zh, 99, 40, 19, 'px'), lineHeight: '1' }}>
                  {item.zh || ''}
                </td>
                <td colSpan={6} style={{ fontSize: countFontSize(item.yhmc, 168, 40, 19, 'px'), lineHeight: '1' }}>
                  {item.yhmc}
                </td>
                <td colSpan={8} style={{ fontSize: countFontSize(item.fzhdgl, 68, 40, 19, 'px'), lineHeight: '1' }}>
                  {item.fzhdgl}
                </td>
              </tr>
            ))}
            {this.buildBlankBox()}
          </tbody>
          <canvas
            className="lineCanvas"
            width={664}
            height={canvasHeight}
            style={{ position: 'absolute', bottom: 2 }}
          />
        </table>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ list }) => ({
  list
}))(Index);
