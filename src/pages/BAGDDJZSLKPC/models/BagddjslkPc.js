import * as nodePrintSerive from '../services/BagddjslkPc';
import {getParameters} from "@/common/services/qmb";
import {config} from "@/common/config";

export default {
  namespace: 'BagddjzslkPc',
  state: {
    formData: {}
  },

  reducers: {
    save(state, { payload: { formData } }) {
      return { ...state, formData };
    }
  },
  effects: {
    *fetch({ payload }, { call, put, all }) {
      const r = yield call(getParameters, payload);
      let path = config.jakbPcJakbContextPath;
      if (r && r.value) {
        path = r.value;
      }
      const result = yield call(nodePrintSerive.fetchData, {...payload, path});
      if (result.code === 1){
        yield put({
          type: 'save',
          payload: {
            formData: result.data,
          }
        });
      }
    }
  }
};
