.body{
  //padding: 20px 0 90px;
  .normal {
    width: 664px;
    margin: 0 auto;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 华文仿宋, 仿宋;
    line-height: 1.6;
    font-size: 16pt;
    .wsTitle{
      font-family: "方正小标宋简体",FZXiaoBiaoSong-B05S,serif;
      font-size: 22pt;
      line-height: 2;
      padding: 0 0 20px;
    }
    .item{
      line-height: 1.6;
      padding: 0 0 0 20px;
      position: relative;
      //display: inline-block;
      //display: flex;
      span{
        word-wrap: break-word;
        white-space: pre-wrap
        // text-decoration: underline;
      }
    }
    .blackLine {
      border-bottom:1px solid black ;
      padding: 0 12px;
    }
    .blackLineArr {
      border-bottom:1px solid black ;
      width: 100%;
      height: 25.6pt;
    }
    .linePosition {
      position: absolute;
      width: 100%;
      top: 0;
    }
     .blackLineFirst {
      position: relative;
      border-bottom:1px solid black ;
      margin-left: 32pt;
      height: 25.6pt;
    }
    .blackFirstLine {
      position: absolute;
      width: 32pt;
      height: 25.6pt;
      border-bottom:1px solid #ffffff;
      top: 0;
      z-index: 99;
    }
    .blankBox {
      width: 16px;
      height: 16px;
      border: 1px solid black !important;
      display: inline-block;
    }
    .BoxCheck {
      width: 17px;
      height: 6px;
      border: 2px solid #000 !important;
      border-radius: 1px;
      border-top: none !important;
      border-right: none !important;
      background: transparent;
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
    .imgSeat {
      height: 27px;
      width: 100px;
      display: inline-block;
      vertical-align: sub;
      position: relative;
    }
     .imgPostion {
      max-width: 100px;
      max-height: 120px;
      height: auto;
      vertical-align: sub;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fenge{
    padding: 0 10px;
  }
}
.loading{
  padding-top: 50px;
  font-size: 24px;
  text-align: center;
  color: #40a9ff;
}

.checkboxImg{
  display: inline-block;
  >img{
    vertical-align: text-top;
    width: 20px;
  }
}

:global{

}
