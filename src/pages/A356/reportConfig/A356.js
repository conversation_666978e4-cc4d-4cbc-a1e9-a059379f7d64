import uuidv4 from 'uuid/v4';
// import { getFontSize, renderIMG, getBorderBottom, getLength, getRows, getWsStyle, formatDate } from '@/utils/func';
import uuid4 from 'uuid/v4';
import React from 'react';

export const getDataConfig = (formData, type) => {
  let yzPositionBottom = '115pt'; // 一页极限
  let yzPositionHeight = '60px'; // 可变大这高度，挤到第二联显示
  // const fontSize = getLength(formData?.xxzz || '') > 554 ? '16pt' : '14pt';
  const fontSize = '14pt';
  let fontSize1 = '14pt';
  // if (fontSize1 === '16pt') {
  //   const num = 56; // 16pt时，纯汉字一行28个字，换成字节一行56个字节
  //   const result = getRows(
  //     num,
  //     [
  //       {
  //         key: 'xzcfdx',
  //         str: formData?.xzcfdx,
  //         del: 42
  //       },
  //       {
  //         key: 'detailreason',
  //         str: formData?.detailreason,
  //         del: 46
  //       },
  //       {
  //         key: 'analysisinlaw',
  //         str: formData?.analysisinlaw,
  //         del: 34
  //       },
  //       {
  //         key: 'lawname',
  //         str: formData?.lawname,
  //         del: 48
  //       },
  //       {
  //         key: 'dw2',
  //         str: formData?.dw2,
  //         del: 22
  //       },
  //       {
  //         key: 'zy2',
  //         str: formData?.zy2,
  //         del: 34
  //       },
  //       {
  //         key: 'oldbriefreason_xxdzms', // 合成一段计算
  //         str: formData?.oldbriefreason + formData?.xxdzms,
  //         del: 0
  //       }
  //     ],
  //     formData
  //   );
  //   if (result.total > 15) {
  //     // 超出一页极限
  //     fontSize1 = '14pt';
  //   } else {
  //     yzPositionBottom = '115pt'; //
  //     yzPositionHeight = '60px'; //
  //   }
  // }
  // if (fontSize1 === '14pt') {
  //   const num = 60;
  //   const result = getRows(
  //     num,
  //     [
  //       {
  //         key: 'xzcfdx',
  //         str: formData?.xzcfdx,
  //         del: 48 // 输入框输入+ 固定字体满行时，能输入的字节数
  //       },
  //       {
  //         key: 'detailreason',
  //         str: formData?.detailreason,
  //         del: 52
  //       },
  //       {
  //         key: 'analysisinlaw',
  //         str: formData?.analysisinlaw,
  //         del: 50
  //       },
  //       {
  //         key: 'lawname',
  //         str: formData?.lawname,
  //         del: 56
  //       },
  //       {
  //         key: 'dw2',
  //         str: formData?.dw2,
  //         del: 42
  //       },
  //       {
  //         key: 'zy2',
  //         str: formData?.zy2,
  //         del: 54
  //       },
  //       {
  //         key: 'oldbriefreason_xxdzms', // 合成一段计算
  //         str: formData?.oldbriefreason + formData?.xxdzms,
  //         del: 3
  //       }
  //     ],
  //     formData
  //   );
  //   if (result.total > 16) {
  //     // 超出一页极限
  //     fontSize1 = '12pt';
  //   } else {
  //     yzPositionBottom = '115pt'; //
  //     yzPositionHeight = '60px'; //
  //   }
  // }
  // if (fontSize1 === '12pt') {
  //   const num = 76;
  //   const result = getRows(
  //     num,
  //     [
  //       {
  //         key: 'xzcfdx',
  //         str: formData?.xzcfdx,
  //         del: 60 // 输入框输入+ 固定字体满行时，能输入的字节数
  //       },
  //       {
  //         key: 'detailreason',
  //         str: formData?.detailreason,
  //         del: 62
  //       },
  //       {
  //         key: 'analysisinlaw',
  //         str: formData?.analysisinlaw,
  //         del: 46
  //       },
  //       {
  //         key: 'lawname',
  //         str: formData?.lawname,
  //         del: 64
  //       },
  //       {
  //         key: 'dw2',
  //         str: formData?.dw2,
  //         del: 62
  //       },
  //       {
  //         key: 'zy2',
  //         str: formData?.zy2,
  //         del: 46
  //       },
  //       {
  //         key: 'oldbriefreason_xxdzms', // 合成一段计算
  //         str: formData?.oldbriefreason + formData?.xxdzms,
  //         del: 10
  //       }
  //     ],
  //     formData
  //   );
  //   if (result.total > 16) {
  //     // 超出一页极限
  //     fontSize1 = '10pt';
  //   } else {
  //     yzPositionBottom = '115pt'; //
  //     yzPositionHeight = '60px'; //
  //   }
  // }
  // if (fontSize1 === '10pt') {
  //   const num = 90;
  //   const result = getRows(
  //     num,
  //     [
  //       {
  //         key: 'xzcfdx',
  //         str: formData?.xzcfdx,
  //         del: 70 // 输入框输入+ 固定字体满行时，能输入的字节数
  //       },
  //       {
  //         key: 'detailreason',
  //         str: formData?.detailreason,
  //         del: 74
  //       },
  //       {
  //         key: 'analysisinlaw',
  //         str: formData?.analysisinlaw,
  //         del: 52
  //       },
  //       {
  //         key: 'lawname',
  //         str: formData?.lawname,
  //         del: 78
  //       },
  //       {
  //         key: 'dw2',
  //         str: formData?.dw2,
  //         del: 20
  //       },
  //       {
  //         key: 'zy2',
  //         str: formData?.zy2,
  //         del: 74
  //       },
  //       {
  //         key: 'oldbriefreason_xxdzms', // 合成一段计算
  //         str: formData?.oldbriefreason + formData?.xxdzms,
  //         del: 11
  //       }
  //     ],
  //     formData
  //   );
  //   if (result.total > 17) {
  //     // 超出一页极限， 且到达了最小字体
  //     yzPositionBottom = '0'; //
  //     yzPositionHeight = '190px'; //
  //   }
  // }

  // const wsStyle = getWsStyle(formData?.systemid);
  let lineHeight = 1.4;
  // if (wsStyle) {
  //   fontSize1 = wsStyle?.fontSize;
  //   yzPositionBottom = '0'; // 一页极限是
  //   yzPositionHeight = '60px'; // 可变大这高度，挤到第二联显示
  //   lineHeight = wsStyle?.lineHeight;
  // }
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 1,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        pageStyle: {},
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuidv4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '到 案 经 过',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '53px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '    到案人员（包括个人简历、基本体貌特征、文化程度、职业、户籍所在地、常住地等）：<%TRANSACTCLOG%>\n' +
                  '    到案方式：<%RESERVATION25%>\n' +
                  '    线索来源：<%RESERVATION23%>\n' +
                  '    到案过程（注明到案时是否带伤）：<%RESERVATION15%>\n' +
                  '    涉案物品：<%RESERVATION16%>\n' +
                  '    需要说明的问题：（包括是否具有抗拒抓捕、坦白、立功等从重、从轻情节）：<%RESERVATION14%>\n',
                // '\n'+
                // '                              单位（公章）\n'+
                // '                              出具人（签名）：<%WRITEBY%>\n' +
                // '                              <%SUBMITTIMENYR%> ',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '0 20px 0',
                  lineHeight,
                  left: 0,
                  fontSize: '14pt',
                  breakInside: 'avoid'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'BRIEFREASON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTCLOG',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.transactclog, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION14',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.reservation14, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION15',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.reservation15, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION16',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.reservation16, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION25',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.reservation25, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'RESERVATION23',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.reservation23, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'SUBMITTIMENYR',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE3',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE4',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE5',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE6',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE7',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE8',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CHOSE9',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.briefreason, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.sex, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'BIRTHDAYINBEGIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.birthdayinbegin, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CARDTYPE',
                    textValue: '',
                    textType: 'dict',
                    dictConfig: {
                      // 字典
                      type: 'static', // static静态字典， dynamic 动态
                      kind: 'wg_03' //
                    },
                    elementStyle: {
                      // ...getBorderBottom(formData?.cardtype, type !== 'edit', 6, 100)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      display: type !== 'edit' ? 'inline-block' : 'inline',
                      // ...getBorderBottom(formData?.zjhm, type !== 'edit', 6, 100)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.xzdz, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'ADDRESSOFREGISTER',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.xzdz, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'WORKIN',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.workin, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.frdb, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'XXZZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.xxzz, type !== 'edit', 6, 200),
                      fontSize: fontSize1
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTUNIT1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.transactunit1, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.transactprimarytout, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'CONFIRMBYPERSON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.confirmbyperson, type !== 'edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {
                      borderBottom: 'none'
                    }
                  }
                ]
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              {
                key: uuidv4(),
                type: 'bigText', // 大文本段落
                tempStr: '单位（公章）：<%transactunit1%>\n' + '出具人（签名）：<%WRITEBY%>\n' + '<%SUBMITTIMENYR%> ',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  padding: '20px',
                  lineHeight: 1.8,
                  right: 20,
                  fontSize: '14pt'
                },
                fields: [
                  {
                    key: uuidv4(),
                    textName: 'transactunit1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {borderBottom: 'none'}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITEBY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {
                      // ...getBorderBottom(formData?.writeby, type!=='edit', 6, 200)
                    }
                  },
                  {
                    key: uuidv4(),
                    textName: 'SUBMITTIMENYR',
                    textValue: '',
                    textType: 'string',
                    elementStyle: { borderBottom: 'none' }
                  }
                ]
              },
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  right: '50px',
                  bottom: '-30px'
                }
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
