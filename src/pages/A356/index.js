import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import styles from './index.less';
import { getDataConfig } from '../A356/reportConfig/A356';
import * as httpServer from "../A356/services/A356";
import {config} from "../../common/config";

const SSWSNAME = '呈请制作到案经过报告书(行政)';
const FILENAME = 'A356';

@connect(({ A356 }) => A356)
class Index extends Component {
  state={
    signImg: '',
    substitute: '',
    time: ''
  }
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type,sswsname, systemCode } = query;

    this.props.dispatch({
      type: 'A356/fetch',
      payload: {
        systemid: wskey,
        wsmc: sswsname,
        token,
        filename: FILENAME,
        type
      }
    });
    // 参数传了systemCode才做特殊处理
    if (systemCode) {
      this.props
        .dispatch({
          type: 'qmb/getParameters',
          payload: { systemCode, token }
        })
        .then((r) => {
          let path = config.jakbPcJakbContextPath;
          if (r && r.value) {
            path = r.value;
          }
          httpServer
            .getSignImg({
              token,
              params: {
                ywbbh: wskey,
                fileName: sswsname
              },
              path
            })
            .then((res) => {
              if (res.code === 1) {
                this.setState({
                  signImg: res?.data?.[0]?.signImg,
                  substitute: res?.data?.[0]?.substitute,
                  time: res?.data?.[0]?.time
                });
              }
            });
        });
    }
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page, systemCode }
    } = location;
    let reportView = '';
    formData.transactclog = formData.transactclog ? formData.transactclog.trimStart() : '';
    formData.reservation15 = formData.reservation15 ? formData.reservation15.trimStart() : '';
    // formData.time = this.state.time;
    if (systemCode) {
      formData.time = this.state.time || '              年       月        日';
      formData.signImg = this.state.signImg;
      formData.substitute = this.state.substitute;
    } else {
      formData.time = '              年       月        日';
    }
    if(formData.zy2){
      for(let i=1;i<10;i++){
        formData['chose'+i]=formData.zy2.indexOf(i)>-1?'☑':'□';
      }
    }
    formData.xxzz2 = formData.xxzz ? formData.xxzz.replace('拟依法', '现决定').replace('嫌疑', '行为').replace('不予处罚', '不予行政处罚') : '';
    // formData.spdwyz = require('../../assets/spyz.png');
    // formData.writetimenyr = '2012-04-12'
    // formData.time = '2012-04-12'
    // formData.signImg = require('../../assets/zsls_sign.jpg');
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig(formData, type)}
          wsData={formData}
          formConfig={formConfig}
          showPage={page}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={getDataConfig(formData, type)} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal} key={JSON.stringify(formData)}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
