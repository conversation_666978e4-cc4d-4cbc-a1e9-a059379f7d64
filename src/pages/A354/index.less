.normal {
  margin: 0 auto;
  width: 664px;
  font-size: 16pt;

  .wsTitle {
    height: 64px;
    font-size: 22pt;
    font-family: FZXiaoBiaoSong-B05S, serif;
    text-align: center;
    line-height: 64px;
  }
  .table {
    border: 1px solid #000000;
    font-size: 14pt;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    line-height: 1;
    .lable {
      text-align: center;
      text-align: 14pt;

      &.vertical {
        writing-mode: vertical-lr;
        text-orientation: mixed;
        text-align: center;
        letter-spacing: 1px;

        span {
          display: inline-block;
          writing-mode: vertical-lr;

          white-space: normal;
          word-wrap: break-word;
          padding: 5px 0;
        }
      }
    }
    .value {
      font-size: 14pt;
    }
    .input {
      display: inline-block;
      border-bottom: 1px solid;
      font-size: 14pt;
    }
    tr {
      height: 44px;
    }
    td {
      border: 1px solid #000;
    }
  }

  .header,
  .footer {
    font-size: 16pt;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
  }

  .header {
    position: relative;
    height: 52pt;
    line-height: 1.2;

    .tt {
      float: left;
      font-size: 14pt;
      position: absolute;
      bottom: 0;
    }

    .zh {
      float: right;
      font-size: 14pt;
      max-width: 50%;
      text-align: right;
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
}

.table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;

  td {
    border: 1px solid #000;
    padding: 8px;
    text-align: left;
    font-size: 14px;
    word-break: break-all;
    vertical-align: middle;
  }
}

.lable {
  font-weight: bold;
  text-align: center;

  &.vertical {
    writing-mode: vertical-lr;
    text-align: center;
    padding: 10px 0;
    letter-spacing: 2px;
    white-space: nowrap;
    height: 120px;

    span {
      display: inline-block;
      writing-mode: vertical-lr;
      line-height: 1;
    }
  }
}

.value {
  padding: 8px;
}

.cutPage {
  page-break-after: always;
  height: 20px;
}

.approvalCell {
  position: relative;
  padding: 15px;
  min-height: 120px;
}

.signArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.signName {
  margin-top: 5px;
  font-size: 14px;
}

.dateRow {
  text-align: right;
  padding-right: 100px;
  position: relative;

  span {
    margin: 0 20px;
  }
}

.sealText {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
}

.sealImage {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30px;
  width: 120px;
  height: 120px;
  opacity: 0.8;
}
