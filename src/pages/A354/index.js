import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import Seal from '../../compoments/seal/Seal';
import Sign from '../../compoments/seal/Sign';
import styles from './index.less';

const format = 'YYYY年MM月DD日';

@connect(({ bgs }) => bgs)
class Index extends Component {
  state = {
    isCutPage: false
  };

  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: '数据服务审批表'
      }
    }).then(() => {
      this.setCutPage();
    });
  }

  setCutPage = () => {
    const firstArea = document.getElementById('firstArea');
    const firstAreaHeight = firstArea.clientHeight;
    this.setState({
      isCutPage: firstAreaHeight % 985 > 985
    });
  };

  render() {
    const { isCutPage } = this.state;
    const { bgsData = {} } = this.props;
    const {
      analysiscase = '',
      ldps1 = '',
      ldps2 = '',
      cqdwyz,
      dwldqm,
      spdwyz,
      spldqm,
      ldpssj1 = '',
      ldpssj2 = '',
      sswsmc = ''
    } = bgsData;
    const ldpssj1Format = ldpssj1 ? moment(ldpssj1).format(format) : '';
    const ldpssj2Format = ldpssj2 ? moment(ldpssj2).format(format) : '';

    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div className={styles.wsTitle}>提请公安机关技术侦察部门数据服务审批表</div>
        <div className={styles.header}>
          <div className={styles.tt}>提请单位： {bgsData.transactunit1}</div>
          <div className={styles.zh}>填表日期： {bgsData.submittime}</div>
        </div>
        <div id="firstArea">
          <table className={styles.table}>
            <tbody>
              <tr>
                <td colSpan={3} className={styles.lable}>
                  案（事）件名称
                </td>

                <td colSpan={19} className={styles.value}>
                  {bgsData.ajmc}
                </td>
              </tr>
              <tr>
                <td colSpan={3} className={styles.lable}>
                  案（事）件编号
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.ajbh}
                </td>
                <td colSpan={3} className={styles.lable}>
                  案（事）件类别
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.ajlx}
                </td>
              </tr>
              <tr>
                <td colSpan={3} className={styles.lable}>
                  提请类型
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.frdb}
                </td>
                <td colSpan={3} className={styles.lable}>
                  提请部门
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.transactunit}
                </td>
              </tr>
              <tr>
                <td colSpan={3} className={styles.lable}>
                  提请人
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.transactprimarytout}
                </td>
                <td colSpan={3} className={styles.lable}>
                  联系方式
                </td>
                <td colSpan={8} className={styles.value}>
                  {bgsData.lxdh}
                </td>
              </tr>
              <tr>
                <td
                  style={{ fontSize: '14px', height: '160px' }}
                  colSpan={2}
                  className={`${styles.lable} ${styles.vertical}`}
                >
                  <span>基本情况、必要性及工作目的</span>
                </td>
                <td style={{ fontSize: '14px', height: '160px' }} colSpan={20} className={styles.value}>
                  {bgsData.transactclog}
                </td>
              </tr>
              <tr>
                <td
                  style={{ fontSize: '14px', height: '250px' }}
                  colSpan={2}
                  className={`${styles.lable} ${styles.vertical}`}
                >
                  <span>开展数据指令查询、分析、监测的具体标识条件以及工作要求</span>
                </td>
                <td style={{ fontSize: '14px', height: '250px' }} colSpan={20} className={styles.value}>
                  {bgsData.analysiscaseclog}
                </td>
              </tr>
              {/* <tr>
                <td colSpan={22} className={styles.value} style={{ textAlign: 'right', paddingRight: '50px' }}>
                  共附 {bgsData.fjfs} 页，共 {bgsData.fjsl} 个标识码（大写）
                </td>
              </tr> */}
              <tr>
                <td
                  style={{ fontSize: '14px', height: '200px' }}
                  colSpan={2}
                  className={`${styles.lable} ${styles.vertical}`}
                >
                  <span>提请单位审核意见</span>
                </td>
                <td
                  colSpan={9}
                  className={styles.value}
                  style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}
                >
                  {ldps1}
                  <Sign imageBase64={spldqm} style={{ right: 140, bottom: 0 }} />
                  <Seal imageBase64={spdwyz} style={{ right: 10, bottom: 0 }} />
                  <span style={{ paddingRight: '10px' }}>{ldpssj1Format}</span>
                </td>
                <td colSpan={2} className={`${styles.lable} ${styles.vertical}`}>
                  <span>提请单位负责人审批意见</span>
                </td>
                <td
                  colSpan={9}
                  className={styles.value}
                  style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}
                >
                  {ldps2}
                  <Sign imageBase64={dwldqm} style={{ right: 140, bottom: 0 }} />
                  <Seal imageBase64={cqdwyz} style={{ right: 10, bottom: 0 }} />
                  <span style={{ paddingRight: '10px' }}>{ldpssj2Format}</span>
                </td>
              </tr>
              <tr>
                <td colSpan={2} className={`${styles.lable}`}>
                  <span>备注</span>
                </td>
                <td colSpan={20} className={styles.value}>
                  {bgsData.note}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className={isCutPage ? styles.cutPage : ''} />
        <div className={styles.cutPage} />
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
