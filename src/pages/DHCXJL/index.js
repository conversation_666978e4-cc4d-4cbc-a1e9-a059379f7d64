import React, { Component } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import Seal from '../../compoments/seal/Seal';
import commonStyles from '../lesses/index.less';
import styles from './index.less';

@connect(({ approvalform }) => approvalform)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname: wsname } = query;
    dispatch({
      type: 'approvalform/queryApprovalform',
      payload: {
        wskey,
        token,
        wsname
      }
    });
  }
  render() {
    const { approvalformData } = this.props;
    const approvalformView = approvalformData.systemid ? (
      <div className={commonStyles.normal} style={{ position: 'relative' }}>
        <div className={commonStyles.wsTitle}>电话查询记录</div>
        <table className={commonStyles.table}>
          <tbody>
            <tr>
              <td colSpan={1} rowSpan={4} style={{ width: '5%' }}>
                被<br />查<br />询<br />人<br />自<br />报<br />条<br />件
              </td>
              <td colSpan={3} style={{ width: '15%' }}>
                姓&nbsp;&nbsp;名
              </td>
              <td colSpan={3} style={{ width: '15%' }}>
                {approvalformData.zwxm || ''}
              </td>
              <td colSpan={2} style={{ width: '10%' }}>
                性别
              </td>
              <td colSpan={2} style={{ width: '10%' }}>
                {approvalformData.sex || ''}
              </td>
              <td colSpan={3} style={{ width: '15%' }}>
                出生日期
              </td>
              <td colSpan={6} style={{ width: '30%' }}>
                {approvalformData.birthdayinbegin || ''}
              </td>
            </tr>
            <tr style={{ height: '50px' }}>
              <td colSpan={3} rowSpan={2}>
                常驻户口所在地公安派出所
              </td>
              <td colSpan={3} rowSpan={2}>
                {approvalformData.xxdzms || ''}
              </td>
              <td colSpan={4}>现住址</td>
              <td colSpan={9}>{approvalformData.xzdz || ''}</td>
            </tr>
            <tr style={{ height: '50px' }}>
              <td colSpan={4}>身份证件种类及号码</td>
              <td colSpan={9}>
                {approvalformData.cardtype || ''}
                <br />
                {approvalformData.zjhm || ''}
              </td>
            </tr>
            <tr style={{ height: '80px' }}>
              <td colSpan={3}>违法犯罪记录</td>
              <td colSpan={16} style={{ verticalAlign: 'top' }}>
                {approvalformData.oldbriefreason || ''}
              </td>
            </tr>
            <tr style={{ height: '80px' }}>
              <td colSpan={4}>受话单位</td>
              <td colSpan={7}>{approvalformData.receiveunit || ''}</td>
              <td colSpan={3}>受话电话</td>
              <td colSpan={6}>{approvalformData.lxfs2 || ''}</td>
            </tr>
            <tr style={{ height: '80px' }}>
              <td colSpan={4}>答复人</td>
              <td colSpan={7}>{approvalformData.receiveby || ''}</td>
              <td colSpan={3}>答复人职务</td>
              <td colSpan={6}>{approvalformData.zy2 || ''}</td>
            </tr>
            <tr>
              <td colSpan={20}>查 询 内 容 及 答 复 情 况</td>
            </tr>
            <tr>
              <td colSpan={20} style={{ textAlign: 'left' }}>
                <span>上述地址有无被查询人：</span>
                <span>{approvalformData.frdb || ''}</span>
              </td>
            </tr>
            <tr>
              <td colSpan={20} style={{ textAlign: 'left' }}>
                <span>被查询人出生年月日：</span>
                <span>{approvalformData.csrq2 ? moment(approvalformData.csrq2).format('YYYY年MM月DD日') : ''}</span>
              </td>
            </tr>
            <tr>
              <td colSpan={20} style={{ textAlign: 'left' }}>
                <span>被查询人是否为负案在逃人员：</span>
                <span>{approvalformData.account || ''}</span>
              </td>
            </tr>
            <tr>
              <td colSpan={20} style={{ textAlign: 'left' }}>
                <span>被查询人体貌特征、口音：</span>
                <span>{approvalformData.dw2 || ''}</span>
              </td>
            </tr>
            <tr>
              <td colSpan={20} style={{ textAlign: 'left', height: '80px', verticalAlign: 'top' }}>
                <div>被查询人违法犯罪记录：</div>
                <div>{approvalformData.detailreason || ''}</div>
              </td>
            </tr>
            <tr style={{ height: '80px' }}>
              <td colSpan={4}>查询时间</td>
              <td colSpan={7}>{approvalformData.submittime || ''}</td>
              <td colSpan={3}>查询电话</td>
              <td colSpan={6}>{approvalformData.lxdh || ''}</td>
            </tr>
            <tr style={{ height: '80px' }}>
              <td colSpan={4}>查询单位</td>
              <td colSpan={7}>{approvalformData.transactunit || ''}</td>
              <td colSpan={3}>查询人</td>
              <td colSpan={6}>{approvalformData.transactprimarytout || ''}</td>
            </tr>
          </tbody>
        </table>
        <div>
          <Seal imageBase64={approvalformData.spdwyz} style={{ bottom: 20, right: 20 }} />
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );
    return <div>{approvalformView}</div>;
  }
}

export default Index;
