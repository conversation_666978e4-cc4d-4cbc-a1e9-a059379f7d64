import uuidv4 from 'uuid/v4';
import moment from 'moment';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '$F{GAFJ}',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              },
              fields: [
                [
                  {
                    key: uuidv4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              ]
            },
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '解除取保候审',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                marginLeft: '-45px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '88px',
                // marginLeft: '107px',
                left: '420px',
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '决 定',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '84px',
                // marginLeft: '68px',
                left: '375px',
                height: '53px',
                textAlign: 'center',
                fontSize: '12pt'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '通 知',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                // width: '100%',
                position: 'absolute',
                top: '106px',
                // marginLeft: '68px',
                left: '375px',
                height: '53px',
                textAlign: 'center',
                fontSize: '12pt'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '$F{WSZH}',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              },
              fields: [
                [
                  {
                    key: uuidv4(),
                    textName: 'WSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '案件名称：<%AJMC%>\n' +
                '案件编号：<%AJBH%>\n' +
                '被取保候审人：<%ZWXM%> <%SEX_1%>\n' +
                '出生日期：<%BIRTHDAYINBEGIN%>\n' +
                '住址：<%XZDZ%>\n' +
                '取保方式：<%QBHSLX%>\n' +
                '执行机关：<%ASSISTANTUNITINFACT%>\n' +
                '取保候审决定时间：<%ZDDDSJ%>\n' +
                '解除原因：<%BRIEFREASON%>\n' +
                '批准人：<%CONFIRMBYPERSON%>\n' +
                '批准时间：<%CONFIRMTIME%>\n' +
                '办案人：<%TRANSACTPRIMARYTOUT%>\n' +
                '办案单位：<%TRANSACTUNIT1%>\n' +
                '填发时间：<%WRITETIME%>\n' +
                '填发人：<%WRITEBY%>',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'AJBH',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'QBHSLX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: { width: '230px' }
                },
                {
                  key: uuidv4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMBYPERSON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'CONFIRMTIME',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTPRIMARYTOUT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'TRANSACTUNIT1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITETIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'WRITEBY',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交保证人',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '解除取保候审通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%XM2%>：\n' +
                '   我局于<%ZDDDSJ%>' +
                '决定对犯罪嫌疑人<%ZWXM%>' +
                '（<%SEX%>' +
                '，<%BIRTHDAYINBEGIN%>' +
                '，住址：<%XZDZ%>' +
                '）取保候审，现因<%BRIEFREASON%>' +
                '，根据<%ITEMOFLAW%>' +
                '之规定，决定解除对其取保候审，并解除你的保证义务。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: 'uuidv4()',
                  textName: 'XM2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: { width: '230px' }
                },
                {
                  key: 'uuidv4()',
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: 'uuidv4()',
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: 'uuidv4()',
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: 'uuidv4()',
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: 'uuidv4()',
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: 'uuidv4()',
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'dynamic',
                    kind: '',
                    params: {
                      configId: moment().isBefore('2018-10-26') ? '20191105Before_C023' : '20191105After_C023'
                    }
                  },
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                // top: '-20px',
                marginTop: "-20px",
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '解除取保候审决定书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textValue: '(副本)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    被取保候审人<%ZWXM%>' +
                '，性别<%SEX%>' +
                '，出生日期<%BIRTHDAYINBEGIN%>' +
                '，住址<%XZDZ%>' +
                '。\n    我局于<%ZDDDSJ%>' +
                '起对其执行取保候审，现因<%BRIEFREASON%>' +
                '，根据<%ITEMOFLAW%>' +
                '之规定，决定予以解除。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: { width: '230px' }
                },
                {
                  key: uuidv4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'dynamic',
                    kind: '',
                    params: {
                      configId: moment().isBefore('2018-10-26') ? '20191105Before_C023' : '20191105After_C023'
                    }
                  },
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '240px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                top: '280px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '本决定书已收到\n      被取保候审人：\n           年   月   日',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                padding: '20px',
                top: '390px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              }
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交被取保候审人',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '解除取保候审决定书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '    被取保候审人<%ZWXM%>' +
                '，性别<%SEX%>' +
                '，出生日期<%BIRTHDAYINBEGIN%>' +
                '，住址<%XZDZ%>' +
                '。\n    我局于<%ZDDDSJ%>' +
                '起对其执行取保候审，现因<%BRIEFREASON%>' +
                '，根据<%ITEMOFLAW%>' +
                '之规定，决定予以解除。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: { width: '230px' }
                },
                {
                  key: uuidv4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'dynamic',
                    kind: '',
                    params: {
                      configId: moment().isBefore('2018-10-26') ? '20191105Before_C023' : '20191105After_C023'
                    }
                  },
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                // top: '-20px',
                marginTop: "-20px",
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuidv4(),
      footText: '此联交执行机关',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '解除取保候审通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%ASSISTANTUNITINFACT%>：\n' +
                '    我局于<%ZDDDSJ%>' +
                '决定对犯罪嫌疑人<%ZWXM%>' +
                '（<%SEX%>' +
                '，<%BIRTHDAYINBEGIN%>' +
                '，住址：<%XZDZ%>' +
                '）取保候审，现因<%BRIEFREASON%>' +
                '，根据<%ITEMOFLAW%>' +
                '之规定，决定予以解除。\n    特此通知。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  customField: true,
                  dateFormat: 'YYYY年MM月DD日',
                  elementStyle: { width: '230px' }
                },
                {
                  key: uuidv4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'dict',
                  dictConfig: {
                    // 字典
                    type: 'dynamic',
                    kind: '',
                    params: {
                      configId: moment().isBefore('2018-10-26') ? '20191105Before_C023' : '20191105After_C023'
                    }
                  },
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                // top: '-20px',
                marginTop: "-20px",
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
