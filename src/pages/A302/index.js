import React, { Component } from 'react';
import { Row, Col, Checkbox } from 'antd';
import { connect } from 'dva';
import Sign from '@/compoments/seal/Sign';
import Seal from '@/compoments/seal/Seal';
import styles from './index.less';
import moment from 'moment';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const {
      sswsmc = '',
      receiveunit = '',
      gxlx,
      writetime = '',
      ajmc = '',
      ajbh = '',
      zy2 = '',
      xm2,
      zxddxz,
      lxfs2 = '',
      detailreason = '',
      briefreason = '',
      xxzz,
      note = '',
      dxxxlist = [],
      xb2,
      age2 = '',
      zxdxjy = '',
      ldps1 = '',
      ldps2 = '',
      ldps3 = '',
      cqdwyz,
      dwldqm,
      dwldxm = '',
      spdwyz,
      spldqm,
      spldxm = '',
      fzdwyz,
      fzldqm,
      fzldxm = '',
      ldpssj1 = '',
      ldpssj2 = '',
      ldpssj3 = ''
    } = bgsData;
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div id="mainText">
          <div className={styles.wsTitle}>{sswsmc}</div>
          <Row className={styles.normalContent} gutter={[8, 8]}>
            <Col>
              <Row type="flex" justify="space-between">
                <Col span={15}>密級：內部</Col>
                <Col>期限：{gxlx}</Col>
              </Row>
            </Col>
            <Col>
              <Row type="flex" justify="space-between">
                <Col span={15}>填表单位：{receiveunit}</Col>
                <Col>填表日期：{writetime ? moment(writetime).format('YYYY年MM月DD日') : null}</Col>
              </Row>
            </Col>
            <Col>案件名称：{ajmc}</Col>
            <Col>案件编号：{ajbh}</Col>
            <Col>案件类别：{zy2}</Col>
            <Col>案件性质：{xxzz}</Col>
            <Col>立案决定书/逮捕证/通缉令文号：{xm2}</Col>
            <Col>
              <Row type="flex">
                <Col span={8}>交案人：{zxddxz}</Col>
                <Col>联系方式：{lxfs2}</Col>
              </Row>
            </Col>
            <Col>基本情况：{detailreason}</Col>
            <Col>已经采取的侦查措施情况及采取技术侦查措施的必要性说明：{briefreason}</Col>
            <Col>备注：{note}</Col>
            <Col>
              <table className={styles.table}>
                <tr>
                  <td className={styles.lable} colSpan={8} style={{ padding: 10 }}>
                    侦查措施适用对象
                  </td>
                </tr>
                <tr className={styles.h40}>
                  <td className={styles.lable} colSpan={2} style={{ width: '15%' }}>
                    序号
                  </td>
                  <td className={styles.lable} colSpan={2} style={{ width: '20%' }}>
                    姓名
                  </td>
                  <td className={styles.lable} colSpan={2} style={{ width: '35%' }}>
                    控制目标
                  </td>
                  <td className={styles.lable} colSpan={2} style={{ width: '35%' }}>
                    号码
                  </td>
                </tr>
                {dxxxlist.map((item) => (
                  <tr key={item.xh}>
                    <td className={styles.lable} colSpan={2}>
                      {item.xh}
                    </td>
                    <td className={styles.lable} colSpan={2}>
                      {item.xm}
                    </td>
                    <td className={styles.lable} colSpan={2}>
                      {item.mbxx}
                    </td>
                    <td className={styles.lable} colSpan={2}>
                      {item.hm}
                    </td>
                  </tr>
                ))}
              </table>
            </Col>
            <Col>
              根据《中国人民共和国刑事诉讼法》等法律法规及有关中央政策文件规定，呈请对以上共{xb2}个适用对象，共{age2}
              个侦控目标采取技术侦查措施。
            </Col>
            <Col>
              <table className={styles.table}>
                <tr className={styles.h80}>
                  <td colSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    交案单位意见
                  </td>
                  <td colSpan={2} className={styles.psyj} style={{ width: '25%' }}>
                    {ldps1}
                    <div style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}>
                      {spldqm ? <Sign imageBase64={spldqm} style={{ right: 180, bottom: 0 }} /> : <span>{spldxm}</span>}
                      <Seal imageBase64={spdwyz} style={{ right: 10, bottom: -28 }} />
                      <span style={{ paddingRight: '10px' }}>{ldpssj1}</span>
                    </div>
                  </td>
                  <td colSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    网安业务部门意见
                  </td>
                  <td colSpan={2} className={styles.psyj} style={{ width: '25%' }} />
                </tr>
                <tr className={styles.h80}>
                  <td colSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    网安法制审核意见
                  </td>
                  <td colSpan={2} className={styles.psyj} />
                  <td colSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    网安部门意见
                  </td>
                  <td colSpan={2} className={styles.psyj} />
                </tr>
                <tr className={styles.h80}>
                  <td colSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    公安机关意见
                  </td>
                  <td colSpan={6} className={styles.psyj} />
                </tr>
              </table>
            </Col>
          </Row>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
