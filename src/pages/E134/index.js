import React, { Component } from 'react';
import { connect } from 'dva';
import WsReport from '../../compoments/wenshu/WenShuReport';
import WsPrintReport from '../../compoments/wenshu/WsPrintReport';
import { getDataConfig } from '../E134/reportConfig/E134';
import styles from './index.less';

let SSWSNAME = '鉴定聘请书';
const FILENAME = 'E134';

@connect(({ E134 }) => E134)
class Index extends Component {
  componentWillMount() {
    const {
      location: { query }
    } = this.props;
    const { wskey, token, type, sswsname} = query;
    SSWSNAME = sswsname.indexOf('刑事')> -1 ? sswsname : '鉴定聘请书';
    this.props.dispatch({
      type: 'E134/fetch',
      payload: {
        systemid: wskey,
        wsmc: SSWSNAME,
        token,
        filename: FILENAME,
        type
      }
    });
  }

  componentDidMount() {}

  render() {
    const { location, formData, formConfig } = this.props;
    const {
      query: { type, page }
    } = location;
    let reportView = '';
    if (!formData.systemid) {
      reportView = <div style={{ textAlign: 'center' }}>加载中...</div>;
    } else if (type === 'edit') {
      reportView = (
        <WsReport
          dataConfig={getDataConfig(formData)}
          wsData={formData}
          formConfig={formConfig}
          showPage={page}
          fileName={FILENAME}
          sswsname={SSWSNAME}
          {...this.props}
        />
      );
    } else if (type === 'print' || type === 'preview') {
      reportView = (
        <WsPrintReport dataConfig={getDataConfig(formData)} wsData={formData} sswsname={SSWSNAME} {...this.props} />
      );
    } else {
      reportView = <div>参数不正确，请检查</div>;
    }
    return (
      <div className={styles.normal}>
        {reportView}
        {/* <WsReport dataConfig={dataConfig} wsData={formData} /> */}
      </div>
    );
  }
}

export default Index;
