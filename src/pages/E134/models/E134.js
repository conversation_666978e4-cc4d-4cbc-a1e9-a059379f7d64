import * as nodePrintSerive from '../services/E134';

export default {
  namespace: 'E134',
  state: {
    formData: {},
    formConfig: []
  },

  reducers: {
    save(state, { payload: { formData, formConfig } }) {
      return { ...state, formData, formConfig };
    }
  },
  effects: {
    *fetch({ payload }, { call, put }) {
      const formData = yield call(nodePrintSerive.fetchData, payload);
      const formConfig = yield call(nodePrintSerive.fetchConfig, { ...payload, wscode: formData.wscode });
      yield put({
        type: 'save',
        payload: {
          formData,
          formConfig
        }
      });
    },
    *updateData({ payload }, { call, put }) {
      // eslint-disable-next-line no-unused-vars
      yield call(nodePrintSerive.updateData, payload);
      yield put({
        type: 'fetch',
        payload
      });
    }
  }
};
