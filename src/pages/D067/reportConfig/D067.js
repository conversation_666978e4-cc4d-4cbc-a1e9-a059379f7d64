import uuidv4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuidv4(),
      footText: '此联交协助查询单位',
      paragraph: [
        {
          key: uuidv4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuidv4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '关于请协助对有关涉案账户紧急止付的函',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuidv4(),
              type: 'staticText',
              textType: 'string',
              textValue: '内部',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          contents: [
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%assistantunit%>：\n' +
                '    近期，我单位在办理一起<%AJMC%>案件中，发现涉案人员利用贵行相关' +
                '账户（附后）进行资金往来。因案情紧急，请贵行立即协助采取紧急止付措施，并以该账户涉嫌违法' +
                '犯罪活动，被<%ZY2%>公安机关予以紧急止付答复有关客户。\n' +
                '    请予以大力协助为盼。\n' +
                '    联系人：<%XM2%>，联系电话：<%LXFS2%>（仅限银行联系，请勿告知有关客户）。\n' +
                '    附件：相关账户列表',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'ASSISTANTUNIT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'ZY2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'LXFS2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuidv4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuidv4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                marginTop: '-20px',
                right: '50px'
              }
            },
            {
              key: uuidv4(),
              type: 'bigText', // 大文本段落
              tempStr: ' <%gafj_1%>\n<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                padding: '20px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuidv4(),
                  textName: 'gafj_1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuidv4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    }
  ] // 文书联数
};
