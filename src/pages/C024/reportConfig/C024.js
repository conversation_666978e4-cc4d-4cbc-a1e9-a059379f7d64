import uuid4 from 'uuid/v4';

export const dataConfig = {
  wsmc: '',
  fileName: '',
  wsCode: '',
  wsType: 'narrative', // filed 填充式 ，narrative 叙述式
  borderLines: 2,
  borderWidth: '',
  pages: [
    {
      key: uuid4(),
      footText: '',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '不予取保候审通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'staticText',
              textValue: '(存根)',
              textName: 'cg',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '136px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '183px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: { display: 'inherit' },
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '案件名称：<%AJMC%>\n' +
                '案件编号：<%AJBH%>\n' +
                '犯罪嫌疑人：<%ZWXM%> 性别 <%SEX%>，<%AGE%>岁\n' +
                '住址：<%XZDZ%>\n' +
                '单位及职业：<%WORKIN%> <%PROFESSION%>\n' +
                '申请人：<%XM2%>\n' +
                '不取保原因：<%BRIEFREASON%>\n' +
                '批准人：<%CONFIRMBYPERSON%>\n' +
                '批准时间：<%CONFIRMTIME%>\n' +
                '办案人：<%TRANSACTPRIMARYTOUT%>\n' +
                '办案单位：<%TRANSACTUNIT1%>\n' +
                '填发时间：<%WRITETIME%>\n' +
                '填发人：<%WRITEBY%>\n',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '10px 20px',
                lineHeight: 1.8,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AGE',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XM2',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJBH',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WORKIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'PROFESSION',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'CONFIRMBYPERSON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'CONFIRMTIME',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'TRANSACTPRIMARYTOUT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'TRANSACTUNIT1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WRITETIME',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'WRITEBY',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuid4(),
      footText: '此联附卷',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '不予取保候审通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },

            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '163px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr:
                '<%XM2%>\n' +
                '    你于<%ZDDDSJ%>提出的（对犯罪嫌疑人）<%ZWXM%>取保候审的申请，经审查认为<%ZWXM%><%BRIEFREASON%>不符合《中华人民共和国刑事诉讼法》' +
                '第<%TIAO%>条规定的取保候审的条件，决定对<%ZWXM%>不予取保候审。',

              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.3,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },

                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },{
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },{
                  key: uuid4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },

              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuid4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                right: '50px',
                marginTop: '-20px'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '本通知书已收到。\n    申请人：\n           <%RECEIVETIME%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                paddingLeft: '22px',
                lineHeight: 1.5,
                marginTop: '30px',
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'RECEIVETIME',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
              ]
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                lineHeight: 1.5,
                left: 0,
                top: '30px',
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
    {
      key: uuid4(),
      footText: '此联交申请人',
      paragraph: [
        {
          key: uuid4(),
          editable: false,
          style: {},
          contents: [
            {
              key: uuid4(),
              type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
              textValue: '不予取保候审通知书',
              textName: 'wsmc',
              elementStyle: {
                fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                width: '100%',
                position: 'absolute',
                top: '88px',
                left: 0,
                height: '53px',
                textAlign: 'center',
                fontSize: '22pt'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '广西南宁市公安局兴宁分局',
              textName: 'gafj',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '40px',
                left: 0,
                height: '48px',
                textAlign: 'center',
                fontSize: '16pt',
                lineHeight: '48px'
              }
            },
            {
              key: uuid4(),
              type: 'textField',
              textType: 'string',
              textValue: '文书字号',
              textName: 'wszh',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                top: '143px',
                left: 0,
                height: '44px',
                textAlign: 'right',
                fontSize: '16pt',
                lineHeight: '44px',
                paddingRight: '22px'
              }
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          contents: [
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%XM2%>\n' +
                '    你于<%ZDDDSJ%>提出的（对犯罪嫌疑人）<%ZWXM%>取保候审的申请，经审查认为<%ZWXM%><%BRIEFREASON%>不符合《中华人民共和国刑事诉讼法》' +
                '第<%TIAO%>条规定的取保候审的条件，决定对<%ZWXM%>不予取保候审。',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'left',
                padding: '20px',
                lineHeight: 1.5,
                left: 0,
                fontSize: '16pt'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZWXM',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'SEX',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BIRTHDAYINBEGIN',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'XZDZ',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'AJMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BRIEFREASON',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {},
                  formRule: [
                    {
                      max: 100,
                      message: '该字段最大长度为100'
                    }
                  ]
                },
                {
                  key: uuid4(),
                  textName: 'ITEMOFLAW',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZXDDMC',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'BANK1',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ASSISTANTUNITINFACT',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'ZDDDSJ',
                  textValue: '',
                  textType: 'date',
                  elementStyle: {}
                },
                {
                  key: uuid4(),
                  textName: 'TIAO',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        },
        {
          key: uuid4(),
          editable: true,
          style: {breakInside: "avoid"},
          contents: [
            {
              key: uuid4(),
              type: 'img',
              textType: 'img',
              textValue: '印章',
              textName: 'spdwyz',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                position: 'absolute',
                right: '50px',
                marginTop: '-20px'
              }
            },
            {
              key: uuid4(),
              type: 'bigText', // 大文本段落
              tempStr: '<%writetimenyr%>',
              elementStyle: {
                fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                width: '100%',
                position: 'absolute',
                textAlign: 'right',
                paddingRight: '22px',
                lineHeight: 2,
                left: 0,
                fontSize: '16pt',
                marginTop: '20px'
              },
              fields: [
                {
                  key: uuid4(),
                  textName: 'writetimenyr',
                  textValue: '',
                  textType: 'string',
                  elementStyle: {}
                }
              ]
            }
          ]
        }
      ]
    },
  ] // 文书联数
};

