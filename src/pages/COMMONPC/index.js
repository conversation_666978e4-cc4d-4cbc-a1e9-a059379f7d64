/**
 * 询 问 笔 录-（不按规定登记承租人信息）
 * 嫌疑人
 *
 * */

import React, { Component } from 'react';
import { connect } from 'dva';
import styles from './index.less';
import {getLineArr} from "@/utils/func";

class Index extends Component {
  constructor() {
    super();
    this.state = {};
  }
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    // debugger
    const { wskey, token, systemCode } = query;
    // const systemid = url.getHashParam('systemid');
    // const token = url.getHashParam('token');
    dispatch({
      type: 'CommonPc/fetch',
      payload: {
        systemid:wskey,
        token,
        systemCode
      }
    });
  }
  // 自定义问题格式处理
  zdy = (obj) => {
    let arr = [];
    for (const key in obj) {
      if (key.indexOf('question') > -1) {
        let indexArr = key.split('question');
        arr[indexArr[1] - 1] = { ...arr[indexArr[1] - 1], question: obj[key]}
      }
      if (key.indexOf('answer')>-1){
        let indexArr = key.split('answer');
        arr[indexArr[1] -1] = { ...arr[indexArr[1] - 1], answer: obj[key]}
      }
    }
    return arr;
  };
  getLine = (data) => {
    const dd = getLineArr(data).map((item, index) => {
      if(index === 0) {
        return <div key={index} className={styles.blackLineFirst}></div>
      } else {
        return <div key={index} className={styles.blackLineArr}></div>
      }
    })
    return <div className={styles.linePosition}>{dd}</div>
  }
  render() {
    const { formData } = this.props;
    const listData = formData.content?JSON.parse(formData.content):{};
    const zdyAnswer = listData && this.zdy(listData);
    console.log(listData,'listData')
    const listView = formData.systemid ? (
      <div className={styles.body}>
        <div className={styles.normal}>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.wsTitle} style={{ display: 'inline-block', padding: '0 0 10px' }}>
              询 问 笔 录
            </div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div className={styles.wsTitle} style={{ display: 'inline-block' }}>
              {`(${listData.abmc})`}
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>时间：</span>
              <span className={styles.date}>
                <span className={styles.blackLine}>
                  {listData.kssjCn || '          年     月     日     时     分'}
                </span>
                <span style={{ padding: '0px 10px' }}>至</span>
                <span className={styles.blackLine}>
                  {listData.jssjCn || '          年     月     日     时     分'}
                </span>
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>地点：</span>
              <span className={styles.blackLine}>
                {listData.dd || '                                                          '}
              </span>
            </div>
          </div>
          <div>
            <div className={`${styles.item} ${styles.imgContent1}`}>
            <span>询问人：</span>
              <span className={styles.blackLine}>
                {/*{listData.xwry1Cn || '                                             '}*/}
                 <span className={styles.imgSeat}>
                  <img
                    className={styles.imgPostion}
                    src={listData.bamjSign1 || ''}
                  />
                </span>
              </span>
              <span style={{ padding: '0px 10px' }}>、</span>
              <span className={styles.blackLine}>
                {/*{listData.xwry2Cn || '                                             '}*/}
                 <span className={styles.imgSeat}>
                  <img
                    className={styles.imgPostion}
                    src={listData.bamjSign2 || ''}
                  />
                </span>
              </span>
              <span>工作单位：</span>
              <span className={styles.blackLine}>
                {listData.xwrgzdwCn || '                                                                      '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>记录人：</span>
              <span className={styles.blackLine}>
                {listData.jlrCn || '                                                '}
              </span>
              <span>工作单位：</span>
              <span className={styles.blackLine}>
                {listData.jlrgzdwCn || '                                                '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>被询问人：</span>
              <span className={styles.blackLine}>{listData.xm || '                                 '}</span>
              <span>性别：</span>
              <span className={styles.blackLine}>{listData.xbCn || '                  '}</span>
              <span>年龄：</span>
              <span className={styles.blackLine}>{listData.nl || '                  '}</span>
              <span>出生日期：</span>
              <span className={styles.blackLine}>
                {listData.csrqCn || '                                          '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>身份证种类及号码：</span>
              <span className={styles.blackLine}>{listData.zjzlCn || '                                   '}</span>
              <span className={styles.blackLine}>{listData.zjhm || '                               '}</span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <div className={styles.checkboxImg}>
                {
                  listData.sfrddb ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>是</span>
              <div className={styles.checkboxImg}>
                {
                  !listData.sfrddb ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>否 人大代表</span>
              <span className={styles.fenge}></span>

              <div className={styles.checkboxImg}>
                {
                  listData.sfzxwy ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>是</span>

              <div className={styles.checkboxImg}>
                {
                  !listData.sfzxwy ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>否 政协委员</span>
              <span className={styles.fenge}></span>

              <div className={styles.checkboxImg}>
                {
                  listData.sfzgdy ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>是</span>

              <div className={styles.checkboxImg}>
                {
                  !listData.sfzgdy ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>否 中共党员</span>
              <span className={styles.fenge}></span>


              <div className={styles.checkboxImg}>
                {
                  listData.sfgjgzry ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>是</span>

              <div className={styles.checkboxImg}>
                {
                  !listData.sfgjgzry ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>否 国家工作人员</span>
              <span className={styles.fenge}></span>


              <div className={styles.checkboxImg}>
                {
                  listData.ywwffzqk ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>有</span>
              <div className={styles.checkboxImg}>
                {
                  !listData.ywwffzqk ?
                    <img src={require('../../assets/box_check.png')} alt=""/> :
                    <img src={require('../../assets/box_no_check.png')} alt=""/>
                }
              </div>
              <span>无 违法犯罪前科</span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>现住址：</span>
              <span className={styles.blackLine}>
                {listData.xzz || '                                                    '}
              </span>
              <span>联系方式：</span>
              <span className={styles.blackLine}>
                {listData.lxdh || '                                                    '}
              </span>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>户籍所在地：</span>
              <span className={styles.blackLine}>
                {listData.hjszd || '                                                                        '}
              </span>
            </div>
          </div>
    {/*      <div>
            <div className={styles.item}>
              <span>（口头传唤∕被扭送∕自动投案的被询问/讯问人</span>
              <span className={styles.blackLine}>
                {listData.xwkssjCn || '          年       月       日      时     分'}
              </span>
              <span>到达，</span>
              <span className={styles.blackLine}>
                {listData.xwjssjCn || '          年       月       日      时     分'}
              </span>
              <span>离开，本人签名</span>
              <span className={styles.blackLine}>{listData.bxwrxm || '                              '}</span>
              <span>)。</span>
            </div>
          </div>*/}
          <div>
            <div className={styles.item}>
              <span>口头传唤的被询问人于{listData?.ddsj}到达，{listData?.lksj}离开，本人签名：</span>
              <span className={styles.imgSeat}>
                <img
                  className={styles.imgPostion}
                  // style={{ maxWidth: '80px', height: '27px', verticalAlign: 'sub' }}
                  src={listData.bxwrSign || ''}
                />
              </span>
            </div>
          </div>
          {
            listData?.substitute === '1' && (
              <div className={styles.item}>
                <span>当事人系文盲无法签名，由民警代签名。</span>
              </div>
            )
          }
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                {listData?.ftxx}
              </span>
              {
                this.getLine(listData?.ftxx)
              }
            </div>
            <div>
              <div className={styles.item}>
                <span>答：</span>
                <span>
                {listData.sfmb === '0' ? '我听明白了。' : listData.sfmb === '1' ? '我不明白。' : ''}
              </span>
                {
                  this.getLine(listData.sfmb === '0' ? '我听明白了。' : listData.sfmb === '1' ? '我不明白。' : '')
                }
              </div>
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                {'这是一份《行政案件快速办理权利义务告知书》，请你看一下，如果你不能阅读，我们可以向你宣读。'}
              </span>
              {
                this.getLine('这是一份《行政案件快速办理权利义务告知书》，请你看一下，如果你不能阅读，我们可以向你宣读。')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfyd === '0' ? '我看过了。' : listData.sfyd === '1' ? '我不识字，你们宣读给我听过' : ''}
              </span>
              {
                this.getLine(listData.sfyd === '0' ? '我看过了。' : listData.sfyd === '1' ? '我不识字，你们宣读给我听过' : '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                你是否同意适用快速办理程序？
              </span>
              {
                this.getLine('你是否同意适用快速办理程序？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfty === '0' ? '我同意适用快速办理程序。' : listData.sfty === '1' ? '我不同意适用快速办理程序。' : ''}
              </span>
              {
                this.getLine(listData.sfty === '0' ? '我同意适用快速办理程序。' : listData.sfty === '1' ? '我不同意适用快速办理程序。' : '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                现依法对你进行询问，请你如实回答我们的问题并接受调查，你有权拒绝回答与案件无关的问题。本次询问通过全程录音录像的方式进行，你是否清楚？
              </span>
              {
                this.getLine('现依法对你进行询问，请你如实回答我们的问题并接受调查，你有权拒绝回答与案件无关的问题。本次询问通过全程录音录像的方式进行，你是否清楚？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfqc === '0' ? '清楚了。' : listData.sfqc === '1' ? '不清楚。' : ''}
              </span>
              {
                this.getLine(listData.sfqc === '0' ? '清楚了。' : listData.sfqc === '1' ? '不清楚。' : '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                说一下你的基本情况，包括姓名、出生日期、身份证码、 户籍所在地和现住址、文化程度等。
              </span>
              {
                this.getLine('说一下你的基本情况，包括姓名、出生日期、身份证码、 户籍所在地和现住址、文化程度等。')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.jbqk || ''}
              </span>
              {
                this.getLine(listData.jbqk || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                你的家庭主要成员情况？
              </span>
              {
                this.getLine('你的家庭主要成员情况？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.jtqk || ''}
              </span>
              {
                this.getLine(listData.jtqk || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                根据有关法律规定公安机关应当及时将传唤的原因和处所等相关事项通知你的家属，我们如何通知你的家属？
              </span>
              {
                this.getLine('根据有关法律规定公安机关应当及时将传唤的原因和处所等相关事项通知你的家属，我们如何通知你的家属？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sftzjs || ''}
              </span>
              {
                this.getLine(listData.sftzjs || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你是否申请回避？</span>
              {
                this.getLine('你是否申请回避？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfhb || ''}
              </span>
              {
                this.getLine(listData.sfhb || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>
                你以前是否受过刑事处罚、行政处罚或有被收容教育、强制隔离戒毒或社区戒毒、收容教育违法犯罪经历等情况？
              </span>
              {
                this.getLine('你以前是否受过刑事处罚、行政处罚或有被收容教育、强制隔离戒毒或社区戒毒、收容教育违法犯罪经历等情况？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.gqqk === '0' ? '是。' : listData.gqqk === '1' ? '否。' : ''}
                {listData.gqqkxq || ''}
              </span>
              {
                this.getLine(listData.gqqk === '0' ? '是。' : listData.gqqk === '1' ? '否。' : '' + listData.gqqkxq || '321321')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你有什么行为？</span>
              {
                this.getLine('你有什么行为？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.xw ||''}
              </span>
              {
                this.getLine(listData.xw ||'')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你把具体经过讲一下？</span>
              {
                this.getLine('你把具体经过讲一下？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.wgxw || ''}
              </span>
              {
                this.getLine(listData.wgxw || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>是否有其他人和你一起实施违法行为？</span>
              {
                this.getLine('是否有其他人和你一起实施违法行为？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfwf === '0' ? '是。' : listData.gqqk === '1' ? '否。' : ''}
                {listData.wfxw || ''}
              </span>
              {
                this.getLine(listData.sfwf === '0' ? '是。' : listData.gqqk === '1' ? '否。' : '' + listData.wfxw || '')
              }
            </div>
          </div>
          {/*<div>*/}
          {/*  <div className={styles.item}>*/}
          {/*    <span className={styles.blackLine}>{`问：${listData.question}？`}</span>*/}
          {/*  </div>*/}
          {/*</div>*/}
          {/*<div>*/}
          {/*  <div className={styles.item}>*/}
          {/*    <span className={styles.blackLine}>答：</span>*/}
          {/*    <span className={styles.blackLine}>*/}
          {/*      {listData.answer || '                                                     '}*/}
          {/*    </span>*/}
          {/*  </div>*/}
          {/*</div>*/}
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你对违法行为有什么认识？</span>
              {
                this.getLine('你对违法行为有什么认识？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.wfrs === '0' ? '我认识到自己错了，愿意接受公安机关的处罚。' : listData.wfrs === '1' ? '其他。' : ''}
                {listData.wfrsxq || ''}
              </span>
              {
                this.getLine(listData.wfrs === '0' ? '我认识到自己错了，愿意接受公安机关的处罚。' : listData.wfrs === '1' ? '其他。' : '' + listData.wfrsxq || '')
              }
            </div>
          </div>
          <div>
            {
              zdyAnswer && zdyAnswer.map((item1,index) => {
                return (
                  <div>
                    {/*<div className={styles.item}>*/}
                    {/*  <span>{`自定义问题${index + 1}：`}</span>*/}
                    {/*</div>*/}
                    <div>
                      <div className={styles.item}>
                        <span>问：</span>
                        <span>
                          {item1.question || ''}
                        </span>
                        {
                          this.getLine(item1.question || '')
                        }
                      </div>
                    </div>
                    <div>
                      <div className={styles.item}>
                        <span>答：</span>
                        <span>
                          {item1.answer || ''}
                        </span>
                        {
                          this.getLine(item1.answer || '')
                        }
                      </div>
                    </div>
                  </div>
                )
              })
            }
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>你还有什么要补充的吗？（如家庭成员的特殊情况）</span>
              {
                this.getLine('你还有什么要补充的吗？（如家庭成员的特殊情况）')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.bcss === '0' ? '有。' : listData.bcss === '1' ? '没有。' : ''}
                {listData.bcqk || ''}
              </span>
              {
                this.getLine(listData.bcss === '0' ? '有。' : listData.bcss === '1' ? '没有。' : '' + listData.bcqk || '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>公安机关对你询问期间是否保障你的权益？</span>
              {
                this.getLine('公安机关对你询问期间是否保障你的权益？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.bzysxx === '0' ? '有。' : listData.bzysxx === '1' ? '没有。' : ''}
              </span>
              {
                this.getLine(listData.bzysxx === '0' ? '有。' : listData.bzysxx === '1' ? '没有。' : '')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>问：</span>
              <span>以上笔录是否与你所说的一致？</span>
              {
                this.getLine('以上笔录是否与你所说的一致？')
              }
            </div>
          </div>
          <div>
            <div className={styles.item}>
              <span>答：</span>
              <span>
                {listData.sfss === '0'
                  ? '以上笔录我看过，与我所说的一致。'
                  : listData.sfss === '1'
                    ? '以上笔录已经念给我听了，与我所说的一致。'
                    : listData.sfss === '2'
                      ? '其他。'
                      : ''
                }
                {listData.sgtxqk || ''}
              </span>
              {
                this.getLine(listData.sfss === '0'
                  ? '以上笔录我看过，与我所说的一致。'
                  : listData.sfss === '1'
                    ? '以上笔录已经念给我听了，与我所说的一致。'
                    : listData.sfss === '2'
                      ? '其他。'
                      : '' + listData.sgtxqk || '')
              }
            </div>
          </div>
        </div>
      </div>
    ) : (
      <div className={styles.loading}>加载中...</div>
    );
    return <div>{listView}</div>;
  }
}

export default connect(({ CommonPc }) => ({
  ...CommonPc
}))(Index);
