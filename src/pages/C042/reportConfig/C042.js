import uuid4 from 'uuid/v4';
import uuidv4 from 'uuid/v4';

export const getDataConfig = (formData, type) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 1,
    borderWidth: '',
    pages: [
      {
        key: uuid4(),
        footText: '',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '公安行政罚款通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  `    ${formData.zwxm != null ? '<%ZWXM%>' : '<%DWMC%> <%FRDB%>'}，${
                    formData.frdb || formData.zwxm ? '<%SEX%>' : ''
                  }，${formData.frdb || formData.zwxm ? '<%AGE%>' : ''}` +
                  `岁，住址：<%XZDZ%>，身份证号：<%ZJHM%>，${
                    formData.lxfs2 || type === 'edit' ? '证件号码：<%LXFS2%>，' : ''
                  }联系电话：<%LXDH%>。根据<%SUPERWSZH%>《<%SUPERWSMC%>》` +
                  '裁决（决定），请于15日内将罚款<%BMONEY%>（小写：<%MMONEY%>）向<%BANK%>交纳。\n' +
                  '    承办单位：<%TRANSACTUNIT1%>\n' +
                  '    承办人：<%TRANSACTPRIMARYTOUT%>\n' +
                  '    批准人：<%CONFIRMBYPERSON%>\n' +
                  '填写时间：<%WRITETIME%>\n\n' +
                  '存根',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'SUPERWSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'DWMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AGE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXFS2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXDH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWRITID',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWSMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'MMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'TRANSACTUNIT1',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'TRANSACTPRIMARYTOUT',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'CONFIRMBYPERSON',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联回执（附卷）',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '公安行政罚款通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  `${formData.zwxm != null ? '<%ZWXM%>' : '<%DWMC%><%FRDB%>'}：\n    ${
                    formData.zwxm != null ? '<%ZWXM%>' : '<%DWMC%>  <%FRDB%>'
                  }，${formData.frdb || formData.zwxm ? '<%SEX%>' : ''}，${
                    formData.frdb || formData.zwxm ? '<%AGE%>' : ''
                  }` +
                  `岁，住址：<%XZDZ%>，身份证号：<%ZJHM%>，${
                    formData.lxfs2 || type === 'edit' ? '证件号码：<%LXFS2%>，' : ''
                  }联系电话：<%LXDH%>。根据<%SUPERWSZH%>《<%SUPERWSMC%>》` +
                  '裁决（决定），请于15日内将罚款<%BMONEY%>（小写：<%MMONEY%>）向<%BANK%>' +
                  '交纳。逾期不缴纳罚款的（除有法律，法规规定的）每日还应加收滞纳金。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'SUPERWSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'DWMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AGE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXFS2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXDH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWRITID',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWSMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'MMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { breakInside: 'avoid' },
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '20px',
                  right: '50px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '宣布时间：<%WRITETIME%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  marginTop: '68px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联交行政部门',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '公安行政罚款通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%GAFJ%>：\n' +
                  `    ${formData.zwxm != null ? '<%ZWXM%>' : '<%DWMC%>  <%FRDB%>'}，${
                    formData.frdb || formData.zwxm ? '<%SEX%>' : ''
                  }，${formData.frdb || formData.zwxm ? '<%AGE%>' : ''}` +
                  `岁，住址：<%XZDZ%>，身份证号：<%ZJHM%>，${
                    formData.lxfs2 || type === 'edit' ? '证件号码：<%LXFS2%>，' : ''
                  }联系电话：<%LXDH%>。根据<%SUPERWSZH%>《<%SUPERWSMC%>》` +
                  '裁决（决定），请于15日内将罚款<%BMONEY%>（小写：<%MMONEY%>）向<%BANK%>' +
                  '交纳。逾期不缴纳罚款的（除有法律，法规规定的）每日还应加收滞纳金。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'SUPERWSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'DWMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AGE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXFS2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXDH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWRITID',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWSMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'MMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { breakInside: 'avoid' },
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '20px',
                  right: '50px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '宣布时间：<%WRITETIME%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  marginTop: '68px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联交银行',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '公安行政罚款通知书',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr:
                  '<%BANK%>：\n' +
                  `    ${formData.zwxm != null ? '<%ZWXM%>' : '<%DWMC%>  <%FRDB%>'}，${
                    formData.frdb || formData.zwxm ? '<%SEX%>' : ''
                  }，${formData.frdb || formData.zwxm ? '<%AGE%>' : ''}` +
                  `岁，住址：<%XZDZ%>，身份证号：<%ZJHM%>，${
                    formData.lxfs2 || type === 'edit' ? '证件号码：<%LXFS2%>，' : ''
                  }联系电话：<%LXDH%>。根据<%SUPERWSZH%>《<%SUPERWSMC%>》` +
                  '裁决（决定），请于15日内将罚款<%BMONEY%>（小写：<%MMONEY%>）向<%BANK%>' +
                  '交纳。逾期不缴纳罚款的（除有法律，法规规定的）每日还应加收滞纳金。',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'SUPERWSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BANK',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'DWMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'FRDB',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SEX',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'AGE',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'XZDZ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZJHM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXFS2',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'LXDH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWRITID',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'SUPERWSMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'MMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            style: { breakInside: 'avoid' },
            contents: [
              {
                key: uuid4(),
                type: 'img',
                textType: 'img',
                textValue: '印章',
                textName: 'spdwyz',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  position: 'absolute',
                  marginTop: '20px',
                  right: '50px'
                }
              },
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '宣布时间：<%WRITETIME%>',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  marginTop: '68px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuidv4(),
                    textName: 'WRITETIME',
                    textValue: '',
                    textType: 'date',
                    elementStyle: {}
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        key: uuid4(),
        footText: '此联由银行填写后交办案单位附卷',
        paragraph: [
          {
            key: uuid4(),
            editable: false,
            style: {},
            contents: [
              {
                key: uuid4(),
                type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
                textValue: '公安行政罚款通知书（回执）',
                textName: 'wsmc',
                elementStyle: {
                  fontFamily: '方正小标宋简体,FZXiaoBiaoSong-B05S,serif',
                  width: '100%',
                  position: 'absolute',
                  top: '88px',
                  left: 0,
                  height: '53px',
                  textAlign: 'center',
                  fontSize: '22pt'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuid4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '143px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: `<%GAFJ%>：\n    根据你局<%WSZH%>通知书，已收到${
                  formData && formData.zwxm ? '<%ZWXM%>' : '<%DWMC%>'
                }交来的罚款共<%BMONEY%>。`,

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'left',
                  padding: '20px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                },
                fields: [
                  {
                    key: uuid4(),
                    textName: 'GAFJ',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'WSZH',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'ZWXM',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'DWMC',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  },
                  {
                    key: uuid4(),
                    textName: 'BMONEY',
                    textValue: '',
                    textType: 'string',
                    elementStyle: {}
                  }
                ]
              }
            ]
          },
          {
            key: uuid4(),
            editable: true,
            contents: [
              {
                key: uuid4(),
                type: 'bigText', // 大文本段落
                tempStr: '    年    月    日',

                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  textAlign: 'right',
                  paddingRight: '22px',
                  marginTop: '68px',
                  lineHeight: 2,
                  left: 0,
                  fontSize: '16pt'
                }
              }
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
