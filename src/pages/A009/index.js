import React, { Component } from 'react';
import { connect } from 'dva';
import { Input } from 'antd';
import styles from './index.less';

const { TextArea } = Input;

/// /////////////////////////////////
/// /////                ////////////
/// ////        弃用     /////////////
/// /////                ////////////
/// /////////////////////////////////

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    const { analysiscaseContent = '', analysiscaseFoot = '', analysiscase } = bgsData;
    const footStartIndex = analysiscaseFoot.indexOf(' ');
    const realFoot = analysiscaseFoot.substring(footStartIndex);
    const plusFoot = analysiscaseFoot.substring(0, footStartIndex);
    const realContent = analysiscaseContent + plusFoot;
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div style={{ textAlign: 'center', fontSize: '18pt' }}>{bgsData.assistantunit || ''}</div>
        <div className={styles.wsTitle}>{bgsData.sswsmc || ''}</div>
        <div style={{ textAlign: 'right', marginRight: '32px' }}>{bgsData.wszh || ''}</div>
        <div className={styles.content}>
          {/* <pre className={styles.analysisCaseContent}>{analysiscaseContent}</pre> */}
          <TextArea
            autosize
            defaultValue={realContent}
            style={{
              fontSize: '16pt',
              border: 0,
              padding: 0,
              resize: 'none',
              // position: 'absolute',
              // top: '100px',
              // left: '0',
              width: '624px'
            }}
          />
          <div />
          <TextArea
            autosize
            defaultValue={realFoot}
            style={{
              fontSize: '16pt',
              border: 0,
              padding: 0,
              resize: 'none',
              // position: 'absolute',
              // top: '100px',
              // left: '0',
              width: '624px'
            }}
          />
          {/* <div className={styles.analysisCaseContent}>{analysiscase}</div> */}
          {/* <div className={styles.analysisCaseContent} dangerouslySetInnerHTML={{ __html: analysiscase }}></div> */}
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return (
      <div className={styles.container}>
        <div className={styles.outBorder}>{reportView}</div>
      </div>
    );
  }
}

export default Index;
