.container{
  position: relative;
  width: 664px;
  margin: auto;
}
.outBorder {
  //height: 953px;
  //width: 664px;
  //position: absolute;
  //left: 0;
  //top: 0;
  //border: 3px solid #000000;
}
.normal {
  position: absolute;
  left: 0;
  top: 0;
  width: 664px;
  margin: 0 auto;
  font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
  font-size: 16pt;
  padding: 20px 20px 0 20px;

  .wsTitle {
    height: 64px;
    font-size: 22pt;
    font-family: FZXiaoBiaoSong-B05S, serif;
    text-align: center;
    line-height: 64px;
  }

  .table {
    border: 1px solid #000000;
    font-family: <PERSON><PERSON><PERSON><PERSON>g, <PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    width: 100%;

    .lable {
      text-align: center;
    }

    .value {
      border: 0;
    }

    td {
      border: 1px solid #000;
    }
  }

  .content {
    font-family: <PERSON><PERSON><PERSON><PERSON>g, <PERSON>ong, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    line-height: 1.5;
    word-break: break-all;
  }

  .analysisCaseContent{
    font-size: 16pt;
    font-family: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, 仿宋, 华文仿宋, <PERSON><PERSON>, serif;
    width: 624px;
    overflow:auto;
    white-space: pre-line;
  }
}
