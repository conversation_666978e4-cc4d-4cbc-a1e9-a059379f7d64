import React, { Component } from 'react';
import { Row, Col, Checkbox, Icon } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import Sign from '@/compoments/seal/Sign';
import Seal from '@/compoments/seal/Seal';
import styles from './index.less';

@connect(({ bgs }) => bgs)
class Index extends Component {
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    });
  }
  render() {
    const { bgsData } = this.props;
    console.log('bgsData', bgsData);
    const {
      sswsmc = '',
      receiveunit = '',
      gxlx,
      writetime = '',
      ajmc = '',
      zy2 = '',
      xm2,
      zxddxz,
      lxfs2 = '',
      detailreason = '',
      briefreason = '',
      xxzz,
      note = '',
      dxxxlist = [],
      xb2,
      age2 = '',
      dw2 = '',
      sf = '',
      zxdxjy = '',
      zdddsj = '',
      tqjcsj = '',
      cqdwyz,
      dwldqm,
      dwldxm = '',
      spdwyz,
      spldqm,
      spldxm = '',
      fzdwyz,
      fzldqm,
      fzldxm = '',
      ldpssj1 = '',
      ldpssj2 = '',
      ldpssj3 = '',
      ldps1
    } = bgsData;
    console.log('bgsData', bgsData);
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div id="mainText">
          <div className={styles.wsTitle}>{sswsmc}</div>
          <Row className={styles.normalContent} gutter={[8, 8]}>
            <Col>
              <Row type="flex" justify="space-between">
                <Col span={15}>密級：內部</Col>
                <Col>期限：{gxlx}</Col>
              </Row>
            </Col>
            <Col>
              <Row type="flex" justify="space-between">
                <Col span={15}>填表单位：{receiveunit}</Col>
                <Col>填表日期： {writetime ? moment(writetime).format('YYYY年MM月DD日') : null}</Col>
              </Row>
            </Col>
            <Col>案事件名称：{ajmc}</Col>
            <Col>案事件类别：{zy2}</Col>
            <Col>文书号：{xm2}</Col>
            <Col>
              <Row type="flex">
                <Col span={8}>承办人：{zxddxz}</Col>
                <Col>联系方式：{lxfs2}</Col>
              </Row>
            </Col>
            <Col>基本情况：{detailreason}</Col>
            <Col>使用资源：{briefreason}</Col>
            <Col>使用目的：{xxzz}</Col>
            <Col>备注：{note}</Col>
            <Col>
              <table className={styles.table}>
                <tr>
                  <td className={styles.lable} colSpan={6} style={{ padding: 10 }}>
                    拟采取网络信息查询监控措施的对象、目标或特定信息
                  </td>
                </tr>
                <tr className={styles.h40}>
                  <td className={styles.lable} colSpan={1} style={{ width: '10%' }}>
                    序号
                  </td>
                  <td className={styles.lable} colSpan={1} style={{ width: '15%' }}>
                    姓名
                  </td>
                  <td className={styles.lable} colSpan={1} style={{ width: '25%' }}>
                    证件种类与号码
                  </td>
                  <td className={styles.lable} colSpan={1} style={{ width: '25%' }}>
                    目标或特定信息
                  </td>
                  <td className={styles.lable} colSpan={1} style={{ width: '15%' }}>
                    号码
                  </td>
                  <td className={styles.lable} colSpan={1} style={{ width: '10%' }}>
                    备注
                  </td>
                </tr>
                {dxxxlist.map((item) => (
                  <tr key={item.xh}>
                    <td className={styles.lable} colSpan={1} style={{ width: '10%' }}>
                      {item.xh}
                    </td>
                    <td className={styles.lable} colSpan={1} style={{ width: '15%' }}>
                      {item.xm}
                    </td>
                    <td className={styles.lable} colSpan={1} style={{ width: '25%' }}>
                      {item.zjxx}
                    </td>
                    <td className={styles.lable} colSpan={1} style={{ width: '25%' }}>
                      {item.mbxx}
                    </td>
                    <td className={styles.lable} colSpan={1} style={{ width: '15%' }}>
                      {item.hm}
                    </td>
                    <td className={styles.lable} colSpan={1} style={{ width: '10%' }}>
                      {item.bz}
                    </td>
                  </tr>
                ))}
              </table>
            </Col>
            <Col>
              *申请对上述{xb2}个对象、{age2}个目标、{dw2}个特定信息采取网络信息查阅监控。
            </Col>
            <Col>采用网络信息查询监控措施的方式</Col>
            <Col>
              <Row type="flex" align="middle">
                <Col span={8} style={{ display: 'flex', alignItems: 'center' }}>
                  <Icon type={sf === '1' ? 'check-square' : 'border'} style={{ fontSize: '20px' }} />
                  <span style={{ paddingLeft: '8px' }}>手动查询</span>
                </Col>
                <Col>
                  查询时间：
                  {zdddsj && tqjcsj
                    ? `${moment(zdddsj).format('YYYY年MM月DD日')} 至 ${moment(tqjcsj).format('YYYY年MM月DD日')}`
                    : null}
                </Col>
              </Row>
            </Col>
            <Col>
              <table className={styles.table}>
                <tr className={styles.h40}>
                  <td rowSpan={2} className={styles.lable} style={{ width: '25%' }}>
                    提请单位意见
                  </td>
                  <td className={styles.psyj} style={{ borderBottom: 0 }}>
                    {ldps1}
                  </td>
                </tr>
                <tr className={styles.h40}>
                  <td style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}>
                    {spldqm ? <Sign imageBase64={spldqm} style={{ right: 180, bottom: 0 }} /> : <span>{spldxm}</span>}
                    <Seal imageBase64={spdwyz} style={{ right: 10, bottom: -28 }} />
                    <span style={{ paddingRight: '10px' }}>{ldpssj1}</span>
                  </td>
                </tr>
                <tr className={styles.h40}>
                  <td rowSpan={2} className={styles.lable}>
                    网安部门意见
                  </td>
                  <td className={styles.psyj} style={{ borderBottom: 0 }} />
                </tr>
                <tr className={styles.h40}>
                  <span />
                </tr>
                <tr className={styles.h40}>
                  <td rowSpan={2} className={styles.lable}>
                    公安机关意见
                  </td>
                  <td className={styles.psyj} style={{ borderBottom: 0 }} />
                </tr>
                <tr className={styles.h40}>
                  <span />
                </tr>
              </table>
            </Col>
          </Row>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
