import uuidv4 from 'uuid/v4';
import moment from 'moment';

export const getDataConfig = (formData) => {
  return {
    wsmc: '',
    fileName: '',
    wsCode: '',
    wsType: 'narrative', // filed 填充式 ，narrative 叙述式
    borderLines: 2,
    borderWidth: '',
    pages: [
      {
        key: uuidv4(),
        footText: '此联存根',
        paragraph: [
          {
            key: uuidv4(),
            editable: false,
            style: {},
            contents: [
              {
                key: 'uuidv4()',
                type: 'staticText',
                textValue: '立 案 决 定 书',
                textName: '',
                elementStyle: {
                  fontFamily: '方正小标宋简体,Heiti,serif',
                  width: '377.3522px',
                  position: 'absolute',
                  top: '34.668px',
                  left: '4.000599999999999px',
                  height: '42.6688px',
                  textAlign: 'center',
                  fontSize: '22pt',
                  lineHeight: '42.6688px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '广西南宁市公安局兴宁分局',
                textName: 'gafj',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '40px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'staticText',
                textValue: '(存根)',
                textName: 'cg',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '136px',
                  left: 0,
                  height: '48px',
                  textAlign: 'center',
                  fontSize: '16pt',
                  lineHeight: '48px'
                }
              },
              {
                key: uuidv4(),
                type: 'textField',
                textType: 'string',
                textValue: '文书字号',
                textName: 'wszh',
                elementStyle: {
                  fontFamily: 'STFangsong, FangSong, 华文仿宋, 仿宋',
                  width: '100%',
                  position: 'absolute',
                  top: '183px',
                  left: 0,
                  height: '44px',
                  textAlign: 'right',
                  fontSize: '16pt',
                  lineHeight: '44px',
                  paddingRight: '22px'
                }
              }
            ]
          },
          {
            key: uuidv4(),
            editable: true,
            contents: [
              [

                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '案件名称',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '216.01039999999998px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '办案单位',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '633.3646px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '填发时间',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '680.0336px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '办 案 人',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '586.6956px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '批准时间',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '540.0265999999999px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '批 准 人',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '493.3576px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '单位及职业',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '124.00619999999999px',
                    position: 'absolute',
                    top: '446.68859999999995px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '案件编号',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '262.6794px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '住    址',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '400.01959999999997px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '犯罪嫌疑人',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '124.00619999999999px',
                    position: 'absolute',
                    top: '309.34839999999997px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '(存根)',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '377.3522px',
                    position: 'absolute',
                    top: '80.00359999999999px',
                    left: '4.000599999999999px',
                    height: '42.6688px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '42.6688px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '填 发 人',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '109.33879999999999px',
                    position: 'absolute',
                    top: '726.7026px',
                    left: '2.6671999999999993px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '此联存根',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '393.35299999999995px',
                    position: 'absolute',
                    top: '794.7059999999999px',
                    left: '-2.6664000000000003px',
                    height: '29.334799999999998px',
                    textAlign: 'left',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'staticText',
                  textValue: '出生日期',
                  textName: '',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '108.0054px',
                    position: 'absolute',
                    top: '353.3506px',
                    left: '4.000599999999999px',
                    height: '29.334799999999998px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '29.334799999999998px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'gafj',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '377.3522px',
                    position: 'absolute',
                    top: '-8.000799999999998px',
                    left: '4.000599999999999px',
                    height: '34.6684px',
                    textAlign: 'center',
                    fontSize: '16pt',
                    lineHeight: '34.6684px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'wszh',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '386.686px',
                    position: 'absolute',
                    top: '138.67319999999998px',
                    left: '-1.3330000000000002px',
                    height: '33.335px',
                    textAlign: 'right',
                    fontSize: '16pt',
                    lineHeight: '33.335px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'ajmc',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '206.67659999999998px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'ajbh',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '253.3456px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'zwxm',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '137.34019999999998px',
                    position: 'absolute',
                    top: '300.0146px',
                    left: '126.67339999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'center',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'xzdz',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '390.6858px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: '(workin!=null?$f{workin}:"")+($f{profession}!=null?$f{profession}:"")',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '253.34599999999998px',
                    position: 'absolute',
                    top: '436.02139999999997px',
                    left: '126.67339999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'confirmbyperson',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '484.0238px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'confirmtime',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '530.6927999999999px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'transactprimarytout',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '577.3618px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'writetime',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '670.6998px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'transactunit1',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '624.0308px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'writaddspell',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '240.01199999999997px',
                    position: 'absolute',
                    top: '-197.34359999999998px',
                    left: '77.3376px',
                    height: '26.668px',
                    textAlign: 'right',
                    fontSize: '8pt',
                    lineHeight: '26.668px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'writeby',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '718.7022px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'birthdayinbegin',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '268.0134px',
                    position: 'absolute',
                    top: '345.3502px',
                    left: '112.00599999999999px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                },
                {
                  key: 'uuidv4()',
                  type: 'textField',
                  textType: 'string',
                  textValue: '',
                  textName: 'sex',
                  elementStyle: {
                    fontFamily: '仿宋,Heiti,serif',
                    width: '94.67139999999999px',
                    position: 'absolute',
                    top: '300.0146px',
                    left: '285.34799999999996px',
                    height: '40.001999999999995px',
                    textAlign: 'left',
                    fontSize: '11pt',
                    lineHeight: '40.001999999999995px'
                  }
                }
              ]
            ]
          }
        ]
      }
    ] // 文书联数
  };
};
