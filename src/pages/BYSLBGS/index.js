import React, { Component } from 'react';
import { connect } from 'dva';
import Seal from '../../compoments/seal/Seal';
import Sign from '../../compoments/seal/Sign';
import styles from './index.less';

@connect(({ bgs }) => bgs)
class Index extends Component {
  state = {
    fontSize: 22,
  };
  componentDidMount() {
    const {
      location: { query },
      dispatch
    } = this.props;
    const { wskey, token, sswsname } = query;
    dispatch({
      type: 'bgs/queryBgs',
      payload: {
        wskey,
        token,
        wsname: sswsname
      }
    })
  }
  componentDidUpdate() {
    this.countSswsmcSizeFont();
  }

  countSswsmcSizeFont = () => {
    const sswsmcDiv = document.getElementById('sswsmc');
    if (!sswsmcDiv) return false;
    const divHeight = sswsmcDiv.clientHeight || 64;
    if (divHeight > 64) {
      this.setState(
        {
          fontSize: this.state.fontSize - 1
        },
        () => {
          this.countSswsmcSizeFont();
        }
      );
    }
  };

  render() {
    const { fontSize } = this.state;
    const { bgsData } = this.props;
    const {
      analysiscase = '',
      ldps1 = '',
      ldps2 = '',
      cqdwyz,
      dwldqm,
      dwldxm = '',
      spdwyz,
      spldqm,
      spldxm = '',
      ldpssj1 = '',
      ldpssj2 = '',
      reservation04=''
    } = bgsData;
    const reportView = bgsData.systemid ? (
      <div className={styles.normal}>
        <div id="mainText">
          <table className={styles.table}>
            <tr style={{ height: '120px' }}>
              <td rowSpan={2} className={styles.lable} style={{ width: '15%' }}>
                领导
                <br />
                批示
              </td>
              <td style={{ borderBottom: 0 }}>{ldps1}</td>
            </tr>
            <tr style={{ height: '40px' }}>
              <td style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}>
                {spldqm ? <Sign imageBase64={spldqm} style={{ right: 180, bottom: 0 }} /> : <span>{spldxm}</span>}
                {/* 如果没有签名或没有批示就不出印章 */}
                {(ldps1 || spldqm) && <Seal imageBase64={spdwyz} style={{ right: 10, bottom: 0 }} />}
                <span style={{ paddingRight: '10px' }}>{ldpssj1}</span>
              </td>
            </tr>
            <tr style={{ height: '120px' }}>
              <td rowSpan={2} className={styles.lable}>
                办案
                <br />
                部门
                <br />
                意见
              </td>
              <td style={{ borderBottom: 0 }}>{ldps2}</td>
            </tr>
            <tr style={{ height: '40px' }}>
              <td style={{ borderTop: 0, textAlign: 'right', position: 'relative' }}>
                {dwldqm ? <Sign imageBase64={dwldqm} style={{ right: 180, bottom: 0 }} /> : <span>{dwldxm}</span>}
                <Seal imageBase64={cqdwyz} style={{ right: 10, bottom: 0 }} />
                <span style={{ paddingRight: '10px' }}>{ldpssj2}</span>
              </td>
            </tr>
          </table>
          <div id="sswsmc" className={styles.wsTitle} style={{ fontSize: `${fontSize}pt` }}>
            {reservation04}
          </div>
          <div className={styles.content}>
            <pre className={styles.analysisCaseContent}>{analysiscase}</pre>
          </div>
        </div>
      </div>
    ) : (
      <div style={{ textAlign: 'center' }}>加载中...</div>
    );

    return <div>{reportView}</div>;
  }
}

export default Index;
