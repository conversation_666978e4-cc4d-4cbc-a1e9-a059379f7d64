window.SinoGearGlobalConfig = {
  open: true,
  qmb_open_video: true,
  cjpt: 'open', // cjpt配置 open or close
  selectQmb: 'JY', // 捷宇JY, 有方YF
  qmbDir: 'D:\\', // 不配置默认为C:\\
  qmfeContentPath: 'http://localhost:8806',
  qmbContextPath: 'http://*************:31627', // 签名版
  wsreportPrintContextPath: 'http://************:3002',
  wsreportContextPath: 'http://************:8002',
  upLoadWsContextPath: 'http://************:8099',
  dzjzContextPath: 'http://************:8084',
  ajblContextPath: 'http://************:9980',
  contextPath: 'http://************:8099',
  dictContextPath: 'http://************:9980',
  qmnyConfig: {
    // 签名捺印配置, 广西快办
    selectQmb: 'JY', // 捷宇JY, 有方YF
    qmbDir: 'D:\\JakbQmVideo\\', // 不配置默认为C:\\
    qmbContextPath: 'http://*************:31627' // 签名捺印服务
  },
  fzywContextPath: 'http://*************:31121', // 法制业务
  // qmContextPath: 'http://*************:8080', // 签名
  // isOpenQM: true,
  // openQMDS:'4502,3707',
  // enablePrintLimit: true

  // wsreportPrintContextPath: 'http://************:3002',
  wsreportContextPath: 'http://************:8002',
  // upLoadWsContextPath: 'http://************:31782',
  // dzjzContextPath: 'http://************:8084',
  // ajblContextPath: 'http://192.168.2.105:8080',
  // contextPath: 'http://192.168.2.105:8088',
  // // contextPath: 'http://127.0.0.1:8088',
  // dictContextPath: 'http://************:31781',
  // fzywContextPath:"http://*************:31121", // 法制业务
};
