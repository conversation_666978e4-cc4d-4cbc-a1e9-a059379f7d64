<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
  <script src="http://lab.mkblog.cn/high/happy.js"></script>
  <style>
    /*外部div样式*/
    .line-content{
      display: inline-block; /*内部使用块级*/
      overflow: hidden; /*隐藏滚动条*/
      vertical-align: top;
      width: 86%;
      padding: 0.1vh 0 0 0;
    }
    /*每一行的样式，设置两边对齐以及文字下面的虚线*/
    .cnnr-detail{
      display: block;
      text-align: justify;
      text-align-last: justify;
      line-height: 2.3vw;
      padding-bottom: 0.4vw;
      border-bottom: #000 solid 0.1vw;
    }
  </style>
</head>
<body>
<div contenteditable="true" class="line-content" id="cnnr-detail">
  测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试
</div>
<img src="data:image/jpeg;base64,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" alt="">

<script src="https://cdn.bootcss.com/jquery/2.2.4/jquery.js"></script>
<script>

  $(function() {
    var strs = '测试测试测试测试测试测试测试测试测试测试测试\n  _ \n测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试';
    strReset(strs,$('#cnnr-detail'),150);
    document.execCommand('saveAs');
  });

  //文字是否有换行符
   function strReset(str, location, rowLength) {
    var self = this;
    $(location).html("");
    if(str.search("\n") !== -1){
      var strArray = str.split(/\n/g);
      for(var i = 0; i < strArray.length; i++){
        var stri = strArray[i];
        self.moreRowsReset(stri, location, rowLength);
      }
    } else {
      self.moreRowsReset(str, location, rowLength);
    }
  };
  /**  2019年12月01日16:26:04
   *   针对旧文书处理,将 ireport xml 转换成 json 再将json 转换成文书模板所需配置
   *
   *
   */
  function moreRowsReset(str, location, rowLength) {
    // 大段文字整理，每行有多少个字符，并且循环append添加到设定的div下
    var self = this;
    var len = str.length;
    var lenByte = self.getByteLength(str);
    var k = rowLength;
    for(var i = 0; i < lenByte; i+=rowLength){
      //如果这一段文字为空的话，直接break
      if(str.substring(Math.ceil(i/2)).length === 0){
        break;
      }
      //判断是否为最后一行
      if(lenByte - i > rowLength){
        var strtemp = str.substring(Math.ceil((i+rowLength)/2));
        //var reg = new RegExp("[\\u4E00-\\u9FFF]+$","g");
        //var reg =/\p{P}/;
        var reg = /[\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/;
        //如果每行的字数有变化，就把行数变回到原来的字数
        if(k - rowLength === 4){
          rowLength+=4;
        }
        //判断行首是否为标点符号
        if(reg.test(strtemp.charAt(0))){
          rowLength-=4;
        }
        var strTemp = str.substring(Math.ceil(i/2),Math.ceil((i+rowLength)/2));

        $(location).append("<span class=\"cnnr-detail\">"+strTemp+"</span>");

      }else{
        var strTemp = str.substring(Math.ceil(i/2),Math.ceil((i+rowLength)/2));
        if(self.getByteLength(strTemp) <= 1){
          //break;
        };
        $(location).append("<span class=\"cnnr-detail\""+ " style='text-align: left; text-align-last: left;'" +">"+strTemp+"</span>");
      }
    }
  }
  //获取这段文字的字节长度
  function getByteLength(str) {
    var len = str.length;
    var blen = 0;
    for(i=0; i<len; i++) {
      if ((str.charCodeAt(i) & 0xff00) !== 0) {
        blen ++;
      }
      blen ++;
    }
    return blen;
  }

</script>
</body>
</html>
