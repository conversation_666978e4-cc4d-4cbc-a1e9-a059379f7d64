<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
</head>
<body style="overflow: hidden">
<div style="display: flex;height: 100vh">
  <div style="flex: 1">
    <label for="text">
    </label>
    <textarea id="text" style="width:100%; height:300px">

</textarea>
    <div style="text-align: center" >
      <input type="button" value="生成" id="gen" style="display: inline-block;height: 50px;width: 80px">
    </div>
  </div>
  <div style="flex: 1">
    <div>
    <span>

</span>
      <label>
<textarea id="filed" style="width:100%; height:300px">

</textarea>
      </label>
    </div>

    <div>
    <span>

</span>
      <label>
<textarea id="str" style="width:100%; height:300px">

</textarea>
      </label>
    </div>
  </div>
</div>

<script src="https://cdn.bootcss.com/jquery/2.2.4/jquery.js"></script>
<script>

  $(function() {
    $("#gen").on("click",function() {

      var str = $('#text').val();
      console.info(str);
      var array = new Set();
      str.replace(/{(.*?)}/g, function(world) {
        array.add(world.replace(/{|}/g, ''));
      });

      array = Array.from(array);
      var objData = [];
      array.forEach(function(value) {
        const d = {
          key: `uuidv4()`,
          textName: value,
          textValue: '',
          textType: 'string',
          elementStyle: {},
        };
        objData.push(d);
      });


      $('#filed').val(JSON.stringify(objData));
      console.info(objData);

      // while (temp = re.exec(str)) {
      //     array.add(temp[0])
      // }

      // console.info(array);

      var arr = str.split('+').map(item => {
        return item.replace(/\(.+\$F\{(.+?)\}.*/g, '<%$1%>').replace(/:".*/g, '');
      });
      var realArr = arr.join('').replace(/^"$/g, '');
      $('#str').val(realArr);
      console.info(realArr);


    });
  });




</script>
</body>
</html>
