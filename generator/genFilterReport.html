<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
</head>

<body style="overflow: hidden">
<div style="display: flex;height: 100vh">
  <div style="flex: 1">
    <label for="text"></label><textarea id="text" style="width:100%; height:300px"></textarea>
    <div style="text-align: center">
      <input type="button" value="生成" id="gen" style="display: inline-block;height: 50px;width: 80px">
    </div>
  </div>
  <div style="flex: 1">
    <div>

      <textarea id="filed" style="width:100%; height:300px"></textarea>

    </div>
  </div>
</div>

<script src="https://cdn.bootcss.com/jquery/2.2.4/jquery.js"></script>
<script>
  $(function() {

    // 取出xml 转json 的 格式result
    $('#gen').on('click', function() {
      var str = $('#text').val();
      var jsonData = {};
      try {
        jsonData = JSON.parse(str);
      } catch (e) {
        alert('json 数据格式不正确，请检查');
      }


      var texts = jsonData.jasperReport.detail.band;
      var texts2 = jsonData.jasperReport.title.band;
      var texts3 = jsonData.jasperReport.summary.band;
      var staticText = texts.staticText|| [];
      var textField = texts.textField || [];

      var staticText2 = texts2.staticText || [];
      var textField2 = texts2.textField || [];

      var staticText3 = texts3.staticText|| [];
      var textField3 = texts3.textField || [];

      var FATCOR = 1.3334;
      // var FATCOR = 1.28;
      var OFFSET = 8;
      var resultList = [];

      if(staticText2){
        staticText = staticText.concat(staticText2);
      }

      if(textField2){
        textField = textField.concat(textField2);
      }

      if(staticText3){
        staticText = staticText.concat(staticText3);
      }

      if(textField3){
        textField = textField.concat(textField3);
      }



      for (var i = 0; i < staticText.length; i++) {
        if(!staticText[i].box){
          continue;
        }
        var reportElement = staticText[i].reportElement;
        var textElement = staticText[i].textElement;
        var text = staticText[i].text;
        var staticTextItem = {
          key: 'uuidv4()',
          type: 'staticText', // staticText 静态文本 ，textField 动态字段，需要表单值
          textValue: text,
          textName: '',
          elementStyle: {
            fontFamily: textElement['font']['fontName'] + ',Heiti,serif',
            width: parseInt(reportElement['width']) * FATCOR + 'px',
            position: 'absolute',
            top: ((parseInt(reportElement['y'])-49) * FATCOR + OFFSET) + 'px',
            left: ((parseInt(reportElement['x'])) * FATCOR - OFFSET) + 'px',
            height: parseInt(reportElement['height']) * FATCOR + 'px',
            textAlign: textElement['textAlignment']&&textElement['textAlignment'].toLowerCase(),
            fontSize: textElement['font']['size'] + 'pt',
            lineHeight:parseInt(reportElement['height']) * FATCOR + 'px'
          },
        };
        resultList.push(staticTextItem);
      }

      //"$F{GAFJ}".match(/\{[^\}]+\}/)[0].replace(/\{([^)]*)\}/, '$1')
      for (var j = 0; j < textField.length; j++) {
        var reportElement = textField[j].reportElement;
        var textElement = textField[j].textElement;
        var filedName = textField[j].textFieldExpression.__cdata?textField[j].textFieldExpression.__cdata.replace(/\$F\{(.+?)}/, '$1'):textField[j].textFieldExpression.replace(/\$F\{(.+?)}/, '$1');
        var textFieldItem = {
          key: "uuidv4()",
          type: 'textField',
          textType: 'string',
          textValue: '',
          textName: filedName.toLowerCase(),
          elementStyle: {
            fontFamily: textElement['font']['fontName'] + ',Heiti,serif',
            width:parseInt(reportElement['width']) * FATCOR + 'px',
            position: 'absolute',
            top: ((parseInt(reportElement['y'])-49) * FATCOR + OFFSET) + 'px',
            left:((parseInt(reportElement['x'])) * FATCOR - OFFSET) + 'px',
            height: parseInt(reportElement['height']) * FATCOR + 'px',
            textAlign: textElement['textAlignment']&&textElement['textAlignment'].toLowerCase(),
            fontSize: textElement['font']['size'] + 'pt',
            lineHeight:parseInt(reportElement['height']) * FATCOR + 'px'
          },
        };
        resultList.push(textFieldItem);
      }

      $('#filed').val(JSON.stringify(resultList));
    });
  });
</script>
</body>

</html>
